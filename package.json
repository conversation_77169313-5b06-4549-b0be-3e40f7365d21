{"name": "construction", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@aws-sdk/client-s3": "^3.828.0", "@aws-sdk/s3-request-presigner": "^3.828.0", "@bprogress/next": "^3.2.12", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.41", "@react-email/render": "^1.1.2", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "@tiptap/extension-character-count": "^2.14.0", "@tiptap/extension-code-block-lowlight": "^2.25.0", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-highlight": "^2.14.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-list-item": "^2.14.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/prismjs": "^1.26.5", "@types/react-share": "^3.0.3", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^6.0.0", "date-fns": "^4.1.0", "js-beautify": "^1.15.4", "lowlight": "^3.3.0", "lucide-react": "^0.511.0", "next": "15.3.3", "next-auth": "^5.0.0-beta.28", "next-themes": "^0.4.6", "openai": "^5.3.0", "prettier": "^3.6.2", "prisma": "^6.8.2", "prismjs": "^1.30.0", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.57.0", "react-icons": "^5.5.0", "react-share": "^5.2.2", "react-syntax-highlighter": "^15.6.1", "resend": "^4.5.1", "sharp": "^0.34.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.63", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@types/bcryptjs": "^2.4.6", "@types/js-beautify": "^1.14.3", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "tailwindcss": "^4.1.8", "tsx": "^4.19.4", "tw-animate-css": "^1.3.3", "typescript": "^5"}, "prisma": {"seed": "tsx prisma/seed.ts"}}