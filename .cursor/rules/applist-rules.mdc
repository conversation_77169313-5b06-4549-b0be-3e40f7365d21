---
alwaysApply: false
---
# AppList Project Rules

## Naming Conventions

### Database & Prisma Schema
- **ALWAYS** use `snake_case` for all database fields and table names
- Boolean fields: `is_something` (NOT `isSomething`)
- Foreign keys: `user_id` (NOT `userId`)
- Timestamps: `created_at`, `updated_at` (NOT `createdAt`, `updatedAt`)
- Count fields: `email_count` (NOT `emailCount`)

```prisma
// ✅ CORRECT
model User {
  id                        String    @id @default(cuid())
  email_verified            DateTime?
  is_verified               Boolean   @default(false)
  verification_email_count  Int       @default(0)
  last_verification_sent    DateTime?
  created_at               DateTime  @default(now())
  updated_at               DateTime  @updatedAt
}

// ❌ WRONG
model User {
  id                      String    @id @default(cuid())
  emailVerified          DateTime?
  isVerified             Boolean   @default(false)
  verificationEmailCount Int       @default(0)
  lastVerificationSent   DateTime?
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @updatedAt
}
```

### Environment Variables
We have these in .env file:

```
DATABASE_URL
GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET
NEXT_PUBLIC_APP_URL="http://localhost:3000"
AUTH_URL="http://localhost:3000"
AUTH_SECRET=
ADMIN_EMAIL=
ADMIN_PASSWORD=
RESEND_API_KEY=
OPENAI_API_KEY=
R2_BUCKET_NAME=
R2_ACCOUNT_ID=
R2_ENDPOINT_URL=
R2_ACCESS_KEY_ID=
R2_SECRET_ACCESS_KEY=
```

### File Naming
- **ALWAYS** use `kebab-case` for file names
- React components: `auth-nav.tsx`, `reset-password.tsx`
- Utility files: `email-rate-limit.ts`, `auth-utils.ts`
- Hook files: `use-auth-data.ts`, `use-auth-store.ts`

```
✅ CORRECT:
- auth-nav.tsx
- reset-password.tsx
- use-auth-data.ts
- email-rate-limit.ts

❌ WRONG:
- AuthNav.tsx
- ResetPassword.tsx
- useAuthData.ts
- EmailRateLimit.ts
```

## Folder Structure Pattern

### Feature-Based Architecture
Follow the `/feature/auth` pattern for all new features:

```
feature/
  [feature-name]/
    actions/          # Server actions and external API calls
      *.ts
    components/       # React components specific to this feature
      *.tsx
    hooks/           # Custom hooks (React Query, custom logic)
      use-[feature]-data.ts    # API calls with React Query
    provider/        # React context providers
      *.tsx
    store/           # State management
      use-[feature]-store.ts   # Zustand stores
    utils/           # Utility functions
      *.ts
```

### Required Files for Each Feature
1. **`hooks/use-[feature]-data.ts`** - API calls with TanStack Query
2. **`store/use-[feature]-store.ts`** - Zustand state management
3. **`components/[feature].tsx`** - Main component
4. **`utils/[feature]-utils.ts`** - Utility functions

## API & Data Management

### React Query / TanStack Query Pattern
- **ALWAYS** use `useApiMutation` and `useApiQuery` from `@/feature/core/api/api-utils`
- **ALWAYS** disable automatic error toasts with `showErrorToast: false`
- **ALWAYS** handle errors in `onError` callback

```typescript
// ✅ CORRECT Pattern
export function useCreateSomething() {
  return useApiMutation(
    async (data: CreateRequest): Promise<CreateResponse> => {
      return apiPost("/api/something/create", data);
    },
    {
      showErrorToast: false,
      onSuccess: (data) => {
        // Handle success
      },
      onError: (error) => {
        // Handle error with toast
        toast.error(error.message);
      },
    }
  );
}
```

### Zustand Store Pattern
- **ALWAYS** separate state and actions
- **ALWAYS** provide selector hooks
- **ALWAYS** include reset functions

```typescript
// ✅ CORRECT Pattern
interface FeatureState {
  // State properties
  data: SomeData;
  isLoading: boolean;
  
  // Actions
  setData: (data: SomeData) => void;
  setIsLoading: (loading: boolean) => void;
  resetState: () => void;
}

export const useFeatureStore = create<FeatureState>((set) => ({
  data: initialData,
  isLoading: false,
  
  setData: (data) => set({ data }),
  setIsLoading: (loading) => set({ isLoading: loading }),
  resetState: () => set({ data: initialData, isLoading: false }),
}));

// Selector hooks
export const useFeatureState = () => {
  const { data, isLoading } = useFeatureStore();
  return { data, isLoading };
};

export const useFeatureActions = () => {
  const { setData, setIsLoading, resetState } = useFeatureStore();
  return { setData, setIsLoading, resetState };
};
```

## API Routes Structure

### API Folder Organization
```
app/api/
  [feature]/
    create/
      route.ts
    update/
      route.ts
    delete/
      route.ts
    [id]/
      route.ts
```

### API Response Pattern
- **ALWAYS** return consistent response format
- **ALWAYS** handle errors with proper HTTP status codes

```typescript
// ✅ CORRECT API Response Pattern
export async function POST(request: Request) {
  try {
    // Implementation
    return NextResponse.json({
      success: true,
      message: "Success message",
      data: result
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Error message" },
      { status: 400 }
    );
  }
}
```

## Styling & Design

### Color Usage
- **ALWAYS** use CSS variables from `globals.css`
- **PRIMARY COLORS**: Use `--color-primary` for main brand elements
- **SECONDARY COLORS**: Use `--color-secondary` for supporting elements

```css
/* ✅ CORRECT - Use CSS variables */
.button-primary {
  background: var(--color-primary);
  color: var(--color-primary-foreground);
}

/* ❌ WRONG - Don't use hardcoded colors */
.button-primary {
  background: #c77332;
  color: white;
}
```

### Tailwind Classes
```typescript
// ✅ CORRECT - Use design system classes
<Button className="bg-primary text-primary-foreground">
  Primary Button
</Button>

// ❌ WRONG - Don't use arbitrary values unnecessarily
<Button className="bg-[#c77332] text-white">
  Primary Button
</Button>
```

## TypeScript Patterns

### Interface Naming
- **ALWAYS** use descriptive names ending with appropriate suffix
- Request/Response interfaces: `CreateUserRequest`, `CreateUserResponse`
- Component props: `AuthNavProps`, `ResetPasswordProps`
- Store interfaces: `AuthState`, `FeatureState`

```typescript
// ✅ CORRECT
export interface CreateUserRequest {
  name: string;
  email: string;
  password: string;
}

export interface CreateUserResponse {
  success: boolean;
  message: string;
  user_id: string;
}

// ❌ WRONG
export interface User {
  name: string;
  email: string;
  password: string;
}
```

## Code Quality Standards

### Error Handling
- **NEVER** use `console.log` or `console.error` in production code
- **ALWAYS** use proper error handling with user-friendly messages
- **ALWAYS** use `toast` for user notifications

```typescript
// ✅ CORRECT
try {
  const result = await apiCall();
  toast.success("Operation successful");
} catch (error) {
  toast.error(error.message || "Operation failed");
}

// ❌ WRONG
try {
  const result = await apiCall();
  console.log("Success", result);
} catch (error) {
  console.error("Error:", error);
}
```

### Component Structure
- **ALWAYS** use functional components with TypeScript
- **ALWAYS** define props interface
- **ALWAYS** use proper imports organization

```typescript
// ✅ CORRECT Component Pattern
"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { useFeatureData } from "@/feature/[feature]/hooks/use-feature-data";

interface FeatureComponentProps {
  id: string;
  dict: any;
  lang: string;
}

export function FeatureComponent({ id, dict, lang }: FeatureComponentProps) {
  const { data, isLoading } = useFeatureData();
  
  return (
    <div className="p-4">
      {/* Component content */}
    </div>
  );
}
```

## Email Templates

### Email Structure Pattern
- **ALWAYS** use `@react-email/components`
- **ALWAYS** import colors from `./email-colors`
- **ALWAYS** follow responsive design patterns

```typescript
// ✅ CORRECT Email Template Pattern
import { brandColors, emailStyles } from "./email-colors";

export const FeatureEmailTemplate = ({ prop }: Props) => (
  <Html>
    <Head />
    <Preview>Email preview text</Preview>
    <Body style={main}>
      <Container style={container}>
        {/* Email content */}
      </Container>
    </Body>
  </Html>
);
```

## Translation & i18n

### Dictionary Usage
- **ALWAYS** pass `dict` and `lang` props to components
- **ALWAYS** provide fallback text
- **ALWAYS** use structured translation keys

```typescript
// ✅ CORRECT Translation Usage
<Button>
  {dict?.common?.save || "Save"}
</Button>

// Translation key structure
{
  "feature": {
    "action": "Action Text",
    "error": "Error Message"
  }
}
```

## Database Operations

### Prisma Patterns
- **ALWAYS** use transactions for related operations
- **ALWAYS** handle unique constraint violations
- **ALWAYS** use proper error handling

```typescript
// ✅ CORRECT Prisma Pattern
try {
  const result = await prisma.$transaction(async (tx) => {
    const user = await tx.user.create({
      data: {
        email,
        name,
        is_verified: false,
        created_at: new Date(),
      },
    });
    
    // Related operations
    return user;
  });
} catch (error) {
  if (error.code === 'P2002') {
    throw new Error("User already exists");
  }
  throw error;
}
```

## Tool Management Implementation Reference

### Complete Implementation Examples
The following implementations serve as **gold standards** for feature development in the AppList project:

#### 1. Authentication System (`/feature/auth`)
- Complete user authentication with email verification
- Password reset functionality with rate limiting
- Multi-language support with AI translation
- Admin role-based access control

#### 2. Tool Categories & Tags (`/feature/tool`)
**Complete CRUD implementation with the following structure:**

```
feature/tool/
  category/
    components/
      category-delete-dialog.tsx    # Confirmation dialog for deletion
      category-form-dialog.tsx      # Create/Edit form with validation
      category-management.tsx       # Main container component
      category-table.tsx           # Data table with search, filters, pagination
    hooks/
      use-tool-category-data.ts    # React Query hooks for API calls
    store/
      use-tool-category-store.ts   # Zustand state management
  tag/
    components/
      tag-delete-dialog.tsx        # Confirmation dialog for deletion
      tag-form-dialog.tsx          # Create/Edit form with validation  
      tag-management.tsx           # Main container component
      tag-table.tsx               # Data table with search, filters, pagination
    hooks/
      use-tool-tag-data.ts         # React Query hooks for API calls
    store/
      use-tool-tag-store.ts        # Zustand state management
```

**API Routes Implementation:**
```
app/api/tool/
  categories/
    route.ts                       # GET (list), POST (create)
    [id]/
      route.ts                     # GET (single), PUT (update), DELETE
  tags/
    route.ts                       # GET (list), POST (create)
    [id]/
      route.ts                     # GET (single), PUT (update), DELETE
```

**Admin Pages:**
```
app/(admin)/admin/tools/
  category/
    page.tsx                       # Categories management page
  tag/
    page.tsx                       # Tags management page
```

#### 3. Tool Management System (`/feature/tool`)
**Complete CRUD implementation for tools with advanced features:**

```
feature/tool/
  components/
    tool-add-form.tsx              # Comprehensive form for creating/editing tools
    tool-edit-form.tsx             # Edit wrapper component
    tool-delete-dialog.tsx         # Confirmation dialog for deletion
    tool-management.tsx            # Main container component
    tool-table.tsx                 # Data table with search, filters, pagination
    tool-filters.tsx               # Advanced filtering system
    tool-section.tsx               # Collapsible form sections
    tool-faq-manager.tsx           # FAQ management with drag-and-drop
    category-selector.tsx          # Multi-select category picker
    tag-selector.tsx               # Multi-select tag picker
  hooks/
    use-tool-data.ts               # Main tool CRUD operations with React Query
    use-tool-config.ts             # Tool configuration data (categories/tags)
    use-tool-faq-data.ts           # FAQ-specific operations
  store/
    use-tool-store.ts              # Main tool state management
    use-tool-faq-store.ts          # FAQ state management
```

**API Routes Implementation:**
```
app/api/tool/
  route.ts                         # GET (list with filters), POST (create)
  [id]/
    route.ts                       # GET (single), PUT (update), DELETE
  config/
    route.ts                       # GET (categories and tags for forms)
```

**Admin Pages:**
```
app/(admin)/admin/tools/
  page.tsx                         # Tools management page
  add/
    page.tsx                       # Add new tool page
  edit/
    [id]/
      page.tsx                     # Edit tool page
```

**Key Features Implemented for Tool Management:**

1. **Comprehensive Tool Properties:**
   - Basic information (name, website URL, logo, screenshots)
   - Tool type classification (SaaS, Mobile App, Desktop App, AI Model, Chrome Extension)
   - Multiple pricing models support (Free, Freemium, Paid, Subscription, etc.)
   - Status management (Pending, Approved, Rejected, Published, Draft, Archived)
   - Availability and publication controls
   - Social media and contact information

2. **Multi-language Content Support:**
   - Automatic AI translation using OpenAI GPT-4o-mini
   - Comprehensive content fields (descriptions, features, pricing, meta data)
   - Translation storage in separate `tool_translations` table
   - Language-aware API responses and UI components

3. **FAQ Management System:**
   - Drag-and-drop reordering with React Beautiful DnD
   - Multi-language FAQ translations
   - Maximum 10 FAQs per tool with progress tracking
   - Collapsible FAQ editor with validation
   - Automatic AI translation for FAQ content

4. **Advanced Category & Tag Selection:**
   - Multi-select category picker with SuperCategory grouping
   - Advanced tag selector with sorting and filtering
   - Real-time search and selection state management
   - Visual feedback with badges and selection counts

5. **Form Architecture:**
   - Modular, collapsible form sections
   - Comprehensive validation with Zod schemas
   - Auto-save functionality and draft management
   - Progress tracking and user guidance

6. **Database Schema Pattern for Tools:**
   ```prisma
   // Main tool entity with snake_case fields
   model Tool {
     id                String             @id @default(cuid())
     name              String
     slug              String             @unique
     website_url       String?
     logo_url          String?
     screenshot_urls   String[]           @default([])
     tool_type         ToolType           @default(SAAS)
     pricing_models    PricingModel[]     @default([FREE])
     is_available      Boolean            @default(true)
     added_date        DateTime           @default(now())
     status            ToolStatus         @default(PENDING)
     is_published      Boolean            @default(false)
     category_ids      Int[]              @default([])
     tag_ids           Int[]              @default([])
     twitter_url       String?
     facebook_url      String?
     linkedin_url      String?
     github_url        String?
     contact_email     String?
     support_email     String?
     created_at        DateTime           @default(now())
     updated_at        DateTime           @updatedAt
     
     translations      ToolTranslation[]
     reviews           ToolReview[]
     faqs              ToolFAQ[]
     highlights        ToolHighlight[]
     featured          ToolFeatured[]
     analytics         ToolAnalytics[]
     @@map("tools")
   }
   
   // Separate translations table
   model ToolTranslation {
     id                String   @id @default(cuid())
     tool_id           String
     language_code     String
     name              String?
     short_description String?
     introduction      String?  @db.Text
     what_is_it        String?  @db.Text
     how_to_use        String?  @db.Text
     full_feature      String?  @db.Text
     short_feature     String?  @db.Text
     pricing           String?  @db.Text
     meta_title        String?
     meta_description  String?
     created_at        DateTime @default(now())
     updated_at        DateTime @updatedAt
     
     tool Tool @relation(fields: [tool_id], references: [id], onDelete: Cascade)
     @@unique([tool_id, language_code])
     @@map("tool_translations")
   }
   
   // FAQ system
   model ToolFAQ {
     id           String   @id @default(cuid())
     tool_id      String
     order        Int      @default(0)
     is_active    Boolean  @default(true)
     created_at   DateTime @default(now())
     updated_at   DateTime @updatedAt
     
     tool         Tool                  @relation(fields: [tool_id], references: [id], onDelete: Cascade)
     translations ToolFAQTranslation[]
     @@map("tool_faqs")
   }
   ```

**Key Features Implemented for Categories & Tags:**

1. **SuperCategory System** - Categories belong to one of five super categories:
   - `WRITING` - Text generation, editing, and writing tools
   - `IMAGE_GENERATION` - AI image creation and editing tools
   - `AUDIO` - Audio processing, generation, and editing tools
   - `VIDEO_GENERATION` - Video creation and editing tools
   - `SOCIAL_MEDIA` - Social media management and content tools

2. **Multi-language Support:**
   - Automatic AI translation using OpenAI GPT-4o-mini
   - Translation storage in separate tables (`tool_category_translations`, `tool_tag_translations`)
   - Language-aware display in UI components

3. **Advanced Features:**
   - Real-time search with debouncing
   - Status filtering (Active/Inactive)
   - Language selection for display
   - Pagination with proper controls
   - Auto-slug generation from names
   - Color support for visual indicators
   - Icon URL support for categories
   - Form validation and error handling

4. **Database Schema Pattern:**
   ```prisma
   // Main entity with snake_case fields
   model ToolCategory {
     id             Int           @id @default(autoincrement())
     name           String
     slug           String        @unique
     super_category SuperCategory
     icon_url       String?
     color          String?
     is_active      Boolean       @default(true)
     created_at     DateTime      @default(now())
     updated_at     DateTime      @updatedAt
     
     translations ToolCategoryTranslation[]
     @@map("tool_categories")
   }
   
   // Separate translations table
   model ToolCategoryTranslation {
     id            Int          @id @default(autoincrement())
     category_id   Int
     language_code String
     name          String
     description   String?      @db.Text
     created_at    DateTime     @default(now())
     updated_at    DateTime     @updatedAt
     
     category ToolCategory @relation(fields: [category_id], references: [id], onDelete: Cascade)
     @@unique([category_id, language_code])
     @@map("tool_category_translations")
   }
   ```

7. **Advanced Filtering & Search:**
   - Real-time search across tool names and descriptions
   - Multi-filter support (status, tool type, pricing model, publication state)
   - Language-aware filtering and display
   - Pagination with proper state management
   - Filter persistence and reset functionality

8. **TypeScript Integration for Tools:**
   - Complete type definitions in `types/tool-api.ts`
   - Request/Response interfaces for all CRUD operations
   - Enum definitions for ToolType, PricingModel, ToolStatus
   - FAQ-specific type definitions
   - Proper type safety throughout the application

9. **UI/UX Features for Tool Management:**
   - Modern, responsive design using Shadcn/ui components
   - Collapsible form sections for better organization
   - Loading skeletons and progress indicators
   - Toast notifications for user feedback
   - Confirmation dialogs for destructive actions
   - Drag-and-drop FAQ reordering
   - Multi-select components with advanced search
   - Badge components for status, tool type, and pricing display
   - Row numbering with pagination continuity

**Usage Example for Tools:**
```typescript
// ✅ CORRECT - Following the implemented pattern
import { useGetTools, useCreateTool } from "@/feature/tool/hooks/use-tool-data";
import { useToolFiltersActions } from "@/feature/tool/store/use-tool-store";
import { useToolConfig } from "@/feature/tool/hooks/use-tool-config";

export function ToolComponent() {
  const { searchQuery, statusFilter, currentPage } = useToolFiltersState();
  const { setSearchQuery, setCurrentPage } = useToolFiltersActions();
  const { data: toolsData, isLoading } = useGetTools({ 
    search: searchQuery, 
    status: statusFilter, 
    page: currentPage 
  });
  const { data: toolConfig } = useToolConfig();
  const createTool = useCreateTool();
  
  return (
    <div>
      {/* Implementation following the established patterns */}
    </div>
  );
}
```

5. **TypeScript Integration for Categories & Tags:**
   - Complete type definitions in `types/tool-api.ts`
   - Request/Response interfaces for all operations
   - Enum definitions for SuperCategory
   - Proper type safety throughout the application

6. **UI/UX Features for Categories & Tags:**
   - Modern, responsive design using Shadcn/ui components
   - Loading skeletons for better perceived performance
   - Toast notifications for user feedback
   - Confirmation dialogs for destructive actions
   - Row numbering with pagination continuity
   - Truncated descriptions to prevent table overflow
   - Badge components for status and super category display

**Usage Example for Categories & Tags:**
```typescript
// ✅ CORRECT - Following the implemented pattern
import { useGetCategories, useCreateCategory } from "@/feature/tool/category/hooks/use-tool-category-data";
import { useCategoryFormActions } from "@/feature/tool/category/store/use-tool-category-store";

export function CategoryComponent() {
  const { data, isLoading } = useGetCategories({ page: 1, limit: 10 });
  const { openCreateDialog } = useCategoryFormActions();
  const createCategory = useCreateCategory();
  
  return (
    <div>
      {/* Implementation following the established patterns */}
    </div>
  );
}
```

#### 4. Blog Management System (`/feature/blog`)
**Complete CRUD implementation for blog posts with advanced content management features:**

```
feature/blog/
  post/
    components/
      blog-post-form.tsx                  # Main blog post form with rich editor
      blog-post-management.tsx            # Management container
      blog-post-table.tsx                 # Data table with filters/pagination
      blog-post-filters.tsx               # Advanced filtering system
      blog-post-delete-dialog.tsx         # Delete confirmation dialog
      blog-rich-editor.tsx                # Rich text editor (TipTap-based)
      blog-media-dialog.tsx               # Media library integration
      blog-category-tag-dialog.tsx        # Category/tag selection
      blog-author-dialog.tsx              # Author selection dialog
      featured-image-upload.tsx           # Featured image upload
      date-time-picker.tsx                # Date/time picker for scheduling
    hooks/
      use-blog-post-data.ts               # Blog post CRUD operations with React Query
      use-blog-config.ts                  # Blog configuration data
    store/
      use-blog-post-store.ts              # Blog post state management
      use-rich-editor-store.ts            # Rich editor state
    styles/
      blog-rich-editor.css                # Rich editor custom styles
  category/
    components/
      blog-category-management.tsx        # Category management container
      blog-category-table.tsx             # Category table with hierarchy
      blog-category-form-dialog.tsx       # Category form with parent selection
      blog-category-delete-dialog.tsx     # Category delete confirmation
    hooks/
      use-blog-category-data.ts           # Category API hooks
    store/
      use-blog-category-store.ts          # Category state management
  tag/
    components/
      blog-tag-management.tsx             # Tag management container
      blog-tag-table.tsx                  # Tag table with search/pagination
      blog-tag-form-dialog.tsx            # Tag form dialog
      blog-tag-delete-dialog.tsx          # Tag delete confirmation
    hooks/
      use-blog-tag-data.ts                # Tag API hooks
    store/
      use-blog-tag-store.ts               # Tag state management
```

**API Routes Implementation:**
```
app/api/blog/
  config/
    route.ts                              # Blog configuration endpoint
  post/
    route.ts                              # GET (list with filters), POST (create)
    [id]/
      route.ts                            # GET (single), PUT (update), DELETE
  categories/
    route.ts                              # GET (list), POST (create)
    [id]/
      route.ts                            # GET (single), PUT (update), DELETE
  tags/
    route.ts                              # GET (list), POST (create)
    [id]/
      route.ts                            # GET (single), PUT (update), DELETE
```

**Admin Pages:**
```
app/(admin)/admin/blog/
  page.tsx                                # Main blog management page
  new/
    page.tsx                              # Create new blog post page
  edit/
    [id]/
      page.tsx                            # Edit blog post page
  category/
    page.tsx                              # Category management page
  tag/
    page.tsx                              # Tag management page
```

**Key Features Implemented for Blog Management:**

1. **Rich Content Management:**
   - TipTap-based rich text editor with advanced formatting
   - Media library integration with drag-and-drop
   - Featured image upload and management
   - SEO fields (meta title, description, slug)
   - Content scheduling and publishing workflow

2. **Blog Post Features:**
   - Multiple status states (Draft, Published, Scheduled, Archived, Private)
   - Author assignment with role-based permissions
   - Category and tag associations
   - Excerpt and full content support
   - Publishing date control and scheduling

3. **Category Management:**
   - Hierarchical category structure with parent-child relationships
   - Nested category display and management
   - Category-based filtering and organization
   - Slug generation and SEO optimization

4. **Tag System:**
   - Tag creation and management
   - Tag-based content organization
   - Tag filtering and search functionality
   - Tag association with multiple posts

5. **Advanced Filtering & Search:**
   - Multi-filter support (status, author, category, tags, publication state)
   - Real-time search across titles and content
   - Date range filtering and sorting
   - Pagination with state management

6. **Database Schema Pattern for Blog:**
   ```prisma
   // Blog post entity with snake_case fields
   model BlogPost {
     id                String     @id @default(cuid())
     title             String
     slug              String     @unique
     excerpt           String?    @db.Text
     content           String     @db.Text
     status            BlogStatus @default(DRAFT)
     meta_title        String?
     meta_description  String?
     featured_image_url String?
     author_id         String
     category_ids      Int[]      @default([])
     tag_ids          Int[]      @default([])
     published_at      DateTime?
     created_at        DateTime   @default(now())
     updated_at        DateTime   @updatedAt
     
     author    User          @relation(fields: [author_id], references: [id])
     comments  BlogComment[]
     @@map("blog_posts")
   }
   
   // Hierarchical category structure
   model BlogCategory {
     id          Int             @id @default(autoincrement())
     name        String
     slug        String          @unique
     description String?         @db.Text
     parent_id   Int?
     created_at  DateTime        @default(now())
     updated_at  DateTime        @updatedAt
     
     parent      BlogCategory?   @relation("CategoryHierarchy", fields: [parent_id], references: [id])
     children    BlogCategory[]  @relation("CategoryHierarchy")
     @@map("blog_categories")
   }
   
   // Blog tag system
   model BlogTag {
     id          Int      @id @default(autoincrement())
     name        String
     slug        String   @unique
     description String?  @db.Text
     created_at  DateTime @default(now())
     updated_at  DateTime @updatedAt
     @@map("blog_tags")
   }
   
   // Comment system with nested replies
   model BlogComment {
     id           String            @id @default(cuid())
     post_id      String
     author_id    String?
     author_name  String
     author_email String
     content      String            @db.Text
     status       BlogCommentStatus @default(PENDING)
     parent_id    String?
     is_approved  Boolean           @default(false)
     created_at   DateTime          @default(now())
     updated_at   DateTime          @updatedAt
     
     post     BlogPost      @relation(fields: [post_id], references: [id])
     author   User?         @relation(fields: [author_id], references: [id])
     parent   BlogComment?  @relation("CommentThread", fields: [parent_id], references: [id])
     replies  BlogComment[] @relation("CommentThread")
     @@map("blog_comments")
   }
   ```

7. **TypeScript Integration for Blog:**
   - Complete type definitions in `types/blog-api.ts`
   - BlogStatus, BlogCommentStatus enums
   - Request/Response interfaces for all CRUD operations
   - Hierarchical category type definitions
   - Comment system type definitions

8. **UI/UX Features for Blog Management:**
   - Rich text editor with toolbar and formatting options
   - Media library integration with file management
   - Category hierarchy display with indentation
   - Tag input with autocomplete and suggestions
   - Author selection with role filtering
   - Status badges and publication indicators
   - Date/time picker for scheduling posts
   - Preview and draft functionality

9. **Content Management Features:**
   - Auto-save functionality for drafts
   - Slug generation from titles
   - SEO meta field management
   - Featured image selection and cropping
   - Content word count and reading time
   - Revision history and version control

**Usage Example for Blog:**
```typescript
// ✅ CORRECT - Following the implemented pattern
import { useGetBlogPosts, useCreateBlogPost } from "@/feature/blog/post/hooks/use-blog-post-data";
import { useBlogPostFiltersActions } from "@/feature/blog/post/store/use-blog-post-store";
import { useGetBlogConfig } from "@/feature/blog/hooks/use-blog-config";

export function BlogComponent() {
  const { searchQuery, statusFilter, currentPage } = useBlogPostFiltersState();
  const { setSearchQuery, setCurrentPage } = useBlogPostFiltersActions();
  const { data: postsData, isLoading } = useGetBlogPosts({ 
    search: searchQuery, 
    status: statusFilter, 
    page: currentPage 
  });
  const { data: blogConfig } = useGetBlogConfig();
  const createPost = useCreateBlogPost();
  
  return (
    <div>
      {/* Implementation following the established patterns */}
    </div>
  );
}
```

### Implementation Checklist
When creating new features, ensure you have:

**Core Requirements:**
- [ ] **Database Schema** - Models with proper `snake_case` naming
- [ ] **API Routes** - RESTful endpoints in `/app/api/[feature]/`
- [ ] **TypeScript Types** - Complete interfaces in `/types/`
- [ ] **React Query Hooks** - Data fetching in `/hooks/use-[feature]-data.ts`
- [ ] **Zustand Store** - State management in `/store/use-[feature]-store.ts`
- [ ] **UI Components** - Form, table, and dialog components
- [ ] **Admin Pages** - Management interfaces in `/app/(admin)/`
- [ ] **Translation Support** - AI-powered multi-language content
- [ ] **Error Handling** - Proper validation and user feedback
- [ ] **Responsive Design** - Modern UI with Shadcn/ui components

**Advanced Features (For Complex Entities like Tools & Blog):**
- [ ] **Multi-language Translations** - Separate translation tables
- [ ] **Advanced Filtering** - Search, status, type, and custom filters
- [ ] **Form Sections** - Collapsible, organized form architecture
- [ ] **File Management** - Multiple URLs/files support (screenshots, etc.)
- [ ] **Related Entity Management** - FAQ, reviews, analytics sub-systems
- [ ] **Drag-and-Drop** - For reordering and enhanced UX
- [ ] **Multi-select Components** - Advanced selectors for categories/tags
- [ ] **State Persistence** - Filter and form state management
- [ ] **Background Processing** - AI translations and async operations
- [ ] **Validation Schemas** - Comprehensive Zod validation
- [ ] **Progress Tracking** - User guidance and completion indicators
- [ ] **Rich Content Editor** - TipTap-based rich text editing
- [ ] **Media Integration** - Built-in media library and file management
- [ ] **Hierarchical Categories** - Nested category structures
- [ ] **Comment System** - Nested comments with moderation
- [ ] **SEO Features** - Meta titles, descriptions, and slug management
- [ ] **Scheduling System** - Content scheduling and publishing workflow

---
**Remember**: These rules ensure consistency, maintainability, and scalability across the entire AppList project. Always refer to the existing `/feature/auth`, `/feature/tool` (categories/tags), `/feature/tool` (main tool system), and **`/feature/blog` (blog management system)** implementations as the gold standard for these patterns. 
