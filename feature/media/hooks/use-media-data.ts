"use client";

import { useApiMutation, useApiQuery } from "@/feature/core/api/api-utils";
import { toast } from "sonner";
import {
  ListMediaRequest,
  ListMediaResponse,
  UploadMediaRequest,
  UploadMediaResponse,
  CreateFolderRequest,
  CreateFolderResponse,
  DeleteMediaRequest,
  DeleteMediaResponse,
  CreateMediaMetadataRequest,
  CreateMediaMetadataResponse,
  UpdateMediaMetadataRequest,
  UpdateMediaMetadataResponse,
  MediaMetadata,
} from "@/types/media-api";

// Helper function to create FormData for file upload
function createUploadFormData(file: File, folderPath?: string): FormData {
  const formData = new FormData();
  formData.append("file", file);
  if (folderPath) {
    formData.append("folder_path", folderPath);
  }
  return formData;
}

// Get media list
export function useGetMedia(params?: ListMediaRequest) {
  const queryParams = new URLSearchParams();
  if (params?.path) queryParams.append("path", params.path);
  if (params?.limit) queryParams.append("limit", params.limit.toString());
  if (params?.continuation_token)
    queryParams.append("continuation_token", params.continuation_token);

  const queryString = queryParams.toString();
  const url = `/api/media${queryString ? `?${queryString}` : ""}`;

  return useApiQuery<ListMediaResponse>(
    ["media", JSON.stringify(params)],
    async () => {
      const response = await fetch(url);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fetch media");
      }
      return response.json();
    },
    {
      staleTime: 30000, // Cache for 30 seconds
    }
  );
}

// Upload media file
export function useUploadMedia() {
  return useApiMutation(
    async (data: {
      file: File;
      folder_path?: string;
    }): Promise<UploadMediaResponse> => {
      const formData = createUploadFormData(data.file, data.folder_path);

      const response = await fetch("/api/media/upload", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to upload file");
      }

      return response.json();
    },
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success(data.message || "File uploaded successfully");
      },
      invalidateQueries: ["media"],
      onError: (error) => {
        toast.error(error.message || "Failed to upload file");
      },
    }
  );
}

// Create folder
export function useCreateFolder() {
  return useApiMutation(
    async (data: CreateFolderRequest): Promise<CreateFolderResponse> => {
      const response = await fetch("/api/media/folder", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create folder");
      }

      return response.json();
    },
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success(data.message || "Folder created successfully");
      },
      invalidateQueries: ["media"],
      onError: (error) => {
        toast.error(error.message || "Failed to create folder");
      },
    }
  );
}

// Delete media items
export function useDeleteMedia() {
  return useApiMutation(
    async (data: DeleteMediaRequest): Promise<DeleteMediaResponse> => {
      const response = await fetch("/api/media/delete", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete items");
      }

      return response.json();
    },
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success(data.message || "Items deleted successfully");
      },
      invalidateQueries: ["media"],
      onError: (error) => {
        toast.error(error.message || "Failed to delete items");
      },
    }
  );
}

// Upload multiple files
export function useUploadMultipleFiles() {
  return useApiMutation(
    async (data: {
      files: File[];
      folder_path?: string;
    }): Promise<UploadMediaResponse[]> => {
      const uploadPromises = data.files.map(async (file) => {
        const formData = createUploadFormData(file, data.folder_path);

        const response = await fetch("/api/media/upload", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(`Failed to upload ${file.name}: ${error.error}`);
        }

        return response.json();
      });

      return Promise.all(uploadPromises);
    },
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success(`Successfully uploaded ${data.length} files`);
      },
      invalidateQueries: ["media"],
      onError: (error) => {
        toast.error(error.message || "Failed to upload files");
      },
    }
  );
}

// Get media metadata
export function useGetMediaMetadata(fileKey: string) {
  return useApiQuery<{
    success: boolean;
    message: string;
    data: MediaMetadata;
  }>(
    ["media-metadata", fileKey],
    async () => {
      const response = await fetch(
        `/api/media/metadata?file_key=${encodeURIComponent(fileKey)}`
      );
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fetch media metadata");
      }
      return response.json();
    },
    {
      enabled: !!fileKey,
      staleTime: 60000, // Cache for 1 minute
    }
  );
}

// Create media metadata
export function useCreateMediaMetadata() {
  return useApiMutation(
    async (
      data: CreateMediaMetadataRequest
    ): Promise<CreateMediaMetadataResponse> => {
      const response = await fetch("/api/media/metadata", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create media metadata");
      }

      return response.json();
    },
    {
      showErrorToast: false,
      onSuccess: (data) => {
        // toast.success("Media metadata created successfully");
      },
      invalidateQueries: ["media", "media-metadata"],
      onError: (error) => {
        toast.error(error.message || "Failed to create media metadata");
      },
    }
  );
}

// Update media metadata
export function useUpdateMediaMetadata() {
  return useApiMutation(
    async (
      data: UpdateMediaMetadataRequest
    ): Promise<UpdateMediaMetadataResponse> => {
      const response = await fetch("/api/media/metadata", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update media metadata");
      }

      return response.json();
    },
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success("Media metadata updated successfully");
      },
      invalidateQueries: ["media", "media-metadata"],
      onError: (error) => {
        toast.error(error.message || "Failed to update media metadata");
      },
    }
  );
}
