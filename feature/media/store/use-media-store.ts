"use client";

import { create } from "zustand";
import { <PERSON><PERSON><PERSON>, MediaFilter, MediaUploadProgress } from "@/types/media-api";

interface MediaState {
  // Navigation state
  current_path: string;
  navigation_history: string[];

  // Selection state
  selected_items: string[];

  // View state
  view_mode: "grid" | "list";
  filter: MediaFilter;

  // Upload state
  upload_progress: MediaUploadProgress[];
  is_uploading: boolean;

  // Dialog states
  is_folder_dialog_open: boolean;
  is_upload_dialog_open: boolean;
  is_delete_dialog_open: boolean;

  // Actions - Navigation
  setCurrentPath: (path: string) => void;
  navigateToPath: (path: string) => void;
  navigateBack: () => void;
  goToParentFolder: () => void;

  // Actions - Selection
  selectItem: (key: string) => void;
  deselectItem: (key: string) => void;
  selectAll: (items: MediaItem[]) => void;
  clearSelection: () => void;
  toggleItemSelection: (key: string) => void;

  // Actions - View
  setViewMode: (mode: "grid" | "list") => void;
  setFilter: (filter: Partial<MediaFilter>) => void;
  resetFilter: () => void;

  // Actions - Upload
  addUploadProgress: (progress: MediaUploadProgress) => void;
  updateUploadProgress: (
    fileName: string,
    progress: Partial<MediaUploadProgress>
  ) => void;
  removeUploadProgress: (fileName: string) => void;
  clearUploadProgress: () => void;
  setIsUploading: (uploading: boolean) => void;

  // Actions - Dialogs
  openFolderDialog: () => void;
  closeFolderDialog: () => void;
  openUploadDialog: () => void;
  closeUploadDialog: () => void;
  openDeleteDialog: () => void;
  closeDeleteDialog: () => void;

  // Action - Reset
  resetState: () => void;
}

const initialState = {
  current_path: "",
  navigation_history: [],
  selected_items: [],
  view_mode: "grid" as const,
  filter: {
    type: "all" as const,
    search: "",
    sort_by: "name" as const,
    sort_order: "asc" as const,
  },
  upload_progress: [],
  is_uploading: false,
  is_folder_dialog_open: false,
  is_upload_dialog_open: false,
  is_delete_dialog_open: false,
};

export const useMediaStore = create<MediaState>((set, get) => ({
  ...initialState,

  // Navigation actions
  setCurrentPath: (path: string) => set({ current_path: path }),

  navigateToPath: (path: string) =>
    set((state) => ({
      current_path: path,
      navigation_history: [...state.navigation_history, state.current_path],
      selected_items: [], // Clear selection when navigating
    })),

  navigateBack: () =>
    set((state) => {
      const history = [...state.navigation_history];
      const previousPath = history.pop() || "";
      return {
        current_path: previousPath,
        navigation_history: history,
        selected_items: [],
      };
    }),

  goToParentFolder: () =>
    set((state) => {
      const pathParts = state.current_path.split("/").filter(Boolean);
      pathParts.pop(); // Remove last part
      const parentPath = pathParts.join("/");
      return {
        current_path: parentPath,
        navigation_history: [...state.navigation_history, state.current_path],
        selected_items: [],
      };
    }),

  // Selection actions
  selectItem: (key: string) =>
    set((state) => ({
      selected_items: [...state.selected_items, key],
    })),

  deselectItem: (key: string) =>
    set((state) => ({
      selected_items: state.selected_items.filter((item) => item !== key),
    })),

  selectAll: (items: MediaItem[]) =>
    set({
      selected_items: items.map((item) => item.key),
    }),

  clearSelection: () => set({ selected_items: [] }),

  toggleItemSelection: (key: string) =>
    set((state) => ({
      selected_items: state.selected_items.includes(key)
        ? state.selected_items.filter((item) => item !== key)
        : [...state.selected_items, key],
    })),

  // View actions
  setViewMode: (mode: "grid" | "list") => set({ view_mode: mode }),

  setFilter: (filter: Partial<MediaFilter>) =>
    set((state) => ({
      filter: { ...state.filter, ...filter },
    })),

  resetFilter: () =>
    set({
      filter: {
        type: "all",
        search: "",
        sort_by: "name",
        sort_order: "asc",
      },
    }),

  // Upload actions
  addUploadProgress: (progress: MediaUploadProgress) =>
    set((state) => ({
      upload_progress: [...state.upload_progress, progress],
    })),

  updateUploadProgress: (
    fileName: string,
    progress: Partial<MediaUploadProgress>
  ) =>
    set((state) => ({
      upload_progress: state.upload_progress.map((item) =>
        item.file_name === fileName ? { ...item, ...progress } : item
      ),
    })),

  removeUploadProgress: (fileName: string) =>
    set((state) => ({
      upload_progress: state.upload_progress.filter(
        (item) => item.file_name !== fileName
      ),
    })),

  clearUploadProgress: () => set({ upload_progress: [] }),

  setIsUploading: (uploading: boolean) => set({ is_uploading: uploading }),

  // Dialog actions
  openFolderDialog: () => set({ is_folder_dialog_open: true }),

  closeFolderDialog: () => set({ is_folder_dialog_open: false }),

  openUploadDialog: () => set({ is_upload_dialog_open: true }),

  closeUploadDialog: () => set({ is_upload_dialog_open: false }),

  openDeleteDialog: () => set({ is_delete_dialog_open: true }),

  closeDeleteDialog: () => set({ is_delete_dialog_open: false }),

  // Reset action
  resetState: () => set(initialState),
}));

// Selector hooks
export const useMediaNavigation = () => {
  const {
    current_path,
    navigation_history,
    setCurrentPath,
    navigateToPath,
    navigateBack,
    goToParentFolder,
  } = useMediaStore();

  return {
    current_path,
    navigation_history,
    setCurrentPath,
    navigateToPath,
    navigateBack,
    goToParentFolder,
    canGoBack: navigation_history.length > 0,
  };
};

export const useMediaSelection = () => {
  const {
    selected_items,
    selectItem,
    deselectItem,
    selectAll,
    clearSelection,
    toggleItemSelection,
  } = useMediaStore();

  return {
    selected_items,
    selectItem,
    deselectItem,
    selectAll,
    clearSelection,
    toggleItemSelection,
    hasSelection: selected_items.length > 0,
    selectionCount: selected_items.length,
  };
};

export const useMediaView = () => {
  const { view_mode, filter, setViewMode, setFilter, resetFilter } =
    useMediaStore();

  return {
    view_mode,
    filter,
    setViewMode,
    setFilter,
    resetFilter,
  };
};

export const useMediaUpload = () => {
  const {
    upload_progress,
    is_uploading,
    addUploadProgress,
    updateUploadProgress,
    removeUploadProgress,
    clearUploadProgress,
    setIsUploading,
  } = useMediaStore();

  return {
    upload_progress,
    is_uploading,
    addUploadProgress,
    updateUploadProgress,
    removeUploadProgress,
    clearUploadProgress,
    setIsUploading,
  };
};

export const useMediaDialogs = () => {
  const {
    is_folder_dialog_open,
    is_upload_dialog_open,
    is_delete_dialog_open,
    openFolderDialog,
    closeFolderDialog,
    openUploadDialog,
    closeUploadDialog,
    openDeleteDialog,
    closeDeleteDialog,
  } = useMediaStore();

  return {
    is_folder_dialog_open,
    is_upload_dialog_open,
    is_delete_dialog_open,
    openFolderDialog,
    closeFolderDialog,
    openUploadDialog,
    closeUploadDialog,
    openDeleteDialog,
    closeDeleteDialog,
  };
};
