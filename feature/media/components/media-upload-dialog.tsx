"use client";

import React, { useState, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogDescription,
  CustomDialogFooter,
} from "@/components/custom/custom-dialog";
import { Upload, X, File, Image, Video, Music, FileText } from "lucide-react";
import {
  useMediaDialogs,
  useMediaUpload,
  useMediaNavigation,
} from "../store/use-media-store";
import { useUploadMultipleFiles } from "../hooks/use-media-data";
import { cn } from "@/lib/utils";

interface MediaUploadDialogProps {
  dict?: any;
}

const getFileIcon = (fileName: string) => {
  const extension = fileName.split(".").pop()?.toLowerCase() || "";

  const imageExtensions = [
    "jpg",
    "jpeg",
    "png",
    "gif",
    "webp",
    "svg",
    "bmp",
    "ico",
  ];
  const videoExtensions = [
    "mp4",
    "avi",
    "mov",
    "wmv",
    "flv",
    "webm",
    "mkv",
    "m4v",
  ];
  const audioExtensions = ["mp3", "wav", "flac", "aac", "ogg", "wma", "m4a"];
  const documentExtensions = [
    "pdf",
    "doc",
    "docx",
    "txt",
    "rtf",
    "odt",
    "xls",
    "xlsx",
    "ppt",
    "pptx",
  ];

  if (imageExtensions.includes(extension)) return <Image className="h-4 w-4" />;
  if (videoExtensions.includes(extension)) return <Video className="h-4 w-4" />;
  if (audioExtensions.includes(extension)) return <Music className="h-4 w-4" />;
  if (documentExtensions.includes(extension))
    return <FileText className="h-4 w-4" />;

  return <File className="h-4 w-4" />;
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export function MediaUploadDialog({ dict }: MediaUploadDialogProps) {
  const { is_upload_dialog_open, closeUploadDialog } = useMediaDialogs();
  const { current_path } = useMediaNavigation();
  const { upload_progress, is_uploading, setIsUploading, clearUploadProgress } =
    useMediaUpload();

  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);

  const uploadMutation = useUploadMultipleFiles();

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);
    const validFiles = fileArray.filter(
      (file) => file.size <= 50 * 1024 * 1024
    ); // 50MB limit

    if (validFiles.length < fileArray.length) {
      // Some files were too large
      const oversizedCount = fileArray.length - validFiles.length;
      alert(
        `${oversizedCount} file(s) exceeded the 50MB limit and were not selected.`
      );
    }

    setSelectedFiles((prev) => [...prev, ...validFiles]);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
      handleFileSelect(e.dataTransfer.files);
    },
    [handleFileSelect]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const removeFile = useCallback((index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const handleUpload = useCallback(async () => {
    if (selectedFiles.length === 0) return;

    setIsUploading(true);

    try {
      await uploadMutation.mutateAsync({
        files: selectedFiles,
        folder_path: current_path,
      });

      // Success - close dialog and reset
      setSelectedFiles([]);
      closeUploadDialog();
    } catch (error) {
      console.error("Upload failed:", error);
    } finally {
      setIsUploading(false);
      clearUploadProgress();
    }
  }, [
    selectedFiles,
    current_path,
    uploadMutation,
    setIsUploading,
    closeUploadDialog,
    clearUploadProgress,
  ]);

  const handleClose = useCallback(() => {
    if (!is_uploading) {
      setSelectedFiles([]);
      closeUploadDialog();
    }
  }, [is_uploading, closeUploadDialog]);

  const totalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);

  return (
    <CustomDialog
      open={is_upload_dialog_open}
      onOpenChange={(open) => !open && handleClose()}
    >
      <CustomDialogContent className="max-w-2xl shadow-none p-4">
        <CustomDialogHeader>
          <CustomDialogTitle>
            {dict?.media?.uploadFiles || "Upload Files"}
          </CustomDialogTitle>
          <CustomDialogDescription>
            {dict?.media?.uploadDescription ||
              "Select files to upload to the current folder. Maximum file size is 50MB."}
          </CustomDialogDescription>
        </CustomDialogHeader>

        <div className="space-y-4">
          {/* File Drop Zone */}
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
              isDragOver
                ? "border-primary bg-primary/5"
                : "border-muted-foreground/25 hover:border-primary/50"
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <div className="space-y-2">
              <p className="text-lg font-medium">
                {dict?.media?.dropFiles || "Drop files here"}
              </p>
              <p className="text-sm text-muted-foreground">
                {dict?.media?.orClickToSelect || "or click to select files"}
              </p>
              <Input
                type="file"
                multiple
                className="hidden"
                id="file-upload"
                onChange={(e) => handleFileSelect(e.target.files)}
                disabled={is_uploading}
              />
              <Button
                variant="outline"
                onClick={() => document.getElementById("file-upload")?.click()}
                disabled={is_uploading}
              >
                {dict?.media?.selectFiles || "Select Files"}
              </Button>
            </div>
          </div>

          {/* Selected Files List */}
          {selectedFiles.length > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <h4 className="font-medium">
                  {dict?.media?.selectedFiles || "Selected Files"} (
                  {selectedFiles.length})
                </h4>
                <span className="text-sm text-muted-foreground">
                  {dict?.media?.totalSize || "Total:"}{" "}
                  {formatFileSize(totalSize)}
                </span>
              </div>

              <div className="max-h-40 overflow-y-auto space-y-1 border rounded-lg p-2">
                {selectedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-muted/50 rounded-md"
                  >
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      {getFileIcon(file.name)}
                      <span className="text-sm truncate" title={file.name}>
                        {file.name}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {formatFileSize(file.size)}
                      </span>
                    </div>
                    {!is_uploading && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                        className="h-8 w-8 p-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Upload Progress */}
          {is_uploading && (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">
                  {dict?.media?.uploading || "Uploading..."}
                </span>
                <span className="text-sm text-muted-foreground">
                  {uploadMutation.isPending ? "Processing..." : "Complete"}
                </span>
              </div>
              <Progress
                value={uploadMutation.isPending ? 50 : 100}
                className="w-full"
              />
            </div>
          )}
        </div>

        <CustomDialogFooter className="p-0 pt-3">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={is_uploading}
          >
            {dict?.common?.cancel || "Cancel"}
          </Button>
          <Button
            onClick={handleUpload}
            disabled={selectedFiles.length === 0 || is_uploading}
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            {is_uploading
              ? dict?.media?.uploading || "Uploading..."
              : dict?.media?.uploadFiles || "Upload Files"}
          </Button>
        </CustomDialogFooter>
      </CustomDialogContent>
    </CustomDialog>
  );
}
