"use client";

import React, { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogDescription,
  CustomDialogFooter,
} from "@/components/custom/custom-dialog";
import { Folder } from "lucide-react";
import { useMediaDialogs, useMediaNavigation } from "../store/use-media-store";
import { useCreateFolder } from "../hooks/use-media-data";

interface MediaFolderDialogProps {
  dict?: any;
}

export function MediaFolderDialog({ dict }: MediaFolderDialogProps) {
  const { is_folder_dialog_open, closeFolderDialog } = useMediaDialogs();
  const { current_path } = useMediaNavigation();

  const [folderName, setFolderName] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  const createFolderMutation = useCreateFolder();

  const handleCreateFolder = useCallback(async () => {
    if (!folderName.trim()) return;

    setIsCreating(true);

    try {
      await createFolderMutation.mutateAsync({
        folder_name: folderName.trim(),
        parent_path: current_path,
      });

      // Success - close dialog and reset
      setFolderName("");
      closeFolderDialog();
    } catch (error) {
      console.error("Folder creation failed:", error);
    } finally {
      setIsCreating(false);
    }
  }, [folderName, current_path, createFolderMutation, closeFolderDialog]);

  const handleClose = useCallback(() => {
    if (!isCreating) {
      setFolderName("");
      closeFolderDialog();
    }
  }, [isCreating, closeFolderDialog]);

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !isCreating && folderName.trim()) {
        handleCreateFolder();
      }
    },
    [handleCreateFolder, isCreating, folderName]
  );

  return (
    <CustomDialog
      open={is_folder_dialog_open}
      onOpenChange={(open) => !open && handleClose()}
    >
      <CustomDialogContent className="max-w-md shadow-none">
        <CustomDialogHeader>
          <CustomDialogTitle className="flex items-center gap-2">
            <Folder className="h-5 w-5" />
            {dict?.media?.createFolder || "Create Folder"}
          </CustomDialogTitle>
          <CustomDialogDescription>
            {dict?.media?.createFolderDescription ||
              "Enter a name for the new folder."}
          </CustomDialogDescription>
        </CustomDialogHeader>

        <div className="space-y-4 p-4">
          <div className="space-y-2">
            <Label htmlFor="folder-name">
              {dict?.media?.folderName || "Folder Name"}
            </Label>
            <Input
              id="folder-name"
              type="text"
              placeholder={
                dict?.media?.folderNamePlaceholder || "Enter folder name..."
              }
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={isCreating}
              autoFocus
            />
          </div>

          {current_path && (
            <div className="text-sm text-muted-foreground">
              <span className="font-medium">
                {dict?.media?.location || "Location:"}
              </span>{" "}
              /{current_path}
            </div>
          )}
        </div>

        <CustomDialogFooter className="p-4">
          <Button variant="outline" onClick={handleClose} disabled={isCreating}>
            {dict?.common?.cancel || "Cancel"}
          </Button>
          <Button
            onClick={handleCreateFolder}
            disabled={!folderName.trim() || isCreating}
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            {isCreating
              ? dict?.media?.creating || "Creating..."
              : dict?.media?.createFolder || "Create Folder"}
          </Button>
        </CustomDialogFooter>
      </CustomDialogContent>
    </CustomDialog>
  );
}
