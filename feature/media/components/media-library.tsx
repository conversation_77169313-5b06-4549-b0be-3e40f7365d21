"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogDescription,
  CustomDialogFooter,
} from "@/components/custom/custom-dialog";
import {
  Grid3X3,
  List,
  Upload,
  FolderPlus,
  Trash2,
  ArrowLeft,
  Search,
  MoreHorizontal,
  Folder,
  File,
  Image,
  Video,
  Music,
  FileText,
  Archive,
  Download,
  Eye,
  X,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  useMediaNavigation,
  useMediaSelection,
  useMediaView,
  useMediaDialogs,
} from "../store/use-media-store";
import { useGetMedia, useDeleteMedia } from "../hooks/use-media-data";
import { MediaUploadDialog } from "./media-upload-dialog";
import { MediaFolderDialog } from "./media-folder-dialog";
import { MediaItem, MediaFileType } from "@/types/media-api";

interface MediaLibraryProps {
  dict?: any;
}

const getFileIcon = (fileType: MediaFileType, className = "h-4 w-4") => {
  switch (fileType) {
    case "image":
      return <Image className={className} />;
    case "video":
      return <Video className={className} />;
    case "audio":
      return <Music className={className} />;
    case "document":
      return <FileText className={className} />;
    case "archive":
      return <Archive className={className} />;
    default:
      return <File className={className} />;
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

export function MediaLibrary({ dict }: MediaLibraryProps) {
  const { current_path, navigateToPath, navigateBack, canGoBack } =
    useMediaNavigation();
  const {
    selected_items,
    hasSelection,
    selectionCount,
    toggleItemSelection,
    clearSelection,
  } = useMediaSelection();
  const { view_mode, filter, setViewMode, setFilter } = useMediaView();
  const {
    openUploadDialog,
    openFolderDialog,
    is_delete_dialog_open,
    openDeleteDialog,
    closeDeleteDialog,
  } = useMediaDialogs();

  const [previewItem, setPreviewItem] = useState<MediaItem | null>(null);

  // Fetch media data
  const {
    data: mediaData,
    isLoading,
    refetch,
  } = useGetMedia({
    path: current_path,
    limit: 100,
  });

  const deleteMediaMutation = useDeleteMedia();

  // Filter items based on current filter
  const filteredItems = React.useMemo(() => {
    if (!mediaData?.data?.items) return [];

    let items = mediaData.data.items;

    // Apply type filter - fix the filter mapping
    if (filter.type && filter.type !== "all") {
      items = items.filter((item) => {
        if (item.type === "folder") return filter.type === "all";

        // Map filter options to file types
        switch (filter.type) {
          case "images":
            return item.file_type === "image";
          case "videos":
            return item.file_type === "video";
          case "documents":
            return item.file_type === "document";
          case "audio":
            return item.file_type === "audio";
          case "other":
            return item.file_type === "other" || item.file_type === "archive";
          default:
            return true;
        }
      });
    }

    // Apply search filter
    if (filter.search) {
      const searchTerm = filter.search.toLowerCase();
      items = items.filter((item) =>
        item.name.toLowerCase().includes(searchTerm)
      );
    }

    // Apply sorting
    items.sort((a, b) => {
      // Folders first
      if (a.type === "folder" && b.type === "file") return -1;
      if (a.type === "file" && b.type === "folder") return 1;

      const aValue =
        filter.sort_by === "name"
          ? a.name
          : filter.sort_by === "size"
            ? a.size || 0
            : a.last_modified || "";
      const bValue =
        filter.sort_by === "name"
          ? b.name
          : filter.sort_by === "size"
            ? b.size || 0
            : b.last_modified || "";

      if (filter.sort_order === "desc") {
        return aValue > bValue ? -1 : 1;
      }
      return aValue < bValue ? -1 : 1;
    });

    return items;
  }, [mediaData?.data?.items, filter]);

  const handleItemClick = (item: MediaItem) => {
    if (item.type === "folder") {
      navigateToPath(item.key);
    } else {
      // For files, show preview instead of selecting
      setPreviewItem(item);
    }
  };

  const handleDeleteSelected = async () => {
    if (selected_items.length === 0) return;

    try {
      await deleteMediaMutation.mutateAsync({
        keys: selected_items,
      });

      clearSelection();
      closeDeleteDialog();
    } catch (error) {
      console.error("Delete failed:", error);
    }
  };

  const handleDownload = (item: MediaItem, e: React.MouseEvent) => {
    e.stopPropagation();
    if (item.url) {
      const link = document.createElement("a");
      link.href = item.url;
      link.download = item.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Clear selection when path changes
  useEffect(() => {
    clearSelection();
  }, [current_path, clearSelection]);

  return (
    <div className="space-y-6">
      {/* Toolbar */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-2">
          {canGoBack && (
            <Button variant="outline" size="sm" onClick={navigateBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}

          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              {dict?.media?.currentPath || "Current path:"}
            </span>
            <Badge variant="outline">/{current_path || "root"}</Badge>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={openFolderDialog}>
            <FolderPlus className="h-4 w-4 mr-2" />
            {dict?.media?.newFolder || "New Folder"}
          </Button>

          <Button
            size="sm"
            onClick={openUploadDialog}
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            <Upload className="h-4 w-4 mr-2" />
            {dict?.media?.upload || "Upload"}
          </Button>

          {hasSelection && (
            <Button
              variant="destructive"
              size="sm"
              onClick={openDeleteDialog}
              className="text-white"
            >
              <Trash2 className="h-4 w-4" />
              {dict?.media?.delete || "Delete"} ({selectionCount})
            </Button>
          )}
        </div>
      </div>

      {/* Filters and View Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-2 flex-1">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={dict?.media?.searchFiles || "Search files..."}
              value={filter.search || ""}
              onChange={(e) => setFilter({ search: e.target.value })}
              className="pl-10"
            />
          </div>

          <select
            value={filter.type || "all"}
            onChange={(e) => setFilter({ type: e.target.value as any })}
            className="px-3 py-2 border border-input bg-background rounded-md text-sm"
          >
            <option value="all">{dict?.media?.allTypes || "All Types"}</option>
            <option value="images">{dict?.media?.images || "Images"}</option>
            <option value="videos">{dict?.media?.videos || "Videos"}</option>
            <option value="documents">
              {dict?.media?.documents || "Documents"}
            </option>
            <option value="audio">{dict?.media?.audio || "Audio"}</option>
            <option value="other">{dict?.media?.other || "Other"}</option>
          </select>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant={view_mode === "grid" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("grid")}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={view_mode === "list" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("list")}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Separator />

      {/* Content */}
      {isLoading ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {Array.from({ length: 12 }).map((_, i) => (
            <div
              key={i}
              className="aspect-square bg-muted rounded-lg animate-pulse"
            />
          ))}
        </div>
      ) : filteredItems.length === 0 ? (
        <div className="text-center py-12">
          <Folder className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-lg font-medium">
            {dict?.media?.noFiles || "No files found"}
          </p>
          <p className="text-muted-foreground">
            {dict?.media?.noFilesDescription ||
              "Upload some files to get started"}
          </p>
        </div>
      ) : view_mode === "grid" ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {filteredItems.map((item) => (
            <div
              key={item.key}
              className={cn(
                "relative group aspect-square border rounded-lg overflow-hidden cursor-pointer transition-all hover:bg-muted/50",
                selected_items.includes(item.key) &&
                  "bg-primary/10 border-primary"
              )}
              onClick={() => handleItemClick(item)}
            >
              {/* Selection checkbox */}
              <div
                className="absolute top-2 left-2 z-10"
                onClick={(e) => e.stopPropagation()}
              >
                <Checkbox
                  checked={selected_items.includes(item.key)}
                  onCheckedChange={() => toggleItemSelection(item.key)}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>

              {/* Actions */}
              <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="flex gap-1">
                  <Button
                    variant="secondary"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      setPreviewItem(item);
                    }}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  {item.type === "file" && (
                    <Button
                      variant="secondary"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={(e) => handleDownload(item, e)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Content */}
              <div className="h-full flex flex-col">
                {item.type === "folder" ? (
                  <div className="flex-1 flex items-center justify-center">
                    <Folder className="h-12 w-12 text-primary" />
                  </div>
                ) : item.file_type === "image" ? (
                  <div className="flex-1 relative bg-muted">
                    <img
                      src={item.url}
                      alt={item.name}
                      className="w-full h-full object-cover"
                      loading="lazy"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = "none";
                        const fallback =
                          target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = "flex";
                      }}
                    />
                    <div className="hidden items-center justify-center w-full h-full absolute inset-0">
                      {getFileIcon(item.file_type!, "h-8 w-8")}
                    </div>
                  </div>
                ) : item.file_type === "video" ? (
                  <div className="flex-1 relative bg-muted flex items-center justify-center">
                    <Video className="h-12 w-12 text-primary" />
                    <div className="absolute bottom-2 right-2">
                      <Badge variant="secondary" className="text-xs">
                        VIDEO
                      </Badge>
                    </div>
                  </div>
                ) : (
                  <div className="flex-1 flex items-center justify-center bg-muted">
                    {getFileIcon(item.file_type!, "h-8 w-8 text-primary")}
                  </div>
                )}

                {/* Info */}
                <div className="p-2 bg-background">
                  <p className="text-xs font-medium truncate" title={item.name}>
                    {item.name}
                  </p>
                  {item.size && (
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(item.size)}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-1">
          {filteredItems.map((item) => (
            <div
              key={item.key}
              className={cn(
                "flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-all hover:bg-muted/50 group",
                selected_items.includes(item.key) &&
                  "bg-primary/10 border-primary"
              )}
              onClick={() => handleItemClick(item)}
            >
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <div onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    checked={selected_items.includes(item.key)}
                    onCheckedChange={() => toggleItemSelection(item.key)}
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>

                {item.type === "folder" ? (
                  <Folder className="h-5 w-5 text-primary flex-shrink-0" />
                ) : item.file_type === "image" ? (
                  <div className="w-10 h-10 rounded overflow-hidden flex-shrink-0">
                    <img
                      src={item.url}
                      alt={item.name}
                      className="w-full h-full object-cover"
                      loading="lazy"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = "none";
                        const fallback =
                          target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = "flex";
                      }}
                    />
                    <div className="hidden w-full h-full items-center justify-center bg-muted">
                      {getFileIcon(item.file_type!, "h-4 w-4")}
                    </div>
                  </div>
                ) : (
                  <div className="flex-shrink-0">
                    {getFileIcon(item.file_type!, "h-5 w-5 text-primary")}
                  </div>
                )}
                <span className="truncate font-medium">{item.name}</span>
              </div>

              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                {item.size && <span>{formatFileSize(item.size)}</span>}
                {item.last_modified && (
                  <span>{formatDate(item.last_modified)}</span>
                )}

                <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      setPreviewItem(item);
                    }}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  {item.type === "file" && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={(e) => handleDownload(item, e)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Dialogs */}
      <MediaUploadDialog dict={dict} />
      <MediaFolderDialog dict={dict} />

      {/* Delete Confirmation Dialog */}
      <CustomDialog
        open={is_delete_dialog_open}
        onOpenChange={closeDeleteDialog}
      >
        <CustomDialogContent className="max-w-md shadow-none">
          <CustomDialogHeader>
            <CustomDialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-destructive" />
              {dict?.media?.confirmDelete || "Confirm Delete"}
            </CustomDialogTitle>
            <CustomDialogDescription>
              {dict?.media?.deleteConfirmation ||
                `Are you sure you want to delete ${selectionCount} item(s)? This action cannot be undone.`}
            </CustomDialogDescription>
          </CustomDialogHeader>
          <CustomDialogFooter className="p-4">
            <Button variant="outline" onClick={closeDeleteDialog}>
              {dict?.common?.cancel || "Cancel"}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteSelected}
              disabled={deleteMediaMutation.isPending}
              className="text-white"
            >
              {deleteMediaMutation.isPending
                ? dict?.media?.deleting || "Deleting..."
                : dict?.media?.delete || "Delete"}
            </Button>
          </CustomDialogFooter>
        </CustomDialogContent>
      </CustomDialog>

      {/* Preview Dialog */}
      {previewItem && (
        <CustomDialog
          open={!!previewItem}
          onOpenChange={() => setPreviewItem(null)}
        >
          <CustomDialogContent className="max-w-4xl shadow-none">
            <CustomDialogHeader>
              <CustomDialogTitle className="flex items-center gap-2">
                {getFileIcon(previewItem.file_type!, "h-5 w-5")}
                {previewItem.name}
              </CustomDialogTitle>
              <CustomDialogDescription>
                {previewItem.file_type?.toUpperCase()} •{" "}
                {previewItem.size ? formatFileSize(previewItem.size) : ""}
                {previewItem.last_modified &&
                  ` • ${formatDate(previewItem.last_modified)}`}
              </CustomDialogDescription>
            </CustomDialogHeader>

            <div className="p-4 max-h-[60vh] overflow-auto">
              {previewItem.file_type === "image" ? (
                <img
                  src={previewItem.url}
                  alt={previewItem.name}
                  className="max-w-full h-auto mx-auto rounded-lg"
                />
              ) : previewItem.file_type === "video" ? (
                <video
                  src={previewItem.url}
                  controls
                  className="max-w-full h-auto mx-auto rounded-lg"
                />
              ) : previewItem.file_type === "audio" ? (
                <audio src={previewItem.url} controls className="w-full" />
              ) : (
                <div className="text-center py-12">
                  {getFileIcon(
                    previewItem.file_type!,
                    "h-16 w-16 mx-auto mb-4 text-muted-foreground"
                  )}
                  <p className="text-lg font-medium">{previewItem.name}</p>
                  <p className="text-muted-foreground">
                    Preview not available for this file type
                  </p>
                </div>
              )}
            </div>

            <CustomDialogFooter className="p-4">
              <Button variant="outline" onClick={() => setPreviewItem(null)}>
                {dict?.common?.close || "Close"}
              </Button>
              <Button
                onClick={(e) => handleDownload(previewItem, e as any)}
                className="bg-primary text-primary-foreground hover:bg-primary/90"
              >
                <Download className="h-4 w-4 mr-2" />
                {dict?.media?.download || "Download"}
              </Button>
            </CustomDialogFooter>
          </CustomDialogContent>
        </CustomDialog>
      )}
    </div>
  );
}
