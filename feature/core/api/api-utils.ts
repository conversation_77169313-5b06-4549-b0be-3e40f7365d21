import {
  useMutation,
  useQuery,
  useQueryClient,
  QueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// API Response types
export interface ApiResponse<T = any> {
  success?: boolean;
  message?: string;
  error?: string;
  data?: T;
}

export interface ApiError {
  message: string;
  status?: number;
  errors?: Record<string, string[]>;
}

// Generic API request function
export async function apiRequest<TData = any>(
  url: string,
  options: RequestInit = {}
): Promise<TData> {
  const defaultHeaders = {
    "Content-Type": "application/json",
  };

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw {
        message: data.error || data.message || "Request failed",
        status: response.status,
        errors: data.errors,
      } as ApiError;
    }

    return data as TData;
  } catch (error) {
    if (error instanceof Error) {
      throw {
        message: error.message,
        status: 500,
      } as ApiError;
    }
    throw error;
  }
}

// GET request helper
export async function apiGet<TData = any>(url: string): Promise<TData> {
  return apiRequest<TData>(url, { method: "GET" });
}

// POST request helper
export async function apiPost<TData = any>(
  url: string,
  data?: any
): Promise<TData> {
  return apiRequest<TData>(url, {
    method: "POST",
    body: data ? JSON.stringify(data) : undefined,
  });
}

// PUT request helper
export async function apiPut<TData = any>(
  url: string,
  data?: any
): Promise<TData> {
  return apiRequest<TData>(url, {
    method: "PUT",
    body: data ? JSON.stringify(data) : undefined,
  });
}

// DELETE request helper
export async function apiDelete<TData = any>(url: string): Promise<TData> {
  return apiRequest<TData>(url, { method: "DELETE" });
}

// PATCH request helper
export async function apiPatch<TData = any>(
  url: string,
  data?: any
): Promise<TData> {
  return apiRequest<TData>(url, {
    method: "PATCH",
    body: data ? JSON.stringify(data) : undefined,
  });
}

// Custom mutation hook with error handling
export function useApiMutation<TData = any, TVariables = any>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: {
    onSuccess?: (data: TData, variables: TVariables) => void;
    onError?: (error: ApiError, variables: TVariables) => void;
    successMessage?: string;
    errorMessage?: string;
    invalidateQueries?: string[];
    showErrorToast?: boolean; // New option to control automatic error toasts
  }
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn,
    onSuccess: (data, variables) => {
      if (options?.successMessage) {
        toast.success(options.successMessage);
      }

      if (options?.invalidateQueries) {
        options.invalidateQueries.forEach((query) => {
          queryClient.invalidateQueries({ queryKey: [query] });
        });
      }

      options?.onSuccess?.(data, variables);
    },
    onError: (error: ApiError, variables) => {
      // Only show automatic error toast if explicitly enabled or if no custom onError is provided
      const shouldShowAutoToast = options?.showErrorToast ?? !options?.onError;

      if (shouldShowAutoToast) {
        const errorMessage =
          options?.errorMessage || error.message || "An error occurred";
        toast.error(errorMessage);
      }

      options?.onError?.(error, variables);
    },
  });
}

// Custom query hook with error handling
export function useApiQuery<TData = any>(
  queryKey: string[],
  queryFn: () => Promise<TData>,
  options?: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
    refetchOnWindowFocus?: boolean;
    onError?: (error: ApiError) => void;
  }
) {
  return useQuery({
    queryKey,
    queryFn,
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5 minutes default
    gcTime: options?.cacheTime ?? 10 * 60 * 1000, // 10 minutes default
    refetchOnWindowFocus: options?.refetchOnWindowFocus ?? false,
    enabled: options?.enabled,
    throwOnError: (error) => {
      const apiError = error as ApiError;
      options?.onError?.(apiError);
      return false;
    },
  });
}

// Query client configuration
export const queryClientConfig = {
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      retry: (failureCount: number, error: any) => {
        // Don't retry on 4xx errors
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
    },
    mutations: {
      retry: false,
    },
  },
};

// Create query client instance
export const createQueryClient = () => new QueryClient(queryClientConfig);
