// Email template brand colors
// Converted from OKLCH colors in globals.css to hex equivalents for email compatibility

export const brandColors = {
  // Primary brand colors
  primary: "#c77332", // oklch(0.585 0.15 25.5) - main brand orange
  primaryLight: "#e6936b", // lighter variant for gradients
  primaryDark: "#a85d28", // darker variant

  // Secondary colors
  secondary: "#f0e4d7", // oklch(0.865 0.06 25.5) - light peach
  secondaryDark: "#e6d4c1", // slightly darker variant

  // Background colors
  background: "#ffffff", // oklch(1 0 0) - pure white
  backgroundMuted: "#f8fafc", // slightly off-white for email body

  // Text colors
  foreground: "#1a1a1a", // oklch(0.145 0 0) - near black
  mutedForeground: "#666666", // oklch(0.556 0 0) - medium gray

  // UI colors
  muted: "#f8f8f8", // oklch(0.97 0 0) - light gray backgrounds
  border: "#e5e5e5", // oklch(0.922 0 0) - borders and dividers

  // Status colors
  destructive: "#d73527", // oklch(0.577 0.245 27.325) - red for warnings/resets
  destructiveLight: "#ef4444", // lighter red variant
  success: "#16a34a", // green for success states
  warning: "#f59e0b", // amber for warnings

  // Transparent variants for email compatibility
  primaryAlpha25: "rgba(199, 115, 50, 0.25)", // primary with 25% opacity
  destructiveAlpha25: "rgba(215, 53, 39, 0.25)", // destructive with 25% opacity
  whiteAlpha90: "rgba(255, 255, 255, 0.9)", // white with 90% opacity
} as const;

// Common email styles for consistency
export const emailStyles = {
  // Typography
  fontFamily:
    '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',

  // Container dimensions
  containerWidth: "600px",
  containerMaxWidth: "600px",

  // Spacing
  containerMargin: "40px auto",
  sectionPadding: "48px",
  headerPadding: "40px 48px 32px",
  footerPadding: "32px 48px",

  // Border radius
  containerRadius: "16px",
  buttonRadius: "12px",
  cardRadius: "8px",

  // Shadows
  containerShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
  buttonShadow: brandColors.primaryAlpha25,
  buttonShadowDestructive: brandColors.destructiveAlpha25,
} as const;

// Gradient definitions for email headers
export const gradients = {
  primary: `linear-gradient(135deg, ${brandColors.primary} 0%, ${brandColors.primaryLight} 100%)`,
  destructive: `linear-gradient(135deg, ${brandColors.destructive} 0%, ${brandColors.destructiveLight} 100%)`,
} as const;
