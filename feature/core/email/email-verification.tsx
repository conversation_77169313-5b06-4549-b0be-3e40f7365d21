import {
  <PERSON>,
  But<PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
  Row,
  Column,
} from "@react-email/components";
import * as React from "react";
import { brandColors, emailStyles, gradients } from "./email-colors";

interface EmailVerificationTemplateProps {
  userEmail: string;
  verificationUrl: string;
}

export const EmailVerificationTemplate = ({
  userEmail,
  verificationUrl,
}: EmailVerificationTemplateProps) => (
  <Html>
    <Head />
    <Preview>Verify your email to get started with AppList</Preview>
    <Body style={main}>
      <Container style={container}>
        {/* Header with Brand */}
        <Section style={header}>
          <Row>
            <Column>
              <Section style={logoContainer}>
                <Heading style={brandText}>AppList</Heading>
                <Text style={tagline}>Your Apps, Organized</Text>
              </Section>
            </Column>
          </Row>
        </Section>

        {/* Main Content */}
        <Section style={content}>
          <Section style={welcomeSection}>
            <Heading style={h1}>Welcome to AppList! 🎉</Heading>
            <Text style={leadText}>
              We're excited to have you on board. Let's verify your email
              address to get you started.
            </Text>
          </Section>

          <Section style={emailDisplaySection}>
            <Text style={emailLabel}>Email Address:</Text>
            <Text style={emailValue}>{userEmail}</Text>
          </Section>

          <Section style={ctaSection}>
            <Text style={text}>
              Click the button below to verify your email address and activate
              your account:
            </Text>
            <Section style={buttonContainer}>
              <Button style={button} href={verificationUrl}>
                <Text style={buttonText}>Verify Email Address</Text>
              </Button>
            </Section>
          </Section>

          <Section style={alternativeSection}>
            <Text style={alternativeText}>
              Having trouble with the button? Copy and paste this link into your
              browser:
            </Text>
            <Link href={verificationUrl} style={link}>
              {verificationUrl}
            </Link>
          </Section>

          <Section style={securitySection}>
            <Text style={securityText}>
              ⏰ This verification link will expire in 24 hours for security
              reasons.
            </Text>
          </Section>
        </Section>

        {/* Divider */}
        <Hr style={hr} />

        {/* Footer */}
        <Section style={footer}>
          <Text style={footerText}>
            If you didn't create an account with AppList, you can safely ignore
            this email.
          </Text>
          <Text style={footerBrand}>© 2024 AppList. All rights reserved.</Text>
          <Text style={footerSecondary}>
            Need help? Contact <NAME_EMAIL>
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

export default EmailVerificationTemplate;

const main = {
  backgroundColor: brandColors.backgroundMuted,
  fontFamily: emailStyles.fontFamily,
  padding: "0",
  margin: "0",
};

const container = {
  backgroundColor: brandColors.background,
  border: `1px solid ${brandColors.border}`,
  borderRadius: emailStyles.containerRadius,
  margin: emailStyles.containerMargin,
  padding: "0",
  width: emailStyles.containerWidth,
  maxWidth: emailStyles.containerMaxWidth,
  boxShadow: emailStyles.containerShadow,
};

const header = {
  background: gradients.primary,
  borderRadius: `${emailStyles.containerRadius} ${emailStyles.containerRadius} 0 0`,
  padding: emailStyles.headerPadding,
  textAlign: "center" as const,
};

const logoContainer = {
  textAlign: "center" as const,
};

const brandText = {
  color: brandColors.background,
  fontSize: "32px",
  fontWeight: "700",
  lineHeight: "1",
  margin: "0 0 8px 0",
  textAlign: "center" as const,
  letterSpacing: "-0.02em",
};

const tagline = {
  color: "rgba(255, 255, 255, 0.9)",
  fontSize: "16px",
  fontWeight: "400",
  lineHeight: "1.4",
  margin: "0",
  textAlign: "center" as const,
};

const content = {
  padding: emailStyles.sectionPadding,
};

const welcomeSection = {
  textAlign: "center" as const,
  marginBottom: "32px",
};

const h1 = {
  color: brandColors.foreground,
  fontSize: "28px",
  fontWeight: "700",
  lineHeight: "1.2",
  margin: "0 0 16px 0",
  textAlign: "center" as const,
  letterSpacing: "-0.01em",
};

const leadText = {
  color: brandColors.mutedForeground,
  fontSize: "18px",
  lineHeight: "1.6",
  margin: "0",
  textAlign: "center" as const,
};

const emailDisplaySection = {
  backgroundColor: brandColors.secondary,
  borderRadius: "12px",
  padding: "24px",
  marginBottom: "32px",
  textAlign: "center" as const,
};

const emailLabel = {
  color: brandColors.mutedForeground,
  fontSize: "14px",
  fontWeight: "500",
  lineHeight: "1.4",
  margin: "0 0 8px 0",
  textTransform: "uppercase" as const,
  letterSpacing: "0.05em",
};

const emailValue = {
  color: brandColors.foreground,
  fontSize: "18px",
  fontWeight: "600",
  lineHeight: "1.4",
  margin: "0",
  wordBreak: "break-all" as const,
};

const ctaSection = {
  textAlign: "center" as const,
  marginBottom: "32px",
};

const text = {
  color: brandColors.foreground,
  fontSize: "16px",
  lineHeight: "1.6",
  margin: "0 0 24px 0",
  textAlign: "center" as const,
};

const buttonContainer = {
  textAlign: "center" as const,
};

const button = {
  background: gradients.primary,
  border: "none",
  borderRadius: emailStyles.buttonRadius,
  display: "inline-block",
  fontSize: "16px",
  fontWeight: "600",
  lineHeight: "1",
  padding: "16px 32px",
  textAlign: "center" as const,
  textDecoration: "none",
  boxShadow: `0 4px 12px ${brandColors.primaryAlpha25}`,
  transition: "all 0.2s ease",
};

const buttonText = {
  color: brandColors.background,
  fontSize: "16px",
  fontWeight: "600",
  lineHeight: "1",
  margin: "0",
  textDecoration: "none",
};

const alternativeSection = {
  backgroundColor: brandColors.muted,
  borderRadius: "8px",
  padding: "20px",
  marginBottom: "24px",
};

const alternativeText = {
  color: brandColors.mutedForeground,
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0 0 12px 0",
  textAlign: "center" as const,
};

const link = {
  color: brandColors.primary,
  fontSize: "14px",
  fontWeight: "500",
  textDecoration: "underline",
  wordBreak: "break-all" as const,
  display: "block",
  textAlign: "center" as const,
};

const securitySection = {
  textAlign: "center" as const,
};

const securityText = {
  color: brandColors.mutedForeground,
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0",
  textAlign: "center" as const,
  fontStyle: "italic",
};

const hr = {
  border: "none",
  borderTop: `1px solid ${brandColors.border}`,
  margin: "0",
};

const footer = {
  padding: emailStyles.footerPadding,
  textAlign: "center" as const,
  backgroundColor: brandColors.muted,
  borderRadius: `0 0 ${emailStyles.containerRadius} ${emailStyles.containerRadius}`,
};

const footerText = {
  color: brandColors.mutedForeground,
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0 0 16px 0",
  textAlign: "center" as const,
};

const footerBrand = {
  color: brandColors.foreground,
  fontSize: "14px",
  fontWeight: "600",
  lineHeight: "1.4",
  margin: "0 0 8px 0",
  textAlign: "center" as const,
};

const footerSecondary = {
  color: brandColors.mutedForeground,
  fontSize: "13px",
  lineHeight: "1.4",
  margin: "0",
  textAlign: "center" as const,
};