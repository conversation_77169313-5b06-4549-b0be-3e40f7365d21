import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Link,
  Preview,
  Section,
  Text,
  Row,
  Column,
} from "@react-email/components";
import * as React from "react";
import { brandColors, emailStyles, gradients } from "./email-colors";

interface WelcomeEmailTemplateProps {
  userName: string;
  userEmail: string;
}

export const WelcomeEmailTemplate = ({
  userName,
  userEmail,
}: WelcomeEmailTemplateProps) => (
  <Html>
    <Head />
    <Preview>Welcome to AppList - Your account is now active!</Preview>
    <Body style={main}>
      <Container style={container}>
        {/* Header with Brand */}
        <Section style={header}>
          <Row>
            <Column>
              <Section style={logoContainer}>
                <Heading style={brandText}>AppList</Heading>
                <Text style={tagline}>Your Apps, Organized</Text>
              </Section>
            </Column>
          </Row>
        </Section>

        {/* Main Content */}
        <Section style={content}>
          <Section style={welcomeSection}>
            <Heading style={h1}>Welcome to AppList, {userName}! 🚀</Heading>
            <Text style={leadText}>
              Your email has been successfully verified and your account is now
              active. We're thrilled to have you join our community!
            </Text>
          </Section>

          <Section style={successSection}>
            <Text style={successIcon}>✅</Text>
            <Text style={successText}>Email Verified Successfully</Text>
            <Text style={emailValue}>{userEmail}</Text>
          </Section>

          <Section style={featuresSection}>
            <Heading style={featuresTitle}>What you can do now:</Heading>

            <Section style={featureItem}>
              <Text style={featureIcon}>📱</Text>
              <Section style={featureContent}>
                <Text style={featureTitle}>Organize Your Apps</Text>
                <Text style={featureDescription}>
                  Create custom lists and categories for all your applications
                </Text>
              </Section>
            </Section>

            <Section style={featureItem}>
              <Text style={featureIcon}>🔍</Text>
              <Section style={featureContent}>
                <Text style={featureTitle}>Discover New Apps</Text>
                <Text style={featureDescription}>
                  Find and explore new applications tailored to your interests
                </Text>
              </Section>
            </Section>

            <Section style={featureItem}>
              <Text style={featureIcon}>📊</Text>
              <Section style={featureContent}>
                <Text style={featureTitle}>Track Usage</Text>
                <Text style={featureDescription}>
                  Monitor and analyze your app usage patterns and preferences
                </Text>
              </Section>
            </Section>
          </Section>

          <Section style={ctaSection}>
            <Text style={ctaText}>Ready to get started?</Text>
            <Section style={buttonContainer}>
              <Button style={button} href={`${process.env.NEXTAUTH_URL}`}>
                <Text style={buttonText}>Start Using AppList</Text>
              </Button>
            </Section>
          </Section>

          <Section style={helpSection}>
            <Text style={helpText}>
              Need help getting started? Check out our{" "}
              <Link href={`${process.env.NEXTAUTH_URL}/help`} style={link}>
                getting started guide
              </Link>{" "}
              or{" "}
              <Link href={`${process.env.NEXTAUTH_URL}/contact`} style={link}>
                contact our support team
              </Link>
              .
            </Text>
          </Section>
        </Section>

        {/* Divider */}
        <Hr style={hr} />

        {/* Footer */}
        <Section style={footer}>
          <Text style={footerText}>
            Thank you for choosing AppList to organize your digital life!
          </Text>
          <Text style={footerBrand}>© 2024 AppList. All rights reserved.</Text>
          <Text style={footerSecondary}>
            Need help? Contact <NAME_EMAIL>
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

export default WelcomeEmailTemplate;

const main = {
  backgroundColor: "#f8fafc",
  fontFamily:
    '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  padding: "0",
  margin: "0",
};

const container = {
  backgroundColor: brandColors.background,
  border: `1px solid ${brandColors.border}`,
  borderRadius: "16px",
  margin: "40px auto",
  padding: "0",
  width: "600px",
  maxWidth: "600px",
  boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
};

const header = {
  background: `linear-gradient(135deg, ${brandColors.primary} 0%, ${brandColors.primaryLight} 100%)`,
  borderRadius: "16px 16px 0 0",
  padding: "40px 48px 32px",
  textAlign: "center" as const,
};

const logoContainer = {
  textAlign: "center" as const,
};

const brandText = {
  color: brandColors.background,
  fontSize: "32px",
  fontWeight: "700",
  lineHeight: "1",
  margin: "0 0 8px 0",
  textAlign: "center" as const,
  letterSpacing: "-0.02em",
};

const tagline = {
  color: "rgba(255, 255, 255, 0.9)",
  fontSize: "16px",
  fontWeight: "400",
  lineHeight: "1.4",
  margin: "0",
  textAlign: "center" as const,
};

const content = {
  padding: "48px",
};

const welcomeSection = {
  textAlign: "center" as const,
  marginBottom: "32px",
};

const h1 = {
  color: brandColors.foreground,
  fontSize: "28px",
  fontWeight: "700",
  lineHeight: "1.2",
  margin: "0 0 16px 0",
  textAlign: "center" as const,
  letterSpacing: "-0.01em",
};

const leadText = {
  color: brandColors.mutedForeground,
  fontSize: "18px",
  lineHeight: "1.6",
  margin: "0",
  textAlign: "center" as const,
};

const successSection = {
  backgroundColor: "#f0f9f0",
  borderRadius: "12px",
  padding: "24px",
  marginBottom: "32px",
  textAlign: "center" as const,
  border: `2px solid ${brandColors.success}`,
};

const successIcon = {
  fontSize: "24px",
  margin: "0 0 8px 0",
  display: "block",
};

const successText = {
  color: brandColors.success,
  fontSize: "16px",
  fontWeight: "600",
  lineHeight: "1.4",
  margin: "0 0 8px 0",
  textTransform: "uppercase" as const,
  letterSpacing: "0.05em",
};

const emailValue = {
  color: brandColors.foreground,
  fontSize: "16px",
  fontWeight: "500",
  lineHeight: "1.4",
  margin: "0",
  wordBreak: "break-all" as const,
};

const featuresSection = {
  marginBottom: "32px",
};

const featuresTitle = {
  color: brandColors.foreground,
  fontSize: "22px",
  fontWeight: "600",
  lineHeight: "1.3",
  margin: "0 0 24px 0",
  textAlign: "center" as const,
};

const featureItem = {
  display: "flex",
  alignItems: "flex-start",
  marginBottom: "20px",
  padding: "16px",
  backgroundColor: brandColors.secondary,
  borderRadius: "8px",
};

const featureIcon = {
  fontSize: "24px",
  margin: "0 16px 0 0",
  display: "inline-block",
  minWidth: "32px",
};

const featureContent = {
  flex: "1",
};

const featureTitle = {
  color: brandColors.foreground,
  fontSize: "16px",
  fontWeight: "600",
  lineHeight: "1.4",
  margin: "0 0 4px 0",
};

const featureDescription = {
  color: brandColors.mutedForeground,
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0",
};

const ctaSection = {
  textAlign: "center" as const,
  marginBottom: "32px",
};

const ctaText = {
  color: brandColors.foreground,
  fontSize: "18px",
  fontWeight: "500",
  lineHeight: "1.6",
  margin: "0 0 20px 0",
  textAlign: "center" as const,
};

const buttonContainer = {
  textAlign: "center" as const,
};

const button = {
  background: `linear-gradient(135deg, ${brandColors.primary} 0%, ${brandColors.primaryLight} 100%)`,
  border: "none",
  borderRadius: "12px",
  display: "inline-block",
  fontSize: "16px",
  fontWeight: "600",
  lineHeight: "1",
  padding: "16px 32px",
  textAlign: "center" as const,
  textDecoration: "none",
  boxShadow: `0 4px 12px rgba(199, 115, 50, 0.25)`,
  transition: "all 0.2s ease",
};

const buttonText = {
  color: brandColors.background,
  fontSize: "16px",
  fontWeight: "600",
  lineHeight: "1",
  margin: "0",
  textDecoration: "none",
};

const helpSection = {
  backgroundColor: brandColors.muted,
  borderRadius: "8px",
  padding: "20px",
  textAlign: "center" as const,
};

const helpText = {
  color: brandColors.mutedForeground,
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0",
  textAlign: "center" as const,
};

const link = {
  color: brandColors.primary,
  fontSize: "14px",
  fontWeight: "500",
  textDecoration: "underline",
};

const hr = {
  border: "none",
  borderTop: `1px solid ${brandColors.border}`,
  margin: "0",
};

const footer = {
  padding: "32px 48px",
  textAlign: "center" as const,
  backgroundColor: brandColors.muted,
  borderRadius: "0 0 16px 16px",
};

const footerText = {
  color: brandColors.mutedForeground,
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0 0 16px 0",
  textAlign: "center" as const,
};

const footerBrand = {
  color: brandColors.foreground,
  fontSize: "14px",
  fontWeight: "600",
  lineHeight: "1.4",
  margin: "0 0 8px 0",
  textAlign: "center" as const,
};

const footerSecondary = {
  color: brandColors.mutedForeground,
  fontSize: "13px",
  lineHeight: "1.4",
  margin: "0",
  textAlign: "center" as const,
};
