import {
  <PERSON>,
  But<PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Link,
  Preview,
  Section,
  Text,
  Row,
  Column,
} from "@react-email/components";
import * as React from "react";
import { brandColors, emailStyles, gradients } from "./email-colors";

interface PasswordResetTemplateProps {
  userEmail: string;
  resetUrl: string;
}

export const PasswordResetTemplate = ({
  userEmail,
  resetUrl,
}: PasswordResetTemplateProps) => (
  <Html>
    <Head />
    <Preview>Reset your AppList password</Preview>
    <Body style={main}>
      <Container style={container}>
        {/* Header with Brand */}
        <Section style={header}>
          <Row>
            <Column>
              <Section style={logoContainer}>
                <Heading style={brandText}>AppList</Heading>
                <Text style={tagline}>Your Apps, Organized</Text>
              </Section>
            </Column>
          </Row>
        </Section>

        {/* Main Content */}
        <Section style={content}>
          <Section style={alertSection}>
            <Text style={alertIcon}>🔐</Text>
            <Heading style={h1}>Password Reset Request</Heading>
            <Text style={leadText}>
              We received a request to reset the password for your AppList
              account.
            </Text>
          </Section>

          <Section style={emailDisplaySection}>
            <Text style={emailLabel}>Account Email:</Text>
            <Text style={emailValue}>{userEmail}</Text>
          </Section>

          <Section style={ctaSection}>
            <Text style={text}>
              Click the button below to create a new password for your account:
            </Text>
            <Section style={buttonContainer}>
              <Button style={button} href={resetUrl}>
                <Text style={buttonText}>Reset Password</Text>
              </Button>
            </Section>
          </Section>

          <Section style={alternativeSection}>
            <Text style={alternativeText}>
              If the button doesn't work, copy and paste this link into your
              browser:
            </Text>
            <Link href={resetUrl} style={link}>
              {resetUrl}
            </Link>
          </Section>

          <Section style={securitySection}>
            <Heading style={securityTitle}>Security Information</Heading>
            <Section style={securityItem}>
              <Text style={securityBullet}>🕒</Text>
              <Text style={securityText}>
                This password reset link will expire in 1 hour for security
                reasons.
              </Text>
            </Section>
            <Section style={securityItem}>
              <Text style={securityBullet}>🔒</Text>
              <Text style={securityText}>
                If you didn't request this password reset, please ignore this
                email. Your account remains secure.
              </Text>
            </Section>
            <Section style={securityItem}>
              <Text style={securityBullet}>⚠️</Text>
              <Text style={securityText}>
                For your security, never share this link with anyone else.
              </Text>
            </Section>
          </Section>

          <Section style={helpSection}>
            <Text style={helpTitle}>Need Help?</Text>
            <Text style={helpText}>
              If you're having trouble resetting your password or didn't request
              this change, please contact our support team at{" "}
              <Link href="mailto:<EMAIL>" style={link}>
                <EMAIL>
              </Link>{" "}
              or visit our{" "}
              <Link href={`${process.env.NEXTAUTH_URL}/help`} style={link}>
                help center
              </Link>
              .
            </Text>
          </Section>
        </Section>

        {/* Divider */}
        <Hr style={hr} />

        {/* Footer */}
        <Section style={footer}>
          <Text style={footerText}>
            This email was sent from a secure AppList server. If you have any
            concerns about this email, please contact our support team
            immediately.
          </Text>
          <Text style={footerBrand}>© 2024 AppList. All rights reserved.</Text>
          <Text style={footerSecondary}>
            AppList Security Team • <EMAIL>
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

export default PasswordResetTemplate;

const main = {
  backgroundColor: "#f8fafc",
  fontFamily:
    '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  padding: "0",
  margin: "0",
};

const container = {
  backgroundColor: brandColors.background,
  border: `1px solid ${brandColors.border}`,
  borderRadius: "16px",
  margin: "40px auto",
  padding: "0",
  width: "600px",
  maxWidth: "600px",
  boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
};

const header = {
  background: `linear-gradient(135deg, ${brandColors.destructive} 0%, #ef4444 100%)`,
  borderRadius: "16px 16px 0 0",
  padding: "40px 48px 32px",
  textAlign: "center" as const,
};

const logoContainer = {
  textAlign: "center" as const,
};

const brandText = {
  color: brandColors.background,
  fontSize: "32px",
  fontWeight: "700",
  lineHeight: "1",
  margin: "0 0 8px 0",
  textAlign: "center" as const,
  letterSpacing: "-0.02em",
};

const tagline = {
  color: "rgba(255, 255, 255, 0.9)",
  fontSize: "16px",
  fontWeight: "400",
  lineHeight: "1.4",
  margin: "0",
  textAlign: "center" as const,
};

const content = {
  padding: "48px",
};

const alertSection = {
  textAlign: "center" as const,
  marginBottom: "32px",
};

const alertIcon = {
  fontSize: "48px",
  margin: "0 0 16px 0",
  display: "block",
};

const h1 = {
  color: brandColors.foreground,
  fontSize: "28px",
  fontWeight: "700",
  lineHeight: "1.2",
  margin: "0 0 16px 0",
  textAlign: "center" as const,
  letterSpacing: "-0.01em",
};

const leadText = {
  color: brandColors.mutedForeground,
  fontSize: "18px",
  lineHeight: "1.6",
  margin: "0",
  textAlign: "center" as const,
};

const emailDisplaySection = {
  backgroundColor: "#fef2f2",
  borderRadius: "12px",
  padding: "24px",
  marginBottom: "32px",
  textAlign: "center" as const,
  border: `2px solid ${brandColors.destructive}`,
};

const emailLabel = {
  color: brandColors.destructive,
  fontSize: "14px",
  fontWeight: "500",
  lineHeight: "1.4",
  margin: "0 0 8px 0",
  textTransform: "uppercase" as const,
  letterSpacing: "0.05em",
};

const emailValue = {
  color: brandColors.foreground,
  fontSize: "18px",
  fontWeight: "600",
  lineHeight: "1.4",
  margin: "0",
  wordBreak: "break-all" as const,
};

const ctaSection = {
  textAlign: "center" as const,
  marginBottom: "32px",
};

const text = {
  color: brandColors.foreground,
  fontSize: "16px",
  lineHeight: "1.6",
  margin: "0 0 24px 0",
  textAlign: "center" as const,
};

const buttonContainer = {
  textAlign: "center" as const,
};

const button = {
  background: `linear-gradient(135deg, ${brandColors.destructive} 0%, #ef4444 100%)`,
  border: "none",
  borderRadius: "12px",
  display: "inline-block",
  fontSize: "16px",
  fontWeight: "600",
  lineHeight: "1",
  padding: "16px 32px",
  textAlign: "center" as const,
  textDecoration: "none",
  boxShadow: `0 4px 12px rgba(215, 53, 39, 0.25)`,
  transition: "all 0.2s ease",
};

const buttonText = {
  color: brandColors.background,
  fontSize: "16px",
  fontWeight: "600",
  lineHeight: "1",
  margin: "0",
  textDecoration: "none",
};

const alternativeSection = {
  backgroundColor: brandColors.muted,
  borderRadius: "8px",
  padding: "20px",
  marginBottom: "32px",
};

const alternativeText = {
  color: brandColors.mutedForeground,
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0 0 12px 0",
  textAlign: "center" as const,
};

const link = {
  color: brandColors.destructive,
  fontSize: "14px",
  fontWeight: "500",
  textDecoration: "underline",
  wordBreak: "break-all" as const,
  display: "block",
  textAlign: "center" as const,
};

const securitySection = {
  backgroundColor: "#fffbeb",
  borderRadius: "12px",
  padding: "24px",
  marginBottom: "32px",
  border: `1px solid ${brandColors.warning}`,
};

const securityTitle = {
  color: brandColors.foreground,
  fontSize: "18px",
  fontWeight: "600",
  lineHeight: "1.4",
  margin: "0 0 16px 0",
  textAlign: "center" as const,
};

const securityItem = {
  display: "flex",
  alignItems: "flex-start",
  marginBottom: "12px",
};

const securityBullet = {
  fontSize: "16px",
  margin: "0 12px 0 0",
  display: "inline-block",
  minWidth: "24px",
};

const securityText = {
  color: brandColors.mutedForeground,
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0",
  flex: "1",
};

const helpSection = {
  backgroundColor: brandColors.secondary,
  borderRadius: "8px",
  padding: "20px",
  textAlign: "center" as const,
};

const helpTitle = {
  color: brandColors.foreground,
  fontSize: "16px",
  fontWeight: "600",
  lineHeight: "1.4",
  margin: "0 0 8px 0",
  textAlign: "center" as const,
};

const helpText = {
  color: brandColors.mutedForeground,
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0",
  textAlign: "center" as const,
};

const hr = {
  border: "none",
  borderTop: `1px solid ${brandColors.border}`,
  margin: "0",
};

const footer = {
  padding: "32px 48px",
  textAlign: "center" as const,
  backgroundColor: brandColors.muted,
  borderRadius: "0 0 16px 16px",
};

const footerText = {
  color: brandColors.mutedForeground,
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0 0 16px 0",
  textAlign: "center" as const,
};

const footerBrand = {
  color: brandColors.foreground,
  fontSize: "14px",
  fontWeight: "600",
  lineHeight: "1.4",
  margin: "0 0 8px 0",
  textAlign: "center" as const,
};

const footerSecondary = {
  color: brandColors.mutedForeground,
  fontSize: "13px",
  lineHeight: "1.4",
  margin: "0",
  textAlign: "center" as const,
};
