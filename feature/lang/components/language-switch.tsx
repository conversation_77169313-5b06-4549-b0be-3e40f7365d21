"use client";

import * as React from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { Globe } from "lucide-react";
import { setCookie } from "cookies-next";
import { defaultLocale, locales } from "@/constant/locale";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogTrigger,
} from "@/components/custom/custom-dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

interface Props {
  lang: string;
  dict?: any;
}

const LanguageSwitch = ({ lang, dict }: Props) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [open, setOpen] = React.useState(false);
  const [search, setSearch] = React.useState("");

  const currentLanguage =
    locales.find((l) => l.locale === lang)?.name || "English";
  const shortLangCode = getShortCode(lang);

  // Filter languages based on search
  const filteredLanguages = locales.filter(
    (l) =>
      l.name.toLowerCase().includes(search.toLowerCase()) ||
      l.locale.includes(search.toLowerCase())
  );

  function getShortCode(locale: string) {
    // For most languages, get first two characters
    if (locale === "zh-TW") return "中";
    if (locale === "zh") return "中";
    if (locale === "ar") return "ع";

    // For other languages, get first character and capitalize
    return locale.charAt(0).toUpperCase();
  }

  const setLanguage = (newLang: string) => {
    setCookie("NEXT_LOCALE", newLang, { maxAge: 31536000, path: "/" });
    document.documentElement.lang = newLang;
    document.documentElement.dir = newLang === "ar" ? "rtl" : "ltr";

    const queryString = searchParams.toString();
    const queryPart = queryString ? `?${queryString}` : "";

    const segments = pathname.split("/").filter(Boolean);

    if (segments[0] === lang) {
      segments.shift();
    }

    let newPath;
    if (segments.length === 0) {
      newPath = newLang === defaultLocale ? "/" : `/${newLang}`;
    } else {
      const pathWithoutLang = segments.join("/");
      newPath =
        newLang === defaultLocale
          ? `/${pathWithoutLang}`
          : `/${newLang}/${pathWithoutLang}`;
    }

    const fullPath = `${newPath}${queryPart}`;

    router.push(fullPath);
    window.location.href = fullPath;
    setOpen(false);
  };

  return (
    <CustomDialog open={open} onOpenChange={setOpen}>
      <CustomDialogTrigger asChild>
        <Button
          variant="ghost"
          className="flex items-center space-x-1 px-2 border shadow-sm"
        >
          <Globe className="h-4 w-4" />
          <span className="sm:hidden">{shortLangCode}</span>
          <span className="hidden sm:inline">{currentLanguage}</span>
        </Button>
      </CustomDialogTrigger>
      <CustomDialogContent className="max-w-sm md:max-w-md">
        <CustomDialogHeader>
          <CustomDialogTitle className="text-base">
            {dict?.language?.select_language || "Select language"}
          </CustomDialogTitle>
        </CustomDialogHeader>
        <div className="py-4 p-4">
          <Input
            placeholder={
              dict?.language?.search_language || "Search language..."
            }
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="mb-4"
          />
          <ScrollArea className="h-[300px]">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {filteredLanguages.map((language) => (
                <Button
                  key={language.locale}
                  variant="outline"
                  className={cn(
                    "justify-start text-left font-normal",
                    lang === language.locale &&
                      "bg-accent text-accent-foreground"
                  )}
                  onClick={() => setLanguage(language.locale)}
                >
                  {language.name}
                </Button>
              ))}
            </div>
          </ScrollArea>
        </div>
      </CustomDialogContent>
    </CustomDialog>
  );
};

export default LanguageSwitch;
