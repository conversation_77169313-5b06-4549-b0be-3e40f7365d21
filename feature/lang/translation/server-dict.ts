import "server-only";

const dictionaries: any = {
  en: () => import("./en.json").then((module) => module.default),
  es: () => import("./es.json").then((module) => module.default),
};

export const getServerDictionary = async (locale: string = "en") => {
  return (dictionaries[locale] || dictionaries["en"])();
};

export const getLanguageFromRequest = (request: Request): string => {
  let locale = "en";

  const referer = request.headers.get("referer");
  if (referer) {
    try {
      const url = new URL(referer);
      const pathSegments = url.pathname.split("/").filter(Boolean);
      const supportedLanguages = ["en", "es"];

      if (
        pathSegments.length > 0 &&
        supportedLanguages.includes(pathSegments[0])
      ) {
        locale = pathSegments[0];
      }
    } catch (error) {}
  }

  if (locale === "en") {
    const acceptLanguage = request.headers.get("accept-language");
    if (acceptLanguage) {
      const languages = acceptLanguage
        .split(",")
        .map((lang) => lang.split(";")[0].trim());
      const supportedLanguages = ["en", "es"];
      locale =
        languages.find((lang) => supportedLanguages.includes(lang)) || "en";
    }
  }

  return locale;
};

export const getDictionaryFromRequest = async (
  request: Request
): Promise<any> => {
  const locale = getLanguageFromRequest(request);
  return await getServerDictionary(locale);
};
