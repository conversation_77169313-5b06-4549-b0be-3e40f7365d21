/* TipTap Editor */

/* Base Editor Styles */
.tiptap-editor-prosemirror {
  outline: none !important;
  border: none !important;
  font-size: 15px;
  line-height: 1.7;
  color: var(--foreground);
  min-height: inherit;
  padding: 1.5rem;
  margin: 0;
  width: 100%;
  height: 100%;
}

.tiptap-editor-wrapper {
  width: 100%;
  height: 100%;
  min-height: inherit;
  cursor: text;
}

.tiptap-editor-wrapper .ProseMirror {
  outline: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100%;
  min-height: inherit;
}

/* Placeholder */
.tiptap-editor-prosemirror:empty:before {
  content: attr(data-placeholder);
  float: left;
  color: rgb(156 163 175);
  pointer-events: none;
  height: 0;
  font-style: italic;
  opacity: 0.6;
}

/* HEADINGS - Maximum specificity targeting */
.tiptap-editor-wrapper .ProseMirror h1,
.tiptap-editor-prosemirror h1 {
  font-size: 2.5rem !important;
  font-weight: 800 !important;
  line-height: 1.2 !important;
  margin: 2rem 0 1.5rem 0 !important;
  color: var(--foreground) !important;
  letter-spacing: -0.03em !important;
  display: block !important;
}

.tiptap-editor-wrapper .ProseMirror h2,
.tiptap-editor-prosemirror h2 {
  font-size: 2rem !important;
  font-weight: 700 !important;
  line-height: 1.3 !important;
  margin: 1.75rem 0 1.25rem 0 !important;
  color: var(--foreground) !important;
  letter-spacing: -0.02em !important;
  display: block !important;
}

.tiptap-editor-wrapper .ProseMirror h3,
.tiptap-editor-prosemirror h3 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin: 1.5rem 0 1rem 0 !important;
  color: var(--foreground) !important;
  letter-spacing: -0.01em !important;
  display: block !important;
}

/* PARAGRAPHS */
.tiptap-editor-wrapper .ProseMirror p,
.tiptap-editor-prosemirror p {
  margin: 1rem 0 !important;
  line-height: 1.7 !important;
  color: var(--foreground) !important;
  font-size: 15px !important;
}

/* TEXT FORMATTING */
.tiptap-editor-wrapper .ProseMirror strong,
.tiptap-editor-prosemirror strong {
  font-weight: 700 !important;
}

.tiptap-editor-wrapper .ProseMirror em,
.tiptap-editor-prosemirror em {
  font-style: italic !important;
}

.tiptap-editor-wrapper .ProseMirror u,
.tiptap-editor-prosemirror u {
  text-decoration: underline !important;
  text-underline-offset: 2px !important;
}

.tiptap-editor-wrapper .ProseMirror code,
.tiptap-editor-prosemirror code {
  background-color: rgb(243 244 246) !important;
  color: var(--primary) !important;
  padding: 0.2rem 0.4rem !important;
  border-radius: 0.375rem !important;
  font-family:
    ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo,
    monospace !important;
  font-size: 0.875em !important;
  font-weight: 500 !important;
}

.dark .tiptap-editor-wrapper .ProseMirror code,
.dark .tiptap-editor-prosemirror code {
  background-color: rgb(55 65 81) !important;
}

/* LISTS - Maximum specificity */
.tiptap-editor-wrapper .ProseMirror ul,
.tiptap-editor-prosemirror ul {
  list-style-type: disc !important;
  margin: 1rem 0 !important;
  padding-left: 1.5rem !important;
  display: block !important;
}

.tiptap-editor-wrapper .ProseMirror ol,
.tiptap-editor-prosemirror ol {
  list-style-type: decimal !important;
  margin: 1rem 0 !important;
  padding-left: 1.5rem !important;
  display: block !important;
}

.tiptap-editor-wrapper .ProseMirror li,
.tiptap-editor-prosemirror li {
  margin: 0.5rem 0 !important;
  line-height: 1.6 !important;
  display: list-item !important;
}

/* BLOCKQUOTE - Maximum specificity */
.tiptap-editor-wrapper .ProseMirror blockquote,
.tiptap-editor-prosemirror blockquote {
  border-left: 4px solid var(--primary) !important;
  margin: 1.5rem 0 !important;
  padding: 0.5rem 1.5rem !important;
  background: linear-gradient(
    135deg,
    rgb(249 250 251) 0%,
    rgb(243 244 246) 100%
  ) !important;
  border-radius: 0.75rem !important;
  font-style: italic !important;
  color: rgb(75 85 99) !important;
  position: relative !important;
  display: block !important;
}

.dark .tiptap-editor-wrapper .ProseMirror blockquote,
.dark .tiptap-editor-prosemirror blockquote {
  background: linear-gradient(
    135deg,
    rgb(17 24 39) 0%,
    rgb(31 41 55) 100%
  ) !important;
  color: rgb(156 163 175) !important;
}

/* LINKS */
.tiptap-editor-wrapper .ProseMirror a,
.tiptap-editor-prosemirror a,
.editor-link {
  color: var(--primary) !important;
  text-decoration: underline !important;
  text-underline-offset: 2px !important;
  cursor: pointer !important;
  transition: opacity 0.2s ease !important;
}

.tiptap-editor-wrapper .ProseMirror a:hover,
.tiptap-editor-prosemirror a:hover,
.editor-link:hover {
  opacity: 0.8 !important;
}

/* HORIZONTAL RULE */
.tiptap-editor-wrapper .ProseMirror hr,
.tiptap-editor-prosemirror hr {
  border: none !important;
  height: 3px !important;
  background: linear-gradient(
    90deg,
    transparent,
    var(--primary),
    transparent
  ) !important;
  margin: 2.5rem 0 !important;
  border-radius: 2px !important;
  display: block !important;
}

/* SELECTION */
.tiptap-editor-wrapper .ProseMirror ::selection,
.tiptap-editor-prosemirror ::selection {
  background: var(--primary) !important;
  color: white !important;
  text-shadow: none !important;
}

/* Selection for different elements to ensure white text */
.tiptap-editor-wrapper .ProseMirror h1::selection,
.tiptap-editor-wrapper .ProseMirror h2::selection,
.tiptap-editor-wrapper .ProseMirror h3::selection,
.tiptap-editor-wrapper .ProseMirror p::selection,
.tiptap-editor-wrapper .ProseMirror strong::selection,
.tiptap-editor-wrapper .ProseMirror em::selection,
.tiptap-editor-wrapper .ProseMirror u::selection,
.tiptap-editor-wrapper .ProseMirror li::selection,
.tiptap-editor-wrapper .ProseMirror blockquote::selection,
.tiptap-editor-wrapper .ProseMirror a::selection,
.tiptap-editor-prosemirror h1::selection,
.tiptap-editor-prosemirror h2::selection,
.tiptap-editor-prosemirror h3::selection,
.tiptap-editor-prosemirror p::selection,
.tiptap-editor-prosemirror strong::selection,
.tiptap-editor-prosemirror em::selection,
.tiptap-editor-prosemirror u::selection,
.tiptap-editor-prosemirror li::selection,
.tiptap-editor-prosemirror blockquote::selection,
.tiptap-editor-prosemirror a::selection {
  background: var(--primary) !important;
  color: white !important;
  text-shadow: none !important;
}

/* PREVIEW MODE */
.tiptap-preview-content h1 {
  font-size: 2.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin: 2rem 0 1.5rem 0;
}
.tiptap-preview-content h2 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.3;
  margin: 1.75rem 0 1.25rem 0;
}
.tiptap-preview-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  margin: 1.5rem 0 1rem 0;
}
.tiptap-preview-content p {
  margin: 1rem 0;
  line-height: 1.7;
}
.tiptap-preview-content ul {
  list-style-type: disc;
  margin: 1rem 0;
  padding-left: 1.5rem;
}
.tiptap-preview-content ol {
  list-style-type: decimal;
  margin: 1rem 0;
  padding-left: 1.5rem;
}
.tiptap-preview-content li {
  margin: 0.5rem 0;
  line-height: 1.6;
}
.tiptap-preview-content blockquote {
  border-left: 4px solid var(--primary);
  margin: 1rem 0;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(
    135deg,
    rgb(249 250 251) 0%,
    rgb(243 244 246) 100%
  );
  border-radius: 0.75rem;
  font-style: italic;
  color: rgb(75 85 99);
}
.tiptap-preview-content code {
  background-color: rgb(243 244 246);
  color: var(--primary);
  padding: 0.2rem 0.4rem;
  border-radius: 0.375rem;
  font-family: monospace;
  font-size: 0.875em;
}
.tiptap-preview-content a {
  color: var(--primary);
  text-decoration: underline;
  text-underline-offset: 2px;
}
.tiptap-preview-content hr {
  border: none;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--primary), transparent);
  margin: 2.5rem 0;
  border-radius: 2px;
}
