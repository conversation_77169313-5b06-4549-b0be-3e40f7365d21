"use client";

import React, { useRef, useEffect, useState, useCallback } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Underline from "@tiptap/extension-underline";
import Link from "@tiptap/extension-link";
import CharacterCount from "@tiptap/extension-character-count";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Code,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Link as LinkIcon,
  Quote,
  Minus,
  RotateCcw,
  Eye,
  EyeOff,
  Type,
  Sparkles,
} from "lucide-react";

interface TipTapEditorCompleteProps {
  content?: string;
  onChange?: (content: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: string;
  showWordCount?: boolean;
  characterLimit?: number;
  showPreview?: boolean;
}

export function TipTapEditorComplete({
  content = "",
  onChange,
  placeholder = "Start typing...",
  className,
  minHeight = "200px",
  showWordCount = true,
  characterLimit,
  showPreview = false,
}: TipTapEditorCompleteProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isToolbarVisible, setIsToolbarVisible] = useState(false);
  const [toolbarPosition, setToolbarPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const hideTimeoutRef = useRef<NodeJS.Timeout>(null);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        bulletList: {},
        orderedList: {},
        listItem: {},
        blockquote: {},
        bold: {},
        italic: {},
        code: {},
        paragraph: {},
        horizontalRule: {},
        history: {},
      }),
      Underline,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: "editor-link",
        },
      }),
      ...(characterLimit || showWordCount
        ? [
            CharacterCount.configure({
              limit: characterLimit,
            }),
          ]
        : []),
    ],
    content,
    editorProps: {
      attributes: {
        class: "tiptap-editor-prosemirror",
        "data-placeholder": placeholder,
      },
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange?.(html);

      if (editor.isFocused) {
        showToolbar();
      }
    },
    onFocus: () => {
      showToolbar();
    },
    onBlur: ({ event }) => {
      const relatedTarget = event?.relatedTarget as HTMLElement;
      if (relatedTarget && relatedTarget.closest("[data-tiptap-toolbar]")) {
        return;
      }

      hideTimeoutRef.current = setTimeout(() => {
        setIsToolbarVisible(false);
      }, 200);
    },
    onSelectionUpdate: ({ editor }) => {
      if (editor.isFocused) {
        showToolbar();
      }
    },
  });

  // Calculate toolbar position
  const calculateToolbarPosition = useCallback(() => {
    if (!editorRef.current) return null;

    const editorRect = editorRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let x = editorRect.left + editorRect.width / 2;
    let y = editorRect.bottom + 16;

    const toolbarWidth = 650;
    const toolbarHeight = 60;

    if (x - toolbarWidth / 2 < 20) {
      x = toolbarWidth / 2 + 20;
    } else if (x + toolbarWidth / 2 > viewportWidth - 20) {
      x = viewportWidth - toolbarWidth / 2 - 20;
    }

    if (y + toolbarHeight > viewportHeight - 20) {
      y = editorRect.top - toolbarHeight - 16;
    }

    return { x, y };
  }, []);

  const showToolbar = useCallback(() => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }

    const position = calculateToolbarPosition();
    if (position) {
      setToolbarPosition(position);
      setIsToolbarVisible(true);
    }
  }, [calculateToolbarPosition]);

  const hideToolbar = useCallback(() => {
    setIsToolbarVisible(false);
    setToolbarPosition(null);
  }, []);

  // Update editor content when content prop changes
  useEffect(() => {
    if (editor && content !== undefined) {
      const currentContent = editor.getHTML();
      if (currentContent !== content) {
        editor.commands.setContent(content || "", false);
      }
    }
  }, [editor, content]);

  useEffect(() => {
    const updatePosition = () => {
      if (isToolbarVisible && editor?.isFocused) {
        const position = calculateToolbarPosition();
        if (position) {
          setToolbarPosition(position);
        }
      }
    };

    window.addEventListener("scroll", updatePosition, { passive: true });
    window.addEventListener("resize", updatePosition, { passive: true });

    return () => {
      window.removeEventListener("scroll", updatePosition);
      window.removeEventListener("resize", updatePosition);
    };
  }, [isToolbarVisible, editor?.isFocused, calculateToolbarPosition]);

  // Toolbar configuration
  const toolbarButtons: Array<{
    key: string;
    icon?: React.ComponentType<{ className?: string }>;
    label?: string;
    isActive?: () => boolean;
    onClick?: () => void;
    isDivider?: boolean;
  }> = [
    {
      key: "bold",
      icon: Bold,
      label: "Bold",
      isActive: () => editor?.isActive("bold") || false,
      onClick: () => editor?.chain().focus().toggleBold().run(),
    },
    {
      key: "italic",
      icon: Italic,
      label: "Italic",
      isActive: () => editor?.isActive("italic") || false,
      onClick: () => editor?.chain().focus().toggleItalic().run(),
    },
    {
      key: "underline",
      icon: UnderlineIcon,
      label: "Underline",
      isActive: () => editor?.isActive("underline") || false,
      onClick: () => editor?.chain().focus().toggleUnderline().run(),
    },
    {
      key: "code",
      icon: Code,
      label: "Inline Code",
      isActive: () => editor?.isActive("code") || false,
      onClick: () => editor?.chain().focus().toggleCode().run(),
    },
    {
      key: "divider-1",
      isDivider: true,
    },
    {
      key: "h1",
      icon: Heading1,
      label: "Heading 1",
      isActive: () => editor?.isActive("heading", { level: 1 }) || false,
      onClick: () => {
        if (editor?.isActive("heading", { level: 1 })) {
          editor?.chain().focus().setParagraph().run();
        } else {
          editor?.chain().focus().toggleHeading({ level: 1 }).run();
        }
      },
    },
    {
      key: "h2",
      icon: Heading2,
      label: "Heading 2",
      isActive: () => editor?.isActive("heading", { level: 2 }) || false,
      onClick: () => {
        if (editor?.isActive("heading", { level: 2 })) {
          editor?.chain().focus().setParagraph().run();
        } else {
          editor?.chain().focus().toggleHeading({ level: 2 }).run();
        }
      },
    },
    {
      key: "h3",
      icon: Heading3,
      label: "Heading 3",
      isActive: () => editor?.isActive("heading", { level: 3 }) || false,
      onClick: () => {
        if (editor?.isActive("heading", { level: 3 })) {
          editor?.chain().focus().setParagraph().run();
        } else {
          editor?.chain().focus().toggleHeading({ level: 3 }).run();
        }
      },
    },
    {
      key: "divider-2",
      isDivider: true,
    },
    {
      key: "bulletList",
      icon: List,
      label: "Bullet List",
      isActive: () => editor?.isActive("bulletList") || false,
      onClick: () => editor?.chain().focus().toggleBulletList().run(),
    },
    {
      key: "orderedList",
      icon: ListOrdered,
      label: "Numbered List",
      isActive: () => editor?.isActive("orderedList") || false,
      onClick: () => editor?.chain().focus().toggleOrderedList().run(),
    },
    {
      key: "blockquote",
      icon: Quote,
      label: "Blockquote",
      isActive: () => editor?.isActive("blockquote") || false,
      onClick: () => editor?.chain().focus().toggleBlockquote().run(),
    },
    {
      key: "divider-3",
      isDivider: true,
    },
    // {
    //   key: "link",
    //   icon: LinkIcon,
    //   label: "Link",
    //   isActive: () => editor?.isActive("link") || false,
    //   onClick: () => {
    //     const url = window.prompt("Enter URL:");
    //     if (url) {
    //       editor?.chain().focus().setLink({ href: url }).run();
    //     }
    //   },
    // },
    {
      key: "horizontalRule",
      icon: Minus,
      label: "Horizontal Line",
      isActive: () => false,
      onClick: () => editor?.chain().focus().setHorizontalRule().run(),
    },
    {
      key: "clear",
      icon: RotateCcw,
      label: "Clear Formatting",
      isActive: () => false,
      onClick: () => editor?.chain().focus().clearNodes().unsetAllMarks().run(),
    },
  ];

  // Statistics
  const getWordCount = () => {
    if (!editor) return 0;
    return editor.storage.characterCount?.words() || 0;
  };

  const getCharacterCount = () => {
    if (!editor) return 0;
    return editor.storage.characterCount?.characters() || 0;
  };

  const isCharacterLimitExceeded = () => {
    return characterLimit ? getCharacterCount() > characterLimit : false;
  };

  const getCharacterLimitProgress = () => {
    if (!characterLimit) return 0;
    return Math.min((getCharacterCount() / characterLimit) * 100, 100);
  };

  useEffect(() => {
    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, []);

  if (!editor) {
    return (
      <div className="flex items-center justify-center h-48 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3 text-gray-500 dark:text-gray-400">
          <Sparkles className="w-5 h-5 animate-pulse" />
          <span className="font-medium">Initializing editor...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Ultra-Modern Editor Container */}
      <div
        ref={editorRef}
        className={cn(
          "group relative overflow-hidden rounded-2xl bg-white dark:bg-gray-900 transition-all duration-300",
          "border border-gray-200 dark:border-gray-700",
          "focus-within:ring-1 focus-within:ring-slate-500/20 focus-within:border-primary/50",
          "hover:border-gray-300 dark:hover:border-gray-600",
          isCharacterLimitExceeded() &&
            "border-primary/60 focus-within:border-primary focus-within:ring-primary/30",
          className
        )}
        style={{ minHeight }}
      >
        {/* Minimalist Header */}
        <div className="flex items-center justify-between px-6 py-2 bg-gradient-to-r from-gray-50/80 via-white to-gray-50/80 dark:from-gray-800/80 dark:via-gray-900 dark:to-gray-800/80 border-b border-gray-200/50 dark:border-gray-700/50">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-1.5">
              <div className="w-3 h-3 rounded-full bg-primary/20"></div>
              <div className="w-3 h-3 rounded-full bg-primary/40"></div>
              <div className="w-3 h-3 rounded-full bg-primary"></div>
            </div>
            <div className="w-px h-5 bg-gray-300 dark:bg-gray-600"></div>
            <Type className="w-4 h-4 text-primary/60" />
          </div>

          {showPreview && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsPreviewMode(!isPreviewMode)}
              className="h-8 px-4 text-xs font-medium transition-all duration-200 hover:bg-primary/5 hover:text-primary border border-gray-200 hover:border-primary/20"
            >
              {isPreviewMode ? (
                <>
                  <EyeOff className="w-3.5 h-3.5 mr-2" />
                  Edit Mode
                </>
              ) : (
                <>
                  <Eye className="w-3.5 h-3.5 mr-2" />
                  Preview
                </>
              )}
            </Button>
          )}
        </div>

        {/* Editor Content Area */}
        <div className="relative">
          {isPreviewMode ? (
            <div
              className="p-6 tiptap-preview-content"
              style={{ minHeight }}
              dangerouslySetInnerHTML={{ __html: editor.getHTML() }}
            />
          ) : (
            <div
              className="relative w-full cursor-text px-4"
              style={{ minHeight }}
              onClick={(e) => {
                // Focus the editor when clicking anywhere in the editor area
                editor.commands.focus();
                showToolbar();
              }}
            >
              <EditorContent
                editor={editor}
                className="tiptap-editor-wrapper w-full h-full"
              />
            </div>
          )}
        </div>
      </div>

      {/* Modern Statistics Bar */}
      {showWordCount && (
        <div className="flex items-center justify-between px-2">
          <div className="flex items-center space-x-4">
            <Badge
              variant="secondary"
              className="text-xs font-medium bg-primary/5 text-primary border-primary/20"
            >
              <Type className="w-3 h-3 mr-1.5" />
              {getWordCount()} words
            </Badge>
            <Badge
              variant="secondary"
              className={cn(
                "text-xs font-medium border",
                isCharacterLimitExceeded()
                  ? "bg-primary/10 text-primary border-primary/30"
                  : "bg-primary/5 text-primary border-primary/20"
              )}
            >
              {getCharacterCount()}
              {characterLimit && `/${characterLimit}`} characters
            </Badge>

            {characterLimit && (
              <div className="flex items-center space-x-3">
                <div className="w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className={cn(
                      "h-full rounded-full transition-all duration-500",
                      isCharacterLimitExceeded()
                        ? "bg-primary"
                        : getCharacterLimitProgress() > 80
                          ? "bg-primary/70"
                          : "bg-primary/50"
                    )}
                    style={{ width: `${getCharacterLimitProgress()}%` }}
                  />
                </div>
                {isCharacterLimitExceeded() && (
                  <span className="text-xs font-medium text-primary">
                    Limit exceeded
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Premium Floating Toolbar */}
      {isToolbarVisible && toolbarPosition && !isPreviewMode && (
        <div
          data-tiptap-toolbar
          className="fixed z-[9999] flex items-center gap-1 bg-gray-50 dark:bg-gray-900/95 backdrop-blur-xl border border-gray-300 rounded-2xl  px-2 py-2 animate-in fade-in-0 zoom-in-95 duration-300 "
          style={{
            left: `${toolbarPosition.x}px`,
            top: `${toolbarPosition.y}px`,
            transform: "translateX(-50%)",
          }}
          onMouseDown={(e) => {
            e.preventDefault();
          }}
        >
          {toolbarButtons.map((button, index) => {
            if (button.isDivider) {
              return (
                <div
                  key={button.key}
                  className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2 rounded-full"
                />
              );
            }

            const Icon = button.icon!;
            const isActive = button.isActive?.() || false;

            return (
              <Button
                key={button.key}
                type="button"
                variant="ghost"
                size="sm"
                className={cn(
                  "h-10 w-10 p-0 transition-all duration-200 hover:scale-105 rounded-xl",
                  isActive
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "hover:bg-primary/5 hover:text-primary text-gray-600 dark:text-gray-400"
                )}
                onClick={(e) => {
                  e.preventDefault();
                  button.onClick?.();
                }}
                onMouseDown={(e) => {
                  e.preventDefault();
                }}
                title={button.label}
              >
                <Icon className="h-4 w-4" />
              </Button>
            );
          })}
        </div>
      )}
    </div>
  );
}
