import { create } from "zustand";
import { ToolListingItem, ToolListingResponse } from "@/app/api/(public)/v1/tool/route";

interface ToolListingStore {
  // Tool listing data
  tools: ToolListingItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  } | null;

  // Loading states
  isLoading: boolean;
  error: string | null;

  // Language preference
  language: string;

  // UI state
  currentPage: number;

  // Actions
  setTools: (response: ToolListingResponse) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setLanguage: (language: string) => void;
  setCurrentPage: (page: number) => void;

  // Reset state
  resetState: () => void;
}

const initialState = {
  tools: [],
  pagination: null,
  isLoading: false,
  error: null,
  language: "en",
  currentPage: 1,
};

export const useToolListingStore = create<ToolListingStore>((set) => ({
  ...initialState,

  setTools: (response) => set({ 
    tools: response.tools, 
    pagination: response.pagination 
  }),
  setIsLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  setLanguage: (language) => set({ language }),
  setCurrentPage: (page) => set({ currentPage: page }),

  resetState: () => set(initialState),
}));

// Selector hooks for better performance
export const useToolListingData = () => {
  const { tools, pagination, isLoading, error } = useToolListingStore();
  return { tools, pagination, isLoading, error };
};

export const useToolListingUI = () => {
  const { language, currentPage } = useToolListingStore();
  return { language, currentPage };
};

export const useToolListingActions = () => {
  const {
    setTools,
    setIsLoading,
    setError,
    setLanguage,
    setCurrentPage,
    resetState,
  } = useToolListingStore();
  return {
    setTools,
    setIsLoading,
    setError,
    setLanguage,
    setCurrentPage,
    resetState,
  };
}; 