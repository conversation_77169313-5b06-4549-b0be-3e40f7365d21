import { create } from "zustand";
import { ToolResponse, ReviewResponse, CreateReviewRequest } from "@/types/tool-api";

interface ToolViewStore {
  // Current tool data
  tool: ToolResponse | null;

  // Loading states
  isLoading: boolean;
  error: string | null;

  // UI state
  activeTab: "overview" | "features" | "pricing" | "faqs" | "reviews";
  showFullScreenshots: boolean;
  currentScreenshotIndex: number;

  // Language preference
  language: string;

  // Review state (pagination and form only)
  currentReviewPage: number;
  reviewsPerPage: number;
  selectedRatingFilter: number | null;
  isReviewDialogOpen: boolean;
  reviewFormData: CreateReviewRequest;

  // Actions
  setTool: (tool: ToolResponse | null) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setActiveTab: (
    tab: "overview" | "features" | "pricing" | "faqs" | "reviews"
  ) => void;
  setShowFullScreenshots: (show: boolean) => void;
  setCurrentScreenshotIndex: (index: number) => void;
  setLanguage: (language: string) => void;

  // Review actions
  setCurrentReviewPage: (page: number) => void;
  setSelectedRatingFilter: (rating: number | null) => void;
  setIsReviewDialogOpen: (open: boolean) => void;
  setReviewFormData: (data: Partial<CreateReviewRequest>) => void;
  resetReviewForm: () => void;

  // Reset state
  resetState: () => void;
}

const initialReviewFormData: CreateReviewRequest = {
  tool_id: "",
  rating: 5,
  content: "",
};

const initialState = {
  tool: null,
  isLoading: false,
  error: null,
  activeTab: "overview" as const,
  showFullScreenshots: false,
  currentScreenshotIndex: 0,
  language: "en",
  // Review state
  currentReviewPage: 1,
  reviewsPerPage: 10,
  selectedRatingFilter: null,
  isReviewDialogOpen: false,
  reviewFormData: initialReviewFormData,
};

export const useToolViewStore = create<ToolViewStore>((set) => ({
  ...initialState,

  setTool: (tool) => set({ tool }),
  setIsLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  setActiveTab: (tab) => set({ activeTab: tab }),
  setShowFullScreenshots: (show) => set({ showFullScreenshots: show }),
  setCurrentScreenshotIndex: (index) => set({ currentScreenshotIndex: index }),
  setLanguage: (language) => set({ language }),

  // Review actions
  setCurrentReviewPage: (page) => set({ currentReviewPage: page }),
  setSelectedRatingFilter: (rating) => set({ selectedRatingFilter: rating }),
  setIsReviewDialogOpen: (open) => set({ isReviewDialogOpen: open }),
  setReviewFormData: (data) =>
    set((state) => ({
      reviewFormData: { ...state.reviewFormData, ...data },
    })),
  resetReviewForm: () => set({ reviewFormData: initialReviewFormData }),

  resetState: () => set(initialState),
}));

// Selector hooks for better performance
export const useToolViewData = () => {
  const { tool, isLoading, error } = useToolViewStore();
  return { tool, isLoading, error };
};

export const useToolViewUI = () => {
  const { activeTab, showFullScreenshots, currentScreenshotIndex, language } =
    useToolViewStore();
  return {
    activeTab,
    showFullScreenshots,
    currentScreenshotIndex,
    language,
  };
};

export const useToolViewActions = () => {
  const {
    setTool,
    setIsLoading,
    setError,
    setActiveTab,
    setShowFullScreenshots,
    setCurrentScreenshotIndex,
    setLanguage,
    resetState,
  } = useToolViewStore();
  return {
    setTool,
    setIsLoading,
    setError,
    setActiveTab,
    setShowFullScreenshots,
    setCurrentScreenshotIndex,
    setLanguage,
    resetState,
  };
};

// Review selector hooks
export const useToolReviewState = () => {
  const {
    currentReviewPage,
    reviewsPerPage,
    selectedRatingFilter,
    isReviewDialogOpen,
    reviewFormData,
  } = useToolViewStore();
  return {
    currentReviewPage,
    reviewsPerPage,
    selectedRatingFilter,
    isReviewDialogOpen,
    reviewFormData,
  };
};

export const useToolReviewActions = () => {
  const {
    setCurrentReviewPage,
    setSelectedRatingFilter,
    setIsReviewDialogOpen,
    setReviewFormData,
    resetReviewForm,
  } = useToolViewStore();
  return {
    setCurrentReviewPage,
    setSelectedRatingFilter,
    setIsReviewDialogOpen,
    setReviewFormData,
    resetReviewForm,
  };
}; 