import { useApiQuery, apiGet } from "@/feature/core/api/api-utils";
import { ToolListingResponse } from "@/app/api/(public)/v1/tool/route";

// Tool listing API functions for client-side
const clientToolListingApi = {
  getTools: async (
    page?: number,
    language?: string
  ): Promise<ToolListingResponse> => {
    const searchParams = new URLSearchParams();
    if (page !== undefined) searchParams.set("page", page.toString());
    if (language) searchParams.set("lang", language);

    const queryString = searchParams.toString();
    const response = await apiGet<{ data: ToolListingResponse }>(
      `/api/v1/tool${queryString ? `?${queryString}` : ""}`
    );
    return response.data;
  },
};

// Hook to get tool listing with pagination
export function useGetToolListing(
  page?: number,
  language?: string,
  enabled?: boolean
) {
  return useApiQuery(
    [
      "tool-listing",
      page?.toString() || "1",
      language || "en",
    ],
    () => clientToolListingApi.getTools(page, language),
    {
      enabled: enabled !== undefined ? enabled : true,
      staleTime: 10 * 60 * 1000, // 10 minutes cache for public content
    }
  );
}

// Hook to get tools with automatic pagination management
export function useToolListingWithPagination(language?: string) {
  const page = 1; // Start with page 1, can be managed by store
  
  const query = useGetToolListing(page, language);
  
  return {
    ...query,
    tools: query.data?.tools || [],
    pagination: query.data?.pagination || null,
  };
} 