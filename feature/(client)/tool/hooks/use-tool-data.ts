import { useApiQuery, useApiMutation, apiGet, apiPost, ApiError } from "@/feature/core/api/api-utils";
import {
  ToolResponse,
  ReviewResponse,
  CreateReviewRequest,
  PaginatedResponse,
  CreateReviewVoteRequest,
  ReviewVoteStatsResponse,
  VoteType,
} from "@/types/tool-api";
import { toast } from "sonner";

// Tool API functions for client-side
const clientToolApi = {
  getToolBySlug: async (
    slug: string,
    language?: string,
    reviewPage?: number,
    reviewLimit?: number,
    reviewRating?: number
  ): Promise<ToolResponse> => {
    const searchParams = new URLSearchParams();
    if (language) searchParams.set("lang", language);
    if (reviewPage !== undefined)
      searchParams.set("review-page", reviewPage.toString());
    if (reviewLimit !== undefined)
      searchParams.set("review-limit", reviewLimit.toString());
    if (reviewRating !== undefined)
      searchParams.set("review-rating", reviewRating.toString());

    const queryString = searchParams.toString();
    const response = await apiGet<{ data: ToolResponse }>(
      `/api/v1/tool/${slug}${queryString ? `?${queryString}` : ""}`
    );
    return response.data;
  },
};

// Review API functions for client-side (keeping vote functionality)
const clientReviewApi = {
  createReview: async (data: CreateReviewRequest): Promise<ReviewResponse> => {
    const response = await apiPost<{ data: ReviewResponse }>(
      "/api/v1/tool/review",
      data
    );
    return response.data;
  },

  voteOnReview: async (
    data: CreateReviewVoteRequest
  ): Promise<ReviewVoteStatsResponse> => {
    const response = await apiPost<{ data: ReviewVoteStatsResponse }>(
      "/api/v1/tool/review/vote",
      data
    );
    return response.data;
  },

  getReviewVoteStats: async (
    review_id: string
  ): Promise<ReviewVoteStatsResponse> => {
    const response = await apiGet<{ data: ReviewVoteStatsResponse }>(
      `/api/v1/tool/review/vote?review_id=${review_id}`
    );
    return response.data;
  },
};

// Tool Save API functions for client-side
const clientToolSaveApi = {
  saveUnsaveTool: async (
    slug: string
  ): Promise<{
    action: "saved" | "unsaved";
    saved_count: number;
    is_saved: boolean;
  }> => {
    const response = await apiPost<{
      data: {
        action: "saved" | "unsaved";
        saved_count: number;
        is_saved: boolean;
      };
    }>(`/api/v1/tool/${slug}/save`, {});
    return response.data;
  },
};

// Hook to get tool by slug (for client-side public access)
export function useGetToolBySlug(
  slug: string,
  language?: string,
  reviewPage?: number,
  reviewLimit?: number,
  reviewRating?: number,
  enabled?: boolean
) {
  return useApiQuery(
    [
      "client-tool",
      slug,
      language || "en",
      reviewPage?.toString() || "1",
      reviewLimit?.toString() || "10",
      reviewRating?.toString() || "all",
    ],
    () =>
      clientToolApi.getToolBySlug(
        slug,
        language,
        reviewPage,
        reviewLimit,
        reviewRating
      ),
    {
      enabled: enabled !== undefined ? enabled : !!slug,
      staleTime: 10 * 60 * 1000, // 10 minutes cache for public content
    }
  );
}

// Hook to get tool translation content
export function useGetToolTranslation(
  tool: ToolResponse | undefined,
  language: string
) {
  if (!tool) return null;

  const translation = tool.translations?.find(
    (t) => t.language_code === language
  );
  return translation || null;
}

// Hook to get tool categories with translations
export function useGetToolCategories(
  tool: ToolResponse | undefined,
  language: string
) {
  if (!tool?.categories) return [];

  return tool.categories.map((category) => {
    const translation = category.translations?.find(
      (t) => t.language_code === language
    );
    return {
      ...category,
      displayName: translation?.name || category.name,
      description: translation?.description || null,
    };
  });
}

// Hook to get tool tags with translations
export function useGetToolTags(
  tool: ToolResponse | undefined,
  language: string
) {
  if (!tool?.tags) return [];

  return tool.tags.map((tag) => {
    const translation = tag.translations?.find(
      (t) => t.language_code === language
    );
    return {
      ...tag,
      displayName: translation?.name || tag.name,
      description: translation?.description || null,
    };
  });
}

// Hook to get tool FAQs with translations
export function useGetToolFAQs(
  tool: ToolResponse | undefined,
  language: string
) {
  if (!tool?.faqs) return [];

  return tool.faqs
    .map((faq) => {
      const translation = faq.translations?.find(
        (t) => t.language_code === language
      );
      return {
        ...faq,
        question: translation?.question || "",
        answer: translation?.answer || "",
      };
    })
    .filter((faq) => faq.question && faq.answer);
}

// Hook to get reviews from tool data (replaces the separate review API)
export function useGetToolReviews(params: {
  tool_id?: string;
  rating?: number;
  page?: number;
  limit?: number;
}) {
  // This hook is now deprecated - use useGetToolBySlug with review parameters instead
  // Keeping for backward compatibility but will be removed
  return useApiQuery(
    ["client-tool-reviews-deprecated", JSON.stringify(params)],
    () =>
      Promise.resolve({
        items: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          total_pages: 0,
          has_next: false,
          has_prev: false,
        },
      }),
    {
      enabled: false, // Disabled - should use the new consolidated API
      staleTime: 0,
    }
  );
}

// Hook to create a new review
export function useCreateToolReview() {
  return useApiMutation(clientReviewApi.createReview, {
    showErrorToast: false,
    onSuccess: (data) => {
      toast.success(
        "Review submitted successfully! It will be published after admin approval."
      );
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to submit review");
    },
    invalidateQueries: ["client-tool"], // Invalidate tool queries to refresh review data
  });
}

// Hook to vote on a review
export function useVoteOnReview() {
  return useApiMutation(
    (data: CreateReviewVoteRequest) => clientReviewApi.voteOnReview(data),
    {
      onSuccess: (data, variables) => {
        let message = "";
        if (data.user_vote === VoteType.HELPFUL) {
          message = "👍 Marked as helpful";
        } else if (data.user_vote === VoteType.UNHELPFUL) {
          message = "👎 Marked as unhelpful";
        } else {
          message = "✨ Vote removed";
        }
        toast.success(message);
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to vote on review");
      },
    }
  );
}

// Hook to get vote stats for a review
export function useGetReviewVoteStats(review_id: string) {
  return useApiQuery(
    ["review-vote-stats", review_id],
    () => clientReviewApi.getReviewVoteStats(review_id),
    {
      enabled: !!review_id,
      staleTime: 30 * 1000, // 30 seconds cache
    }
  );
}

// Helper hook to calculate review statistics
export function useReviewStats(reviews: ReviewResponse[] | undefined) {
  if (!reviews || reviews.length === 0) {
    return {
      averageRating: 0,
      totalReviews: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
    };
  }

  const totalReviews = reviews.length;
  const ratingSum = reviews.reduce((sum, review) => sum + review.rating, 0);
  const averageRating = ratingSum / totalReviews;

  const ratingDistribution = reviews.reduce(
    (acc, review) => {
      acc[review.rating] = (acc[review.rating] || 0) + 1;
      return acc;
    },
    { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 } as Record<number, number>
  );

  return {
    averageRating,
    totalReviews,
    ratingDistribution,
  };
}

// Helper hook to format rating display
export function useFormatRating(rating: number) {
  return rating.toFixed(1);
}

// Hook to save/unsave tool
export function useSaveUnsaveTool() {
  return useApiMutation(
    async (
      slug: string
    ): Promise<{
      action: "saved" | "unsaved";
      saved_count: number;
      is_saved: boolean;
    }> => {
      return clientToolSaveApi.saveUnsaveTool(slug);
    },
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success(
          data.action === "saved"
            ? "Tool saved successfully"
            : "Tool unsaved successfully"
        );
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to save/unsave tool");
      },
      invalidateQueries: ["client-tool"],
    }
  );
} 