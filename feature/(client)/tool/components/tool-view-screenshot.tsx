"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Maximize2 } from "lucide-react";
import { 
  CustomDialog, 
  CustomDialogContent, 
  CustomDialogHeader, 
  CustomDialogTitle 
} from "@/components/custom/custom-dialog";

interface ToolViewScreenshotProps {
  screenshots: string[];
  toolName: string;
  dict: any;
}

const ToolViewScreenshot = ({ screenshots, toolName, dict }: ToolViewScreenshotProps) => {
  const toolView = dict.client.toolView;
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const [itemsPerView, setItemsPerView] = useState(4);

  // Skip the first screenshot as it's already shown as featured image (only for md and above)
  const availableScreenshots =
    itemsPerView === 1 ? screenshots : screenshots.slice(1);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setItemsPerView(4); // Desktop: 4 screenshots
      } else if (window.innerWidth >= 768) {
        setItemsPerView(3); // Tablet: 3 screenshots
      } else {
        setItemsPerView(1); // Mobile: 1 screenshot
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  if (!availableScreenshots || availableScreenshots.length === 0) {
    return null;
  }

  const maxIndex = Math.max(0, availableScreenshots.length - itemsPerView);

  const nextSlide = () => {
    setCurrentIndex((prev) => Math.min(prev + itemsPerView, maxIndex));
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => Math.max(prev - itemsPerView, 0));
  };

  const openModal = (index: number) => {
    setModalImageIndex(index);
    setShowModal(true);
  };

  const nextModalImage = () => {
    setModalImageIndex((prev) => (prev + 1) % availableScreenshots.length);
  };

  const prevModalImage = () => {
    setModalImageIndex(
      (prev) =>
        (prev - 1 + availableScreenshots.length) % availableScreenshots.length
    );
  };

  const getVisibleScreenshots = () => {
    return availableScreenshots.slice(
      currentIndex,
      currentIndex + itemsPerView
    );
  };

  const getGridClass = () => {
    if (itemsPerView === 1) return "grid-cols-1";
    if (itemsPerView === 3) return "grid-cols-3";
    return "grid-cols-4";
  };

  const getTotalPages = () => {
    return Math.ceil(availableScreenshots.length / itemsPerView);
  };

  const getCurrentPage = () => {
    return Math.floor(currentIndex / itemsPerView);
  };

  // Swipe functionality for mobile
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const minSwipeDistance = 50;

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (itemsPerView === 1) {
      if (isLeftSwipe && currentIndex < maxIndex) {
        nextSlide();
      }
      if (isRightSwipe && currentIndex > 0) {
        prevSlide();
      }
    }
  };

  return (
    <div className="w-full space-y-4">
      {/* Navigation Controls - Only show on md and larger screens */}
      {availableScreenshots.length > itemsPerView && (
        <div className="hidden md:flex justify-end">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={prevSlide}
              disabled={currentIndex === 0}
              className="h-8 w-8 bg-primary/10 hover:bg-primary/20 border-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="icon"
              onClick={nextSlide}
              disabled={currentIndex >= maxIndex}
              className="h-8 w-8 bg-primary/10 hover:bg-primary/20 border-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Screenshots Grid */}
      <div className="relative overflow-hidden">
        <div className="relative">
          <div
            className={`grid ${getGridClass()} gap-4 mb-4`}
            onTouchStart={onTouchStart}
            onTouchMove={onTouchMove}
            onTouchEnd={onTouchEnd}
          >
            {getVisibleScreenshots().map((screenshot, index) => (
              <div
                key={currentIndex + index}
                className="relative group cursor-pointer overflow-hidden rounded-lg"
                onClick={() => openModal(currentIndex + index)}
              >
                <img
                  src={screenshot}
                  alt={`${toolName} screenshot ${itemsPerView === 1 ? currentIndex + index + 1 : currentIndex + index + 2}`}
                  className="w-full h-44 md:h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Maximize2 className="h-6 w-6 text-white" />
                </div>
              </div>
            ))}
          </div>

          {/* Dots indicator - Only show when there are multiple pages/items */}
          {availableScreenshots.length > itemsPerView && (
            <div className="flex justify-center gap-2">
              {itemsPerView === 1
                ? // Mobile: Show dots for each screenshot
                  availableScreenshots.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentIndex(index)}
                      className={`w-2 h-2 rounded-full transition-all ${
                        index === currentIndex
                          ? "bg-primary"
                          : "bg-black/20 hover:bg-black/40"
                      }`}
                    />
                  ))
                : // Desktop/Tablet: Show dots for each page
                  Array.from({ length: getTotalPages() }).map(
                    (_, pageIndex) => (
                      <button
                        key={pageIndex}
                        onClick={() =>
                          setCurrentIndex(pageIndex * itemsPerView)
                        }
                        className={`w-2 h-2 rounded-full transition-all ${
                          pageIndex === getCurrentPage()
                            ? "bg-primary"
                            : "bg-black/20 hover:bg-black/40"
                        }`}
                      />
                    )
                  )}
            </div>
          )}
        </div>
      </div>

      {/* Modal for fullscreen view */}
      <CustomDialog open={showModal} onOpenChange={setShowModal}>
        <CustomDialogContent className="max-w-4xl max-h-[90vh] p-0">
          <CustomDialogHeader className="py-4">
            <CustomDialogTitle>
              {toolName} - {toolView.pics}{" "}
              {modalImageIndex + (itemsPerView === 1 ? 1 : 2)} of{" "}
              {screenshots.length}
            </CustomDialogTitle>
          </CustomDialogHeader>
          <div className="p-6 pt-0">
            <div className="relative">
              <img
                src={availableScreenshots[modalImageIndex]}
                alt={`${toolName} screenshot ${modalImageIndex + (itemsPerView === 1 ? 1 : 2)}`}
                className="w-full h-auto max-h-[70vh] object-contain rounded-lg mt-2"
              />

              {/* Navigation arrows in modal */}
              {availableScreenshots.length > 1 && (
                <>
                  <Button
                    variant="outline"
                    size="icon"
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white border-0"
                    onClick={prevModalImage}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="outline"
                    size="icon"
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white border-0"
                    onClick={nextModalImage}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </>
              )}
            </div>

            {/* Navigation dots in modal */}
            {availableScreenshots.length > 1 && (
              <div className="flex justify-center gap-2 mt-4">
                {availableScreenshots.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setModalImageIndex(index)}
                    className={`w-2 h-2 rounded-full transition-all ${
                      index === modalImageIndex
                        ? "bg-primary"
                        : "bg-black/20 hover:bg-black/40"
                    }`}
                  />
                ))}
              </div>
            )}
          </div>
        </CustomDialogContent>
      </CustomDialog>
    </div>
  );
};

export default ToolViewScreenshot;
