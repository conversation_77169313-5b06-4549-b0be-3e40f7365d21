"use client";

import React, { useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  ExternalLink,
  Globe,
  Twitter,
  Facebook,
  Linkedin,
  Github,
  Mail,
  AlertCircle,
  Calendar,
  Tag,
  Folder,
  DollarSign,
  Camera,
  X,
} from "lucide-react";
import {
  useGetToolBySlug,
  useGetToolTranslation,
  useGetToolCategories,
  useGetToolTags,
  useGetToolFAQs,
} from "../hooks/use-tool-data";
import {
  useToolViewActions,
  useToolViewData,
  useToolViewUI,
} from "../store/use-tool-view-store";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import Breadcrumb from "@/components/layout/breadcrumb";
import ToolViewHero from "./tool-view-hero";
import ToolViewSection from "./tool-view-section";

interface ToolViewProps {
  dict: any;
  lang: string;
  slug: string;
}

const ToolView = ({ dict, lang, slug }: ToolViewProps) => {
  const toolView = dict.client.toolView;
  // Always include review parameters with defaults to prevent double API calls
  const {
    data: tool,
    isLoading,
    error,
  } = useGetToolBySlug(
    slug,
    lang,
    1, // review page
    10, // review limit
    undefined // review rating filter
  );
  const { setTool, setIsLoading, setError, setLanguage } = useToolViewActions();
  const { activeTab, showFullScreenshots, currentScreenshotIndex } =
    useToolViewUI();
  const { setActiveTab, setShowFullScreenshots, setCurrentScreenshotIndex } =
    useToolViewActions();

  // Set language on mount
  useEffect(() => {
    setLanguage(lang);
  }, [lang, setLanguage]);

  // Update store when data changes
  useEffect(() => {
    if (tool) {
      setTool(tool);
    }
  }, [tool, setTool]);

  useEffect(() => {
    setIsLoading(isLoading);
  }, [isLoading, setIsLoading]);

  useEffect(() => {
    if (error) {
      setError(error instanceof Error ? error.message : "Failed to load tool");
    }
  }, [error, setError]);

  // Get translated content
  const translation = useGetToolTranslation(tool, lang);
  const categories = useGetToolCategories(tool, lang);
  const tags = useGetToolTags(tool, lang);
  const faqs = useGetToolFAQs(tool, lang);

  // Loading skeleton
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Skeleton className="h-8 w-3/4 mb-4" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-2/3 mb-8" />
            <Skeleton className="h-64 w-full" />
          </div>
          <div className="lg:col-span-1">
            <Skeleton className="h-80 w-full" />
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {dict?.common?.error || "An error occurred while loading the tool."}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Not found state
  if (!tool) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {dict?.tool?.notFound || "Tool not found."}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const displayName = translation?.name || tool.name;
  const description = translation?.short_description || "";
  const introduction = translation?.introduction || "";
  const whatIsIt = translation?.what_is_it || "";
  const howToUse = translation?.how_to_use || "";
  const fullFeature = translation?.full_feature || "";
  const shortFeature = translation?.short_feature || "";
  const pricingInfo = translation?.pricing || "";

  return (
    <div>
      <div className="mt-2">
        <Breadcrumb
          items={[
            {
              icon: "tool",
              text: toolView.tools,
              href: "/",
            },
          ]}
          lang={lang}
          dict={dict}
        />
        <div className="mt-6 max-w-7xl mx-auto px-2 md:px-0">
          <ToolViewHero
            tool={tool}
            translation={translation}
            categories={categories}
            tags={tags}
            dict={dict}
            lang={lang}
          />
        </div>

        <div className="mt-2 max-w-7xl mx-auto px-2 md:px-0">
          <ToolViewSection
            tool={tool}
            toolName={displayName}
            dict={dict}
            content={{
              introduction,
              whatIsIt,
              howToUse,
              fullFeature,
              shortFeature,
              pricingInfo,
            }}
            faqs={faqs}
            lang={lang}
          />
        </div>
      </div>
    </div>
  );
};

export default ToolView;
