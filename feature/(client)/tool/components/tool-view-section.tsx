"use client";

import React, { useState, useEffect, useRef } from "react";
import { cn } from "@/lib/utils";
import ToolViewScreenshot from "./tool-view-screenshot";
import ToolViewAbout from "./tool-view-about";
import { ToolViewReview } from "./tool-view-review";
import ToolViewEmbed from "./tool-view-embed";

interface ToolViewSectionProps {
  tool: any;
  toolName: string;
  dict: any;
  content: {
    introduction: string;
    whatIsIt: string;
    howToUse: string;
    fullFeature: string;
    shortFeature: string;
    pricingInfo: string;
  };
  faqs: any[];
  lang: string;
}

const ToolViewSection = ({
  tool,
  toolName,
  dict,
  content,
  faqs,
  lang,
}: ToolViewSectionProps) => {
  const toolView = dict.client.toolView;
  const [activeTab, setActiveTab] = useState("screenshot");
  const [isSticky, setIsSticky] = useState(false);
  const sectionRefs = useRef<{ [key: string]: HTMLElement | null }>({});
  const navRef = useRef<HTMLDivElement>(null);

  const tabs = [
    {
      id: "screenshot",
      label: `${toolName} ${toolView.pics}`,
      component: (
        <ToolViewScreenshot
          screenshots={tool.screenshot_urls || []}
          toolName={toolName}
          dict={dict}
        />
      ),
    },
    {
      id: "about",
      label: `${toolView.about} ${toolName}`,
      component: (
        <ToolViewAbout
          tool={tool}
          toolName={toolName}
          dict={dict}
          content={content}
          faqs={faqs}
        />
      ),
    },
    {
      id: "review",
      label: toolView.reviews,
      component: (
        <ToolViewReview
          tool={tool}
          toolSlug={tool.slug}
          toolName={toolName}
          dict={dict}
          lang={lang}
        />
      ),
    },
    {
      id: "embed",
      label: toolView.embedSoon,
      component: <ToolViewEmbed tool={tool} toolName={toolName} dict={dict} />,
    },
  ];

  // Scroll spy functionality
  useEffect(() => {
    const observers: IntersectionObserver[] = [];

    const handleIntersection = (entries: IntersectionObserverEntry[]) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const id = entry.target.id;
          setActiveTab(id);
        }
      });
    };

    // Create observers for each section
    tabs.forEach((tab) => {
      const element = sectionRefs.current[tab.id];
      if (element) {
        const observer = new IntersectionObserver(handleIntersection, {
          root: null,
          rootMargin: "-20% 0px -70% 0px", // Trigger when section is 20% from top
          threshold: 0,
        });
        observer.observe(element);
        observers.push(observer);
      }
    });

    return () => {
      observers.forEach((observer) => observer.disconnect());
    };
  }, [tabs]);

  // Sticky navigation functionality
  useEffect(() => {
    const handleScroll = () => {
      if (navRef.current) {
        const rect = navRef.current.getBoundingClientRect();
        setIsSticky(rect.top <= 0);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToSection = (tabId: string) => {
    const element = sectionRefs.current[tabId];
    if (element) {
      const navHeight = navRef.current?.offsetHeight || 0;
      const offsetTop = element.offsetTop - navHeight - 20; // 20px additional offset

      window.scrollTo({
        top: offsetTop,
        behavior: "smooth",
      });
    }
  };

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    scrollToSection(tabId);
  };

  return (
    <div className="w-full">
      {/* Sticky Tab Navigation */}
      <div
        ref={navRef}
        className={cn(
          "sticky top-16 z-40 bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800 transition-all duration-200",
          isSticky ? "shadow-md" : ""
        )}
      >
        <div className="max-w-7xl mx-auto">
          <div className="flex space-x-0 overflow-x-auto scrollbar-hide">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabClick(tab.id)}
                className={cn(
                  "relative flex-shrink-0 px-4 py-2 text-sm font-medium transition-all duration-200 ease-in-out hover:cursor-pointer rounded-t-lg",
                  "border-b-2 border-transparent",
                  "hover:text-primary hover:border-primary/30",
                  "focus:outline-none focus:text-primary focus:border-primary/30",
                  // Mobile responsive
                  "min-w-[100px] text-center",
                  // Tablet responsive
                  "sm:min-w-[120px] sm:px-6",
                  // Desktop responsive
                  "lg:min-w-[140px] lg:px-8",
                  activeTab === tab.id
                    ? "text-primary border-primary bg-primary/5"
                    : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                )}
              >
                {tab.label}
                {activeTab === tab.id && (
                  <div className="absolute inset-x-0 bottom-0 h-0.5 bg-primary rounded-t-full" />
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Sections Content */}
      <div className="max-w-7xl mx-auto">
        {tabs.map((tab) => (
          <section
            key={tab.id}
            id={tab.id}
            ref={(el) => {
              sectionRefs.current[tab.id] = el;
            }}
            className="py-4 sm:py-2 "
          >
            <div className="transition-all duration-300 ease-in-out">
              {tab.component}
            </div>
          </section>
        ))}
      </div>
    </div>
  );
};

export default ToolViewSection;
