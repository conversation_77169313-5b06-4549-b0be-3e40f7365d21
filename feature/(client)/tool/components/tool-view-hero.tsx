"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import {
  Calendar,
  ExternalLink,
  Mail,
  Tag,
  Folder,
  DollarSign,
  Send,
  Star,
  StarHalf,
  Bookmark,
  BookmarkCheck,
  Loader2,
} from "lucide-react";
import { FaTwitter, FaFacebook, FaLinkedin, FaGithub } from "react-icons/fa";
import { ToolResponse } from "@/types/tool-api";
import ToolViewScreenshot from "./tool-view-screenshot";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { useSaveUnsaveTool } from "../hooks/use-tool-data";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { getLocalizedUrl } from "@/lib/utils";

interface ToolViewHeroProps {
  tool: ToolResponse;
  translation: any;
  categories: any[];
  tags: any[];
  dict: any;
  lang: string;
}

const ToolViewHero = ({
  tool,
  translation,
  categories,
  tags,
  dict,
  lang,
}: ToolViewHeroProps) => {
  const toolView = dict.client.toolView;
  const displayName = translation?.name || tool.name;
  const description = translation?.short_description || "";
  const { data: session } = useSession();
  const router = useRouter();
  const saveUnsaveTool = useSaveUnsaveTool();

  // Handle save/unsave tool
  const handleSaveUnsave = () => {
    if (!session) {
      toast.error(toolView.pleaseLoginToSaveTools);
      router.push(getLocalizedUrl("/login", lang));
      return;
    }

    saveUnsaveTool.mutate(tool.slug);
  };

  // Function to render star rating
  const renderStarRating = (rating: number, totalReviews: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    // Full stars
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={`full-${i}`} className="w-4 h-4 fill-primary text-primary" />
      );
    }

    // Half star
    if (hasHalfStar) {
      stars.push(
        <StarHalf key="half" className="w-4 h-4 fill-primary text-primary" />
      );
    }

    // Empty stars
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-4 h-4 text-gray-300" />);
    }

    return (
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1">{stars}</div>
        <span className="text-sm font-medium text-gray-600">
          {rating.toFixed(1)} ({totalReviews}{" "}
          {totalReviews === 1 ? toolView.review : toolView.reviews})
        </span>
      </div>
    );
  };

  const socialLinks = [
    { url: tool.twitter_url, icon: FaTwitter, label: toolView.twitter },
    { url: tool.facebook_url, icon: FaFacebook, label: toolView.facebook },
    { url: tool.linkedin_url, icon: FaLinkedin, label: toolView.linkedin },
    { url: tool.github_url, icon: FaGithub, label: toolView.github },
  ];

  const contactLinks = [
    {
      url: tool.contact_email ? `mailto:${tool.contact_email}` : null,
      icon: Send,
    },
    {
      url: tool.support_email ? `mailto:${tool.support_email}` : null,
      icon: Mail,
    },
  ];

  return (
    <div className="w-full">
      <div>
        <div>
          {/* Header Section */}
          <div className="flex flex-col md:flex-row md:items-start gap-6 mb-4">
            {/* Logo and Title */}
            <div className="flex items-center gap-4">
              {tool.logo_url && (
                <div className="relative h-16 w-16 md:h-20 md:w-20 rounded-2xl overflow-hidden border-none">
                  <Image
                    src={tool.logo_url}
                    alt={displayName}
                    fill
                    className="object-cover"
                  />
                </div>
              )}
              <div>
                <div className="flex flex-col md:flex-row gap-1.5 md:gap-5">
                  <h1 className="text-3xl md:text-4xl font-bold mb-0 md:mb-2">
                    {displayName}
                  </h1>
                  <div className="flex items-center flex-row gap-1 md:gap-3">
                    {tool.website_url && (
                      <div className="flex-shrink-0">
                        <Link
                          href={`${tool.website_url}?utm_source=applist.com&utm_medium=referral&utm_campaign=tool-view`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center justify-center bg-primary hover:bg-primary/90 text-white px-2 md:px-4 py-1 md:py-2 rounded-lg font-semibold text-xs md:text-base"
                        >
                          <ExternalLink className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                          {toolView.visitWebsite}
                        </Link>
                      </div>
                    )}
                    <div className="flex-shrink-0 border-none md:border rounded-lg hover:cursor-pointer">
                      <button
                        onClick={handleSaveUnsave}
                        disabled={saveUnsaveTool.isPending}
                        className="inline-flex items-center justify-center p-1 md:p-2 font-semibold hover:cursor-pointer"
                      >
                        {saveUnsaveTool.isPending ? (
                          <Loader2 className="h-4 w-4 md:h-5 md:w-5 animate-spin" />
                        ) : tool.is_saved ? (
                          <BookmarkCheck className="h-4 w-4 md:h-5 md:w-5 text-primary" />
                        ) : (
                          <Bookmark className="h-4 w-4 md:h-5 md:w-5" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
                {description && (
                  <p className="hidden md:block text-lg leading-relaxed">{description}</p>
                )}
              </div>
            </div>
          </div>

          {/* Info Section */}
          <div className="flex flex-col md:flex-row items-center justify-between gap-3 md:gap-6">
            {/* Rating */}
            <div className="flex flex-row gap-3 items-center">
              <div>
                {tool.reviews?.quick_review &&
                tool.reviews.quick_review.total_reviews > 0 ? (
                  renderStarRating(
                    tool.reviews.quick_review.average_rating,
                    tool.reviews.quick_review.total_reviews
                  )
                ) : (
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star key={star} className="w-4 h-4 text-gray-300" />
                      ))}
                    </div>
                    <span className="text-sm font-medium text-gray-600">
                      {toolView.noReviewsYet}
                    </span>
                  </div>
                )}
              </div>
              <div className="h-6 w-px bg-gray-200" />
              <div className="flex items-center gap-1">
                <Bookmark className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-600">
                  {tool.saved_count} {toolView.saved}
                </span>
              </div>
            </div>

            {/* Social & Contact Links */}
            <div className="flex flex-wrap gap-3">
              {socialLinks.map((social, index) => {
                if (!social.url) return null;
                const IconComponent = social.icon;
                return (
                  <Link
                    key={index}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 px-2 py-2 rounded-lg bg-black/5 hover:bg-black/10 transition-colors"
                    title={social.label}
                  >
                    <IconComponent className="h-5 w-5 text-black/80" />
                  </Link>
                );
              })}

              {contactLinks.map((contact, index) => {
                if (!contact.url) return null;
                const IconComponent = contact.icon;
                return (
                  <Link
                    key={index}
                    href={contact.url}
                    className="flex items-center gap-2 px-2 py-2 rounded-lg bg-black/5 hover:bg-black/10 transition-colors"
                  >
                    <IconComponent className="h-5 w-5 text-black/80" />
                  </Link>
                );
              })}
            </div>
          </div>

          <Separator className="my-4 md:my-3" />

          {/* Tags, Categories | Featured Image */}
          <div className="flex flex-col md:flex-row gap-6 pt-2">
            <div className="w-full md:w-1/2 space-y-4 mb-8">
              {/* Added Date */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-bold">
                  {toolView.added}:
                </span>
                <span className="text-sm font-medium">
                  {new Date(tool.added_date).toLocaleDateString()}
                </span>
              </div>

              {/* Type */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-bold">
                  {toolView.type}:
                </span>
                <span className="text-sm font-medium">
                  {tool.tool_type
                    .replace(/_/g, " ")
                    .toLowerCase()
                    .replace(/\b\w/g, (l) => l.toUpperCase())}
                </span>
              </div>

              {/* Monthly Traffic */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-bold">
                  {toolView.monthlyTraffic}:
                </span>
                <span className="text-sm font-medium">-</span>
              </div>

              {/* Pricing Models */}
              {tool.pricing_models && tool.pricing_models.length > 0 && (
                <div className="flex flex-wrap items-center gap-2 pb-1 md:pb-5">
                  <div className="flex items-center gap-2 text-sm font-bold">
                    {toolView.pricing}:
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {tool.pricing_models.map((model) => (
                      <Badge
                        key={model}
                        variant="outline"
                        className="border-primary/20 text-primary bg-primary/5 hover:bg-primary/10"
                      >
                        {model}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Categories */}
              {categories.length > 0 && (
                <div className="flex flex-wrap items-center gap-2">
                  <div className="flex flex-wrap gap-2">
                    {categories.map((category) => (
                      <Link
                        href="/"
                        key={category.id}
                        className="border-primary/50 text-primary bg-primary/5 hover:bg-primary/10 p-1 rounded-full border px-3 text-xs md:text-sm"
                      >
                        {category.displayName}
                      </Link>
                    ))}
                  </div>
                </div>
              )}
              {/* Tags */}
              {tags.length > 0 && (
                <div className="flex flex-wrap items-center gap-2">
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag) => (
                      <Badge key={tag.id} className="rounded-full">
                        {tag.displayName}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
            <div className="hidden md:block md:w-1/2">
              {tool.screenshot_urls?.[0] && (
                <div className="relative h-80 w-full rounded-xl overflow-hidden">
                  <Image
                    src={tool.screenshot_urls[0]}
                    alt={`${displayName} featured screenshot`}
                    fill
                    className="object-cover"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Screenshots
          {tool.screenshot_urls && tool.screenshot_urls.length > 0 && (
            <ToolViewScreenshot
              screenshots={tool.screenshot_urls}
              toolName={displayName}
              dict={dict}
            />
          )} */}
        </div>
      </div>
    </div>
  );
};

export default ToolViewHero;
