import ToolListingHeader from "./tool-listing-header";
import ToolListing from "./tool-listing";

interface ToolListingPageProps {
  lang: string;
  title?: string;
  description?: string;
}

export default function ToolListingPage({ 
  lang,
  title,
  description 
}: ToolListingPageProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 md:px-0 py-8">
        {/* <ToolListingHeader 
          title={title}
          description={description}
        /> */}
        <ToolListing lang={lang} />
      </div>
    </div>
  );
} 