"use client";

import { useEffect } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useGetToolListing } from "../hooks/use-tool-listing-data";
import {
  useToolListingData,
  useToolListingUI,
  useToolListingActions,
} from "../store/use-tool-listing-store";
import { PricingModel, SuperCategory } from "@/types/tool-api";
import Link from "next/link";
import Image from "next/image";
import { ArrowRight } from "lucide-react";
import { getLocalizedUrl } from "@/lib/utils";

interface ToolListingProps {
  lang: string;
}

export default function ToolListing({ lang }: ToolListingProps) {
  const { tools, pagination, isLoading, error } = useToolListingData();
  const { currentPage } = useToolListingUI();
  const { setTools, setIsLoading, setError, setCurrentPage, setLanguage } =
    useToolListingActions();

  // Fetch tools data
  const {
    data: toolsData,
    isLoading: queryLoading,
    error: queryError,
  } = useGetToolListing(currentPage, lang);

  // Update store when data changes
  useEffect(() => {
    if (toolsData) {
      setTools(toolsData);
    }
  }, [toolsData, setTools]);

  // Update loading state
  useEffect(() => {
    setIsLoading(queryLoading);
  }, [queryLoading, setIsLoading]);

  // Update error state
  useEffect(() => {
    setError(queryError?.message || null);
  }, [queryError, setError]);

  // Update language when prop changes
  useEffect(() => {
    setLanguage(lang);
  }, [lang, setLanguage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getPricingBadgeColor = (pricingModel: PricingModel) => {
    switch (pricingModel) {
      case PricingModel.FREE:
        return "bg-green-100 text-green-800 hover:bg-green-200";
      case PricingModel.FREEMIUM:
        return "bg-blue-100 text-blue-800 hover:bg-blue-200";
      case PricingModel.PAID:
        return "bg-orange-100 text-orange-800 hover:bg-orange-200";
      case PricingModel.SUBSCRIPTION:
        return "bg-purple-100 text-purple-800 hover:bg-purple-200";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-200";
    }
  };

  const formatPricingModel = (pricingModel: PricingModel) => {
    switch (pricingModel) {
      case PricingModel.FREE:
        return "Free";
      case PricingModel.FREEMIUM:
        return "Freemium";
      case PricingModel.PAID:
        return "Paid";
      case PricingModel.SUBSCRIPTION:
        return "Subscription";
      case PricingModel.ONE_TIME:
        return "One-time";
      case PricingModel.USAGE_BASED:
        return "Usage-based";
      case PricingModel.CUSTOM:
        return "Custom";
      default:
        return pricingModel;
    }
  };

  const formatSuperCategory = (superCategory: SuperCategory) => {
    switch (superCategory) {
      case SuperCategory.WRITING:
        return "Writing";
      case SuperCategory.IMAGE_GENERATION:
        return "Image Generation";
      case SuperCategory.AUDIO:
        return "Audio";
      case SuperCategory.VIDEO_GENERATION:
        return "Video Generation";
      case SuperCategory.SOCIAL_MEDIA:
        return "Social Media";
      default:
        return superCategory;
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading tools: {error}</p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Tools Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {isLoading
          ? Array.from({ length: 6 }).map((_, index) => (
              <Card key={index} className="h-64">
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="w-12 h-12 rounded-lg" />
                    <div className="flex-1">
                      <Skeleton className="h-5 w-32 mb-2" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4 mb-4" />
                  <div className="flex gap-2">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                </CardContent>
              </Card>
            ))
          : tools.map((tool) => {
              const linkHref = tool.is_direct
                ? tool.website_url
                : getLocalizedUrl(`/tool/${tool.slug}`, lang);
              const linkTarget = tool.is_direct ? "_blank" : "_self";
              const linkRel = tool.is_direct
                ? "noopener noreferrer"
                : undefined;

              return (
                <Link
                  key={tool.id}
                  href={linkHref || "#"}
                  target={linkTarget}
                  rel={linkRel}
                  className="block group"
                >
                  <div className="h-48 p-5 rounded-xl bg-white dark:bg-gray-800 space-y-4 cursor-pointer border border-transparent ">
                    <div>
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 relative flex-shrink-0 rounded-lg overflow-hidden">
                          {tool.logo_url ? (
                            <Image
                              src={tool.logo_url}
                              alt={`${tool.name} logo`}
                              fill
                              className="object-contain"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700">
                              <span className="text-gray-500 dark:text-gray-300 text-xl font-bold group-hover:text-primar dark:group-hover:text-primary">
                                {tool.name.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-bold text-lg truncate text-gray-900 dark:text-white group-hover:text-primary dark:group-hover:text-primary">
                            {tool.name}
                          </h3>
                          {tool.super_categories.length > 0 && (
                            <Badge variant="outline" className="mt-1 bg-primary border-primary text-white rounded-full text-[10px]">
                              {formatSuperCategory(tool.super_categories[0])}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="h-20">
                      <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3 leading-relaxed font-medium">
                        {tool.short_description}
                      </p>
                    </div>
                    {/* <div className="absolute bottom-4 right-4">
                      <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-primary-500 dark:group-hover:text-primary-400 transition-colors" />
                    </div> */}
                  </div>
                </Link>
              );
            })}
      </div>

      {/* <div className="flex flex-wrap gap-2 pt-2">
                        {tool.pricing_models?.map((model, i) => (
                          <span 
                            key={i}
                            className="text-xs px-2 py-1 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300"
                          >
                            {model}
                          </span>
                        ))}
                      </div> */}

      {/* Pagination */}
      {pagination && pagination.total_pages > 1 && (
        <div className="flex justify-center items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={!pagination.has_prev}
          >
            Previous
          </Button>

          <div className="flex space-x-1">
            {Array.from(
              { length: Math.min(5, pagination.total_pages) },
              (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(page)}
                  >
                    {page}
                  </Button>
                );
              }
            )}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={!pagination.has_next}
          >
            Next
          </Button>
        </div>
      )}

      {/* Results info */}
      {pagination && (
        <div className="text-center text-sm text-gray-600">
          Showing {tools.length} of {pagination.total} tools
        </div>
      )}
    </div>
  );
}
