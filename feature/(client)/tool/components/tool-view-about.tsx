import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ChevronDown, ChevronUp } from "lucide-react";

interface ToolViewAboutProps {
  tool: any;
  toolName: string;
  dict: any;
  content: {
    introduction: string;
    whatIsIt: string;
    howToUse: string;
    fullFeature: string;
    shortFeature: string;
    pricingInfo: string;
  };
  faqs: any[];
}

const ToolViewAbout = ({
  tool,
  toolName,
  dict,
  content,
  faqs,
}: ToolViewAboutProps) => {
  const toolView = dict.client.toolView;
  const [openFAQ, setOpenFAQ] = useState<string | null>(null);

  const sections = [
    {
      title: "",
      content: content.introduction,
      id: "introduction",
    },
    {
      title: `${toolView.whatIsIt} ${toolName}`,
      content: content.whatIsIt,
      id: "what-is-it",
    },
    {
      title: `${toolView.howToUse} ${toolName}`,
      content: content.howToUse,
      id: "how-to-use",
    },
    {
      title: `${toolView.featuresOf} ${toolName}`,
      content: content.fullFeature || content.shortFeature,
      id: "features",
    },
    {
      title: `${toolName} ${toolView.pricing} `,
      content: content.pricingInfo,
      id: "pricing",
    },
  ];

  // Filter out empty sections
  const validSections = sections.filter(
    (section) => section.content && section.content.trim().length > 0
  );

  const toggleFAQ = (faqId: string) => {
    setOpenFAQ(openFAQ === faqId ? null : faqId);
  };

  if (validSections.length === 0 && (!faqs || faqs.length === 0)) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-gray-500 text-lg">
            {toolView.noContentAvailable}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {validSections.map((section, index) => (
        <div key={section.id} className="space-y-4">
          {/* Section Title */}
          <div className="flex items-center gap-4">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {section.title}
            </h2>
          </div>

          {/* Section Content */}
          <div className="border-0 bg-white dark:bg-black">
            <div>
              <div
                className="prose prose-lg max-w-none dark:prose-invert"
                dangerouslySetInnerHTML={{ __html: section.content }}
              />
            </div>
          </div>
        </div>
      ))}

      {/* FAQ Section */}
      {faqs && faqs.length > 0 && (
        <div className="space-y-6">
          {/* FAQ Title */}
          <div className="flex items-center gap-4">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {`${toolView.faqsAbout} ${toolName}`}
            </h2>
          </div>

          {/* FAQ Content */}
          <div className="space-y-3">
            {faqs.map((faq) => (
              <div
                key={faq.id}
                className="rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 ease-in-out"
              >
                <div
                  className="px-4 py-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors flex items-center justify-between"
                  onClick={() => toggleFAQ(faq.id)}
                >
                  <div className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    {faq.question}
                  </div>
                  <div className="ml-2 transition-transform duration-300">
                    {openFAQ === faq.id ? (
                      <ChevronUp className="h-5 w-5 text-gray-500 shrink-0 transform rotate-180" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-500 shrink-0" />
                    )}
                  </div>
                </div>
                <div
                  className={`overflow-hidden transition-all duration-300 ease-in-out ${
                    openFAQ === faq.id ? 'max-h-[1000px]' : 'max-h-0'
                  }`}
                >
                  <div className="px-4 pb-4 pt-2 bg-gray-50 dark:bg-gray-800/30">
                    <div
                      className="prose prose-sm max-w-none dark:prose-invert text-gray-700 dark:text-gray-300"
                      dangerouslySetInnerHTML={{ __html: faq.answer }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ToolViewAbout;
