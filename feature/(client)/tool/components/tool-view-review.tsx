"use client";

import React, { useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogDescription,
  CustomDialogFooter,
  CustomDialogHeader,
  CustomDialogTitle,
} from "@/components/custom/custom-dialog";
import { CustomPagination } from "@/components/custom/custom-pagination";
import {
  Star,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Filter,
  Plus,
  <PERSON>Half,
  MessageCircle,
  LogIn,
} from "lucide-react";
import {
  useGetToolBySlug,
  useCreateToolReview,
  useReviewStats,
  useFormatRating,
  useVoteOnReview,
  useGetReviewVoteStats,
} from "@/feature/(client)/tool/hooks/use-tool-data";
import {
  useToolReviewState,
  useToolReviewActions,
} from "@/feature/(client)/tool/store/use-tool-view-store";
import { ReviewResponse, VoteType, ToolResponse } from "@/types/tool-api";
import { useSession } from "next-auth/react";
import { getLocalizedUrl } from "@/lib/utils";

interface ToolViewReviewProps {
  tool: ToolResponse;
  toolSlug: string;
  dict?: any;
  toolName: string;
  language?: string;
  lang: string;
}

export function ToolViewReview({
  tool,
  toolSlug,
  dict,
  toolName,
  language = "en",
  lang,
}: ToolViewReviewProps) {
  const toolView = dict.client.toolView;
  const { data: session } = useSession();

  const {
    currentReviewPage,
    reviewsPerPage,
    selectedRatingFilter,
    isReviewDialogOpen,
    reviewFormData,
  } = useToolReviewState();

  const {
    setCurrentReviewPage,
    setSelectedRatingFilter,
    setIsReviewDialogOpen,
    setReviewFormData,
    resetReviewForm,
  } = useToolReviewActions();

  // Only fetch new data when pagination changes or filters are applied
  const shouldFetchData =
    currentReviewPage > 1 || selectedRatingFilter !== null;

  const {
    data: toolData,
    isLoading: isLoadingPagination,
    refetch,
  } = useGetToolBySlug(
    toolSlug,
    language,
    currentReviewPage,
    reviewsPerPage,
    selectedRatingFilter ?? undefined,
    shouldFetchData
  );

  // Use passed tool data for initial load, then toolData for pagination/filtering
  const currentToolData = shouldFetchData ? toolData : tool;
  const isLoading = shouldFetchData ? isLoadingPagination : false;

  const createReviewMutation = useCreateToolReview();
  const voteOnReviewMutation = useVoteOnReview();

  // Track which review is being voted on for loading states
  const [votingReviewId, setVotingReviewId] = useState<string | null>(null);

  // Local state for reviews to handle vote updates
  const [localReviews, setLocalReviews] = useState<ReviewResponse[]>([]);

  // Get reviews from current tool data
  const reviewsData = currentToolData?.reviews;
  const currentReviews = reviewsData?.items || [];

  // Update local reviews when API data changes
  useEffect(() => {
    if (currentReviews.length >= 0) {
      setLocalReviews(currentReviews);
    }
  }, [currentReviews]);

  // Calculate stats from current reviews or use quick_review data from API
  const calculatedStats = useMemo(() => {
    // Use quick_review data from API if available (more accurate for all reviews)
    if (currentToolData?.reviews?.quick_review) {
      return {
        averageRating: currentToolData.reviews.quick_review.average_rating,
        totalReviews: currentToolData.reviews.quick_review.total_reviews,
        ratingDistribution:
          currentToolData.reviews.quick_review.rating_distribution,
      };
    }
    // Fallback to calculating from current paginated reviews
    return useReviewStats(localReviews);
  }, [localReviews, currentToolData?.reviews?.quick_review]);

  // Check if current user has already reviewed this tool
  const userHasReviewed = useMemo(() => {
    if (!session?.user?.id) return false;
    return localReviews.some((review) => review.user_id === session.user.id);
  }, [localReviews, session?.user?.id]);

  // Set tool_id in form when component mounts
  useEffect(() => {
    if (currentToolData?.id && reviewFormData.tool_id !== currentToolData.id) {
      setReviewFormData({ tool_id: currentToolData.id });
    }
  }, [currentToolData?.id, reviewFormData.tool_id, setReviewFormData]);

  const handleSubmitReview = async () => {
    if (!reviewFormData.rating) {
      return;
    }

    try {
      await createReviewMutation.mutateAsync(reviewFormData);
      resetReviewForm();
      setIsReviewDialogOpen(false);
      // Refresh reviews by resetting page and refetching
      setCurrentReviewPage(1);
      refetch();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleVote = async (review_id: string, vote_type: VoteType) => {
    if (!session) {
      window.location.href = getLocalizedUrl("/login", lang);
      return;
    }

    // Set loading state for this specific review
    setVotingReviewId(review_id);

    try {
      const result = await voteOnReviewMutation.mutateAsync({
        review_id,
        vote_type,
      });

      // Update the review in the local state
      setLocalReviews(
        localReviews.map((review) =>
          review.id === review_id
            ? {
                ...review,
                helpful_votes: result.helpful_votes,
                unhelpful_votes: result.unhelpful_votes,
                user_vote: result.user_vote,
              }
            : review
        )
      );
    } catch (error) {
      // Error is handled by the mutation
    } finally {
      // Clear loading state
      setVotingReviewId(null);
    }
  };

  const renderStarRating = (rating: number, size: "sm" | "lg" = "sm") => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    const iconSize = size === "lg" ? "w-6 h-6" : "w-4 h-4";

    // Full stars
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star
          key={`full-${i}`}
          className={`${iconSize} fill-primary text-primary`}
        />
      );
    }

    // Half star
    if (hasHalfStar) {
      stars.push(
        <StarHalf
          key="half"
          className={`${iconSize} fill-primary text-primary`}
        />
      );
    }

    // Empty stars
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Star key={`empty-${i}`} className={`${iconSize} text-gray-300`} />
      );
    }

    return <div className="flex items-center gap-1">{stars}</div>;
  };

  const renderReviewStats = () => (
    <div className="mb-8 shadow-none rounded-xl p-6 border">
      <div className="mb-6">
        <h3 className="text-xl font-semibold">
          {toolView.seeWhatUsersAreSayingAbout}{" "}
          <span className="font-bold text-primary">{toolName}</span>
        </h3>
      </div>
      <div className="p-0">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Overall Rating */}
          <div className="flex flex-col items-center justify-center p-4 bg-gradient-to-br from-primary/5 to-primary/10 rounded-xl">
            <div className="text-5xl font-bold mb-2 text-primary">
              {calculatedStats.averageRating.toFixed(1)}
            </div>
            <div className="mb-3">
              {renderStarRating(calculatedStats.averageRating, "lg")}
            </div>
            <p className="text-sm text-muted-foreground">
              {calculatedStats.totalReviews}{" "}
              {calculatedStats.totalReviews === 1
                ? toolView.review
                : toolView.reviews}
            </p>
          </div>

          {/* Rating Distribution */}
          <div className="space-y-2">
            {[5, 4, 3, 2, 1].map((rating) => (
              <div key={rating} className="flex items-center gap-3">
                <div className="flex items-center gap-1 w-12">
                  <span className="text-sm font-medium">{rating}</span>
                  <Star className="w-3 h-3 fill-primary text-primary" />
                </div>
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${
                        calculatedStats.totalReviews > 0
                          ? (calculatedStats.ratingDistribution[rating] /
                              calculatedStats.totalReviews) *
                            100
                          : 0
                      }%`,
                    }}
                  />
                </div>
                <span className="text-sm text-gray-600 w-8">
                  {calculatedStats.ratingDistribution[rating] || 0}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderReviewCard = (review: ReviewResponse) => (
    <div key={review.id} className="group">
      <div className="relative pt-3 transition-colors duration-200">
        <div className="flex items-start gap-4">
          <Avatar className="w-11 h-11">
            <AvatarImage src={review.user?.image || undefined} />
            <AvatarFallback>
              {review.user?.name?.charAt(0).toUpperCase() || "U"}
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 space-y-1.5">
            <div className="flex items-start justify-between">
              <div>
                <h4 className="font-medium ">
                  {review.user?.name || toolView.anonymous}
                </h4>
                <div className="flex items-center gap-2 mt-1">
                  {renderStarRating(review.rating, "sm")}
                  <span className="text-sm text-gray-500">
                    {new Date(review.created_at).toLocaleDateString(lang, {
                      day: "2-digit",
                      month: "long",
                      year: "numeric",
                    })}
                  </span>
                </div>
              </div>
            </div>

            {review.content && (
              <p className="text-gray-700 leading-relaxed">{review.content}</p>
            )}

            {/* Vote buttons */}
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleVote(review.id, VoteType.HELPFUL)}
                disabled={
                  !session ||
                  votingReviewId === review.id ||
                  review.user_id === session?.user?.id
                }
                className={`flex items-center gap-1.5 `}
              >
                <ThumbsUp className="w-4 h-4" />
                <span>{review.helpful_votes}</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleVote(review.id, VoteType.UNHELPFUL)}
                disabled={
                  !session ||
                  votingReviewId === review.id ||
                  review.user_id === session?.user?.id
                }
                className={`flex items-center gap-1.5`}
              >
                <ThumbsDown className="w-4 h-4" />
                <span>{review.unhelpful_votes}</span>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderReviewForm = () => (
    <CustomDialog
      open={isReviewDialogOpen}
      onOpenChange={setIsReviewDialogOpen}
    >
      <CustomDialogContent className="sm:max-w-lg">
        <CustomDialogHeader>
          <CustomDialogTitle>{toolView.writeReview}</CustomDialogTitle>
          <CustomDialogDescription>
            {toolView.shareYourExperience} <span className="font-bold">{toolName}</span>
          </CustomDialogDescription>
        </CustomDialogHeader>

        <div className="space-y-4 px-4 py-2">
          {/* Rating */}
          <div className="space-y-2">
            <Label htmlFor="rating" className="text-sm">
              {toolView.rating} *
            </Label>
            <div className="flex items-center gap-0">
              {[1, 2, 3, 4, 5].map((rating) => (
                <button
                  key={rating}
                  onClick={() => setReviewFormData({ rating })}
                  className={`p-2 hover:cursor-pointer ${
                    reviewFormData.rating && reviewFormData.rating >= rating
                      ? "text-primary"
                      : "text-gray-300"
                  }`}
                >
                  <Star className="w-7 h-7 fill-current" />
                </button>
              ))}
              {reviewFormData.rating && (
                <span className="ml-2 text-sm text-gray-600">
                  {reviewFormData.rating}/5
                </span>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="space-y-2">
            <Label htmlFor="content" className="text-sm">
              {toolView.reviewContent} ({toolView.optional})
            </Label>
            <Textarea
              id="content"
              placeholder={`${toolView.reviewPlaceholder} ${toolName}...`}
              value={reviewFormData.content || ""}
              onChange={(e) => setReviewFormData({ content: e.target.value })}
              rows={10}
              className="min-h-[200px] mt-2 text-base"
            />
          </div>
        </div>

        <CustomDialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsReviewDialogOpen(false)}
          >
            {toolView.cancel}
          </Button>
          <Button
            onClick={handleSubmitReview}
            disabled={!reviewFormData.rating || createReviewMutation.isPending}
          >
            {createReviewMutation.isPending
              ? toolView.submitting
              : toolView.submitReview}
          </Button>
        </CustomDialogFooter>
      </CustomDialogContent>
    </CustomDialog>
  );

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-40 bg-gray-200 rounded animate-pulse" />
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Review Stats */}
      {renderReviewStats()}

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        {/* Add Review Button */}
        <div>
          {session ? (
            <Button
              onClick={() => setIsReviewDialogOpen(true)}
              disabled={userHasReviewed}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              {userHasReviewed
                ? toolView.alreadyReviewed
                : toolView.writeReview}
            </Button>
          ) : (
            <Button
              onClick={() => (window.location.href = "/login")}
              className="flex items-center gap-2"
            >
              <LogIn className="w-4 h-4" />
              {toolView.loginToReview}
            </Button>
          )}
        </div>

        {/* Rating Filter */}
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4" />
          <Select
            value={selectedRatingFilter?.toString() || "all"}
            onValueChange={(value) =>
              setSelectedRatingFilter(value === "all" ? null : parseInt(value))
            }
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder={`${toolView.allRatings}`} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{toolView.allRatings}</SelectItem>
              {[5, 4, 3, 2, 1].map((rating) => (
                <SelectItem key={rating} value={rating.toString()}>
                  {rating} {toolView.star} {rating !== 1 ? toolView.stars : ""}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {localReviews.length === 0 ? (
          <div className="text-center py-12">
            <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {toolView.noReviewsYet}
            </h3>
            <p className="text-gray-600">
              {toolView.beTheFirst} {toolName}
            </p>
          </div>
        ) : (
          <>
            {localReviews.map(renderReviewCard)}

            {/* Pagination */}
            {reviewsData && reviewsData.pagination.total_pages > 1 && (
              <div className="flex justify-center mt-6">
                <CustomPagination
                  currentPage={currentReviewPage}
                  totalPages={reviewsData.pagination.total_pages}
                  hasNext={reviewsData.pagination.has_next}
                  hasPrev={reviewsData.pagination.has_prev}
                  onPageChange={setCurrentReviewPage}
                />
              </div>
            )}
          </>
        )}
      </div>

      {/* Review Form Dialog */}
      {renderReviewForm()}
    </div>
  );
}
