"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { SaveIcon, XIcon, ImageIcon } from "lucide-react";
import { UpdateUserProfileRequest } from "../store/use-user-view-store";

interface UserEditFormProps {
  formData: UpdateUserProfileRequest;
  onFormChange: (data: Partial<UpdateUserProfileRequest>) => void;
  onSave: () => void;
  onCancel: () => void;
  isLoading: boolean;
}

export function UserEditForm({
  formData,
  onFormChange,
  onSave,
  onCancel,
  isLoading,
}: UserEditFormProps) {
  const getInitials = (name: string) => {
    return name
      ?.split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2) || "U";
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Edit Profile</span>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onCancel}
              disabled={isLoading}
            >
              <XIcon className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={onSave}
              disabled={isLoading}
            >
              <SaveIcon className="h-4 w-4 mr-2" />
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Avatar Section */}
        <div className="flex flex-col items-center space-y-4">
          <Avatar className="h-24 w-24">
            <AvatarImage
              src={formData.image || ""}
              alt={formData.name || "User"}
              className="object-cover"
            />
            <AvatarFallback className="text-lg">
              {getInitials(formData.name || "")}
            </AvatarFallback>
          </Avatar>
          <div className="w-full max-w-md space-y-2">
            <Label htmlFor="image">Profile Image URL</Label>
            <div className="flex gap-2">
              <Input
                id="image"
                type="url"
                placeholder="https://example.com/image.jpg"
                value={formData.image || ""}
                onChange={(e) => onFormChange({ image: e.target.value })}
                className="flex-1"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  // In a real app, this would open a file picker or image upload dialog
                  const url = prompt("Enter image URL:");
                  if (url) onFormChange({ image: url });
                }}
              >
                <ImageIcon className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Enter a URL to your profile image or click the icon to upload
            </p>
          </div>
        </div>

        {/* Form Fields */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              type="text"
              placeholder="Your full name"
              value={formData.name || ""}
              onChange={(e) => onFormChange({ name: e.target.value })}
              required
            />
            <p className="text-xs text-muted-foreground">
              This will also update your profile URL slug
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="about">About</Label>
            <Textarea
              id="about"
              placeholder="Tell us about yourself..."
              value={formData.about || ""}
              onChange={(e) => onFormChange({ about: e.target.value })}
              rows={4}
              maxLength={500}
            />
            <p className="text-xs text-muted-foreground">
              {formData.about?.length || 0}/500 characters
            </p>
          </div>
        </div>

        {/* Action Buttons (Mobile) */}
        <div className="flex flex-col sm:hidden gap-2 pt-4 border-t">
          <Button
            onClick={onSave}
            disabled={isLoading}
            className="w-full"
          >
            <SaveIcon className="h-4 w-4 mr-2" />
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
            className="w-full"
          >
            <XIcon className="h-4 w-4 mr-2" />
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 