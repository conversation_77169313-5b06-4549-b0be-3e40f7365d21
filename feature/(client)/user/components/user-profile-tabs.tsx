"use client";

import React from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { UserIcon, StarIcon, BookmarkIcon } from "lucide-react";

interface UserProfileTabsProps {
  activeTab: "profile" | "reviews" | "saved-tools";
  onTabChange: (tab: "profile" | "reviews" | "saved-tools") => void;
  reviewsCount: number;
  savedToolsCount: number;
  isOwnProfile?: boolean;
}

export function UserProfileTabs({
  activeTab,
  onTabChange,
  reviewsCount,
  savedToolsCount,
  isOwnProfile = false,
}: UserProfileTabsProps) {
  return (
    <Tabs 
      value={activeTab} 
      onValueChange={(value) => onTabChange(value as "profile" | "reviews" | "saved-tools")} 
      className="w-full"
    >
      <TabsList className={`grid w-full ${isOwnProfile ? 'grid-cols-3' : 'grid-cols-2'}`}>
        <TabsTrigger value="profile" className="flex items-center gap-2">
          <UserIcon className="h-4 w-4" />
          <span>Profile</span>
        </TabsTrigger>
        <TabsTrigger value="reviews" className="flex items-center gap-2">
          <StarIcon className="h-4 w-4" />
          <span>Reviews</span>
          {reviewsCount > 0 && (
            <span className="ml-1 rounded-full bg-muted px-2 py-0.5 text-xs">
              {reviewsCount}
            </span>
          )}
        </TabsTrigger>
        {isOwnProfile && (
          <TabsTrigger value="saved-tools" className="flex items-center gap-2">
            <BookmarkIcon className="h-4 w-4" />
            <span>Saved Tools</span>
            {savedToolsCount > 0 && (
              <span className="ml-1 rounded-full bg-muted px-2 py-0.5 text-xs">
                {savedToolsCount}
              </span>
            )}
          </TabsTrigger>
        )}
      </TabsList>
    </Tabs>
  );
} 