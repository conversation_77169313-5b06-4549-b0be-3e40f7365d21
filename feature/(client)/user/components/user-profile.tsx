"use client";

import React, { useEffect } from "react";
import { useParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircleIcon } from "lucide-react";
import {
  UserProfileHeader,
  UserProfileTabs,
  UserEditForm,
  useUserViewStore,
  useUserViewData,
  useUserViewUI,
  useUserViewActions,
  useUserViewPagination,
  useUserEditState,
  useGetUserProfile,
  useGetUserBySlug,
  useGetUserReviews,
  useGetSavedTools,
  useUpdateUserProfile,
} from "@/feature/(client)/user";

interface UserProfileProps {
    dict: any;
    lang: string;
    slug: string;
}

export default function UserProfile({ dict, lang, slug }: UserProfileProps) {
  const { data: session } = useSession();

  // Store state
  const { userProfile, userReviews, savedTools, isLoading, error } = useUserViewData();
  const { activeTab, language, isEditMode } = useUserViewUI();
  const { 
    setUserProfile, 
    setUserReviews, 
    setSavedTools, 
    setActiveTab, 
    setIsEditMode,
    setError 
  } = useUserViewActions();
  const { reviewsPage, savedToolsPage } = useUserViewPagination();
  const { editFormData, setEditFormData, resetEditForm } = useUserEditState();

  // Get user profile by slug (public API)
  const { data: profileData, isLoading: isLoadingProfile, error: profileError } = useGetUserBySlug(
    slug,
    !!slug
  );

  // Check if this is the user's own profile
  const isOwnProfile = session?.user?.slug === slug;
  const userId = profileData?.id || "";

  const { data: reviewsData, isLoading: isLoadingReviews } = useGetUserReviews(
    slug,
    {
      page: reviewsPage,
      limit: 10,
      language,
    },
    !!slug && activeTab === "reviews"
  );

  const { data: savedToolsData, isLoading: isLoadingSavedTools } = useGetSavedTools(
    slug,
    {
      page: savedToolsPage,
      limit: 20,
      language,
    },
    !!slug && activeTab === "saved-tools"
  );

  const updateProfileMutation = useUpdateUserProfile();

  // Effects
  useEffect(() => {
    if (profileData) {
      setUserProfile(profileData);
    }
  }, [profileData, setUserProfile]);

  useEffect(() => {
    if (reviewsData) {
      setUserReviews(reviewsData.items);
    }
  }, [reviewsData, setUserReviews]);

  useEffect(() => {
    if (savedToolsData) {
      setSavedTools(savedToolsData.items);
    }
  }, [savedToolsData, setSavedTools]);

  useEffect(() => {
    if (profileError) {
      setError(profileError.message || "Failed to load user profile");
    }
  }, [profileError, setError]);

  // Reset tab to profile if viewing another user's profile and saved-tools is active
  useEffect(() => {
    if (!isOwnProfile && activeTab === "saved-tools") {
      setActiveTab("profile");
    }
  }, [isOwnProfile, activeTab, setActiveTab]);

  // Handlers
  const handleEditClick = () => {
    if (isOwnProfile) {
      setIsEditMode(true);
    }
  };

  const handleEditCancel = () => {
    setIsEditMode(false);
    resetEditForm();
  };

  const handleEditSave = async () => {
    if (!userId || !editFormData.name?.trim() || !isOwnProfile) return;

    try {
      const updatedProfile = await updateProfileMutation.mutateAsync({
        userId,
        data: editFormData,
      });
      setUserProfile(updatedProfile);
      setIsEditMode(false);
    } catch (error) {
      console.error("Failed to update profile:", error);
    }
  };

  const handleTabChange = (tab: "profile" | "reviews" | "saved-tools") => {
    // Don't allow access to saved-tools tab for other users' profiles
    if (tab === "saved-tools" && !isOwnProfile) {
      return;
    }
    setActiveTab(tab);
  };

  // Loading state
  if (isLoadingProfile) {
    return (
      <div className="container mx-auto px-4 py-8 space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-6">
              <Skeleton className="h-24 w-24 md:h-32 md:w-32 rounded-full" />
              <div className="flex-1 space-y-4">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-16 w-full" />
                <div className="grid grid-cols-3 gap-4">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (profileError || (!isLoadingProfile && !userProfile)) {
    const errorMessage = profileError?.message || "User not found";
    const isNotFound = errorMessage.includes("not found") || errorMessage.includes("404");
    
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircleIcon className="h-4 w-4" />
          <AlertDescription>
            {isNotFound ? `User with slug "${slug}" not found` : errorMessage}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Return null if still loading or no profile data
  if (!userProfile) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Profile Header */}
      {isEditMode ? (
        <UserEditForm
          formData={editFormData}
          onFormChange={setEditFormData}
          onSave={handleEditSave}
          onCancel={handleEditCancel}
          isLoading={updateProfileMutation.isPending}
        />
      ) : (
        <UserProfileHeader
          profile={userProfile}
          isOwnProfile={isOwnProfile}
          onEditClick={handleEditClick}
        />
      )}

      {/* Navigation Tabs */}
      {!isEditMode && (
        <UserProfileTabs
          activeTab={activeTab}
          onTabChange={handleTabChange}
          reviewsCount={userProfile.stats.reviewsCount}
          savedToolsCount={isOwnProfile ? userProfile.stats.savedToolsCount : 0}
          isOwnProfile={isOwnProfile}
        />
      )}

      {/* Tab Content */}
      {!isEditMode && (
        <div className="space-y-6">
          {activeTab === "profile" && (
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-12">
                  <h3 className="text-lg font-medium mb-2">Profile Overview</h3>
                  <p className="text-muted-foreground">
                    {userProfile.about || "No additional information provided."}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {activeTab === "reviews" && (
            <Card>
              <CardContent className="p-6">
                {isLoadingReviews ? (
                  <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-16 w-full" />
                      </div>
                    ))}
                  </div>
                ) : userReviews.length > 0 ? (
                  <div className="space-y-4">
                    {userReviews.map((review) => (
                      <div key={review.id} className="border-b pb-4 last:border-b-0">
                        <div className="flex items-start gap-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h4 className="font-medium">{review.tool.name}</h4>
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <span
                                    key={i}
                                    className={`text-sm ${
                                      i < review.rating
                                        ? "text-yellow-400"
                                        : "text-gray-300"
                                    }`}
                                  >
                                    ★
                                  </span>
                                ))}
                              </div>
                            </div>
                            {review.content && (
                              <p className="text-muted-foreground">{review.content}</p>
                            )}
                            <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                              <span>
                                {new Date(review.created_at).toLocaleDateString()}
                              </span>
                              <span>{review.votes.helpful} helpful</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <h3 className="text-lg font-medium mb-2">No Reviews Yet</h3>
                    <p className="text-muted-foreground">
                      This user hasn't written any reviews yet.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {activeTab === "saved-tools" && isOwnProfile && (
            <Card>
              <CardContent className="p-6">
                {isLoadingSavedTools ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[...Array(6)].map((_, i) => (
                      <div key={i} className="space-y-2">
                        <Skeleton className="h-32 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                    ))}
                  </div>
                ) : savedTools.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {savedTools.map((savedTool) => (
                      <div
                        key={savedTool.id}
                        className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-start gap-3">
                          {savedTool.tool.logo_url && (
                            <img
                              src={savedTool.tool.logo_url}
                              alt={savedTool.tool.name}
                              className="w-12 h-12 rounded-lg object-cover"
                            />
                          )}
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium truncate">
                              {savedTool.tool.name}
                            </h4>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {savedTool.tool.translations[0]?.short_description ||
                                "No description available"}
                            </p>
                            <p className="text-xs text-muted-foreground mt-2">
                              Saved {new Date(savedTool.saved_at).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <h3 className="text-lg font-medium mb-2">No Saved Tools</h3>
                    <p className="text-muted-foreground">
                      You haven't saved any tools yet.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
