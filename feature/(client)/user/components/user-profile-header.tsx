"use client";

import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CalendarIcon, EditIcon, MailIcon, UserIcon } from "lucide-react";
import { UserProfile } from "../store/use-user-view-store";
import { useFormatUserJoinDate, useUserStats } from "../hooks/use-user-data";

interface UserProfileHeaderProps {
  profile: UserProfile;
  isOwnProfile: boolean;
  onEditClick: () => void;
}

export function UserProfileHeader({
  profile,
  isOwnProfile,
  onEditClick,
}: UserProfileHeaderProps) {
  const joinDate = useFormatUserJoinDate(profile.createdAt);
  const stats = useUserStats(profile);

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role.toLowerCase()) {
      case "admin":
        return "destructive";
      case "author":
        return "secondary";
      default:
        return "outline";
    }
  };

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Avatar Section */}
          <div className="flex flex-col items-center md:items-start">
            <Avatar className="h-24 w-24 md:h-32 md:w-32">
              <AvatarImage
                src={profile.image || ""}
                alt={profile.name}
                className="object-cover"
              />
              <AvatarFallback className="text-lg md:text-xl">
                {getInitials(profile.name)}
              </AvatarFallback>
            </Avatar>
            {isOwnProfile && (
              <Button
                variant="outline"
                size="sm"
                onClick={onEditClick}
                className="mt-4"
              >
                <EditIcon className="h-4 w-4 mr-2" />
                Edit Profile
              </Button>
            )}
          </div>

          {/* Profile Info Section */}
          <div className="flex-1 space-y-4">
            <div className="space-y-2">
              <div className="flex flex-col md:flex-row md:items-center gap-2">
                <h1 className="text-2xl md:text-3xl font-bold">{profile.name}</h1>
                <Badge variant={getRoleBadgeColor(profile.role)}>
                  {profile.role}
                </Badge>
                {profile.isVerified && (
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    Verified
                  </Badge>
                )}
              </div>
              <p className="text-muted-foreground">@{profile.slug}</p>
            </div>

            {/* About Section */}
            {profile.about && (
              <div className="space-y-2">
                <h3 className="font-medium">About</h3>
                <p className="text-muted-foreground leading-relaxed">
                  {profile.about}
                </p>
              </div>
            )}

            {/* Stats Section */}
            <div className="grid grid-cols-3 gap-4 pt-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {stats?.totalReviews || 0}
                </div>
                <div className="text-sm text-muted-foreground">Reviews</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {stats?.totalSavedTools || 0}
                </div>
                <div className="text-sm text-muted-foreground">Saved Tools</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {stats?.totalBlogPosts || 0}
                </div>
                <div className="text-sm text-muted-foreground">Blog Posts</div>
              </div>
            </div>

            {/* Meta Info */}
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground pt-4 border-t">
              <div className="flex items-center gap-1">
                <CalendarIcon className="h-4 w-4" />
                <span>Joined {joinDate}</span>
              </div>
              {isOwnProfile && (
                <div className="flex items-center gap-1">
                  <MailIcon className="h-4 w-4" />
                  <span>{profile.email}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 