import { useApiQ<PERSON>y, useApiMutation, apiGet, apiPatch, apiDelete } from "@/feature/core/api/api-utils";
import { 
  UserProfile, 
  UserReview, 
  SavedTool, 
  UpdateUserProfileRequest 
} from "../store/use-user-view-store";
import { toast } from "sonner";

// User API functions
const userApi = {
  getUserProfile: async (userId: string): Promise<UserProfile> => {
    const response = await apiGet<{ data: UserProfile }>(`/api/user/${userId}/profile`);
    return response.data;
  },

  updateUserProfile: async (userId: string, data: UpdateUserProfileRequest): Promise<UserProfile> => {
    const response = await apiPatch<{ data: UserProfile }>(`/api/user/${userId}/profile`, data);
    return response.data;
  },

  deleteUserProfile: async (userId: string): Promise<{ id: string; email: string }> => {
    const response = await apiDelete<{ data: { id: string; email: string } }>(`/api/user/${userId}/profile`);
    return response.data;
  },

  getUserReviews: async (
    userSlug: string,
    params: {
      page?: number;
      limit?: number;
      search?: string;
      rating?: number;
      status?: string;
      tool_type?: string;
      language?: string;
    } = {}
  ): Promise<{
    items: UserReview[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      total_pages: number;
      has_next: boolean;
      has_prev: boolean;
    };
    user: {
      id: string;
      name: string;
      image: string | null;
    };
  }> => {
    const searchParams = new URLSearchParams();
    if (params.page) searchParams.set("page", params.page.toString());
    if (params.limit) searchParams.set("limit", params.limit.toString());
    if (params.search) searchParams.set("search", params.search);
    if (params.rating) searchParams.set("rating", params.rating.toString());
    if (params.status) searchParams.set("status", params.status);
    if (params.tool_type) searchParams.set("tool_type", params.tool_type);
    if (params.language) searchParams.set("language", params.language);

    const queryString = searchParams.toString();
    const response = await apiGet<{
      data: {
        items: UserReview[];
        pagination: {
          page: number;
          limit: number;
          total: number;
          total_pages: number;
          has_next: boolean;
          has_prev: boolean;
        };
        user: {
          id: string;
          name: string;
          image: string | null;
        };
      };
    }>(`/api/v1/user/${userSlug}/reviews${queryString ? `?${queryString}` : ""}`);
    return response.data;
  },

  getSavedTools: async (
    userSlug: string,
    params: {
      page?: number;
      limit?: number;
      search?: string;
      tool_type?: string;
      category_id?: string;
      language?: string;
    } = {}
  ): Promise<{
    items: SavedTool[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      total_pages: number;
      has_next: boolean;
      has_prev: boolean;
    };
  }> => {
    const searchParams = new URLSearchParams();
    if (params.page) searchParams.set("page", params.page.toString());
    if (params.limit) searchParams.set("limit", params.limit.toString());
    if (params.search) searchParams.set("search", params.search);
    if (params.tool_type) searchParams.set("tool_type", params.tool_type);
    if (params.category_id) searchParams.set("category_id", params.category_id);
    if (params.language) searchParams.set("language", params.language);

    const queryString = searchParams.toString();
    const response = await apiGet<{
      data: {
        items: SavedTool[];
        pagination: {
          page: number;
          limit: number;
          total: number;
          total_pages: number;
          has_next: boolean;
          has_prev: boolean;
        };
      };
    }>(`/api/v1/user/${userSlug}/saved-tools${queryString ? `?${queryString}` : ""}`);
    return response.data;
  },

  removeSavedTool: async (
    userId: string,
    toolId: string
  ): Promise<{
    removed_tool: {
      id: string;
      name: string;
      slug: string;
      logo_url: string | null;
      tool_type: string;
      category_ids: number[];
      categories: Array<{
        id: number;
        name: string;
        slug: string;
        super_category: string;
        icon_url: string | null;
        color: string | null;
      }>;
      super_categories: string[];
      translations: Array<{
        id: string;
        language_code: string;
        name: string | null;
        short_description: string | null;
      }>;
      saved_at: Date;
    };
    message: string;
  }> => {
    const response = await apiDelete<{
      data: {
        removed_tool: {
          id: string;
          name: string;
          slug: string;
          logo_url: string | null;
          tool_type: string;
          category_ids: number[];
          categories: Array<{
            id: number;
            name: string;
            slug: string;
            super_category: string;
            icon_url: string | null;
            color: string | null;
          }>;
          super_categories: string[];
          translations: Array<{
            id: string;
            language_code: string;
            name: string | null;
            short_description: string | null;
          }>;
          saved_at: Date;
        };
        message: string;
      };
    }>(`/api/user/${userId}/saved-tools/${toolId}`);
    return response.data;
  },

  getUserBySlug: async (slug: string): Promise<UserProfile> => {
    const response = await apiGet<{ data: UserProfile }>(`/api/v1/user/${slug}`);
    return response.data;
  },
};

// Hook to get user profile
export function useGetUserProfile(userId: string, enabled: boolean = true) {
  return useApiQuery(
    ["user-profile", userId],
    () => userApi.getUserProfile(userId),
    {
      enabled: enabled && !!userId,
      staleTime: 5 * 60 * 1000, // 5 minutes cache
    }
  );
}

// Hook to update user profile
export function useUpdateUserProfile() {
  return useApiMutation(
    async ({ userId, data }: { userId: string; data: UpdateUserProfileRequest }) => {
      return userApi.updateUserProfile(userId, data);
    },
    {
      onSuccess: (data) => {
        toast.success("Profile updated successfully");
      },
      onError: (error: any) => {
        toast.error(error?.message || "Failed to update profile");
      },
    }
  );
}

// Hook to delete user profile
export function useDeleteUserProfile() {
  return useApiMutation(
    async (userId: string) => {
      return userApi.deleteUserProfile(userId);
    },
    {
      onSuccess: (data) => {
        toast.success("Account deleted successfully");
      },
      onError: (error: any) => {
        toast.error(error?.message || "Failed to delete account");
      },
    }
  );
}

// Hook to get user reviews
export function useGetUserReviews(
  userSlug: string,
  params: {
    page?: number;
    limit?: number;
    search?: string;
    rating?: number;
    status?: string;
    tool_type?: string;
    language?: string;
  } = {},
  enabled: boolean = true
) {
  return useApiQuery(
    ["user-reviews", userSlug, JSON.stringify(params)],
    () => userApi.getUserReviews(userSlug, params),
    {
      enabled: enabled && !!userSlug,
      staleTime: 2 * 60 * 1000, // 2 minutes cache
    }
  );
}

// Hook to get saved tools
export function useGetSavedTools(
  userSlug: string,
  params: {
    page?: number;
    limit?: number;
    search?: string;
    tool_type?: string;
    category_id?: string;
    language?: string;
  } = {},
  enabled: boolean = true
) {
  return useApiQuery(
    ["saved-tools", userSlug, JSON.stringify(params)],
    () => userApi.getSavedTools(userSlug, params),
    {
      enabled: enabled && !!userSlug,
      staleTime: 2 * 60 * 1000, // 2 minutes cache
    }
  );
}

// Hook to remove saved tool
export function useRemoveSavedTool() {
  return useApiMutation(
    async ({ userId, toolId }: { userId: string; toolId: string }) => {
      return userApi.removeSavedTool(userId, toolId);
    },
    {
      onSuccess: (data) => {
        toast.success("Tool removed from saved list");
      },
      onError: (error: any) => {
        toast.error(error?.message || "Failed to remove saved tool");
      },
    }
  );
}

// Hook to get user by slug (for public profile access)
export function useGetUserBySlug(slug: string, enabled: boolean = true) {
  return useApiQuery(
    ["user-by-slug", slug],
    () => userApi.getUserBySlug(slug),
    {
      enabled: enabled && !!slug,
      staleTime: 5 * 60 * 1000, // 5 minutes cache
    }
  );
}

// Utility hooks for data processing
export function useFormatUserJoinDate(date: Date | string) {
  const joinDate = typeof date === "string" ? new Date(date) : date;
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
  }).format(joinDate);
}

export function useUserStats(profile: UserProfile | null) {
  if (!profile) return null;

  return {
    totalReviews: profile.stats.reviewsCount,
    totalSavedTools: profile.stats.savedToolsCount,
    totalBlogPosts: profile.stats.blogPostsCount,
    joinDate: useFormatUserJoinDate(profile.createdAt),
  };
} 