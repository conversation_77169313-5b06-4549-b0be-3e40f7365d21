import { create } from "zustand";

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  image: string | null;
  slug: string;
  about: string | null;
  role: string;
  emailVerified: Date | null;
  isVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
  stats: {
    reviewsCount: number;
    savedToolsCount: number;
    blogPostsCount: number;
  };
}

export interface UserReview {
  id: string;
  rating: number;
  content: string | null;
  is_published: boolean;
  helpful_votes: number;
  unhelpful_votes: number;
  created_at: Date;
  updated_at: Date;
  votes: {
    helpful: number;
    unhelpful: number;
    total: number;
    user_vote: string | null;
  };
  tool: {
    id: string;
    name: string;
    slug: string;
    logo_url: string | null;
    tool_type: string;
    pricing_models: string[];
    website_url: string | null;
    category_ids: number[];
    tag_ids: number[];
    created_at: Date;
    updated_at: Date;
    translations: Array<{
      id: string;
      language_code: string;
      name: string | null;
      short_description: string | null;
    }>;
    categories: Array<{
      id: number;
      name: string;
      slug: string;
      super_category: string;
      icon_url: string | null;
      color: string | null;
      translations: any[];
    }>;
  };
}

export interface SavedTool {
  id: string;
  saved_at: Date;
  tool: {
    id: string;
    name: string;
    slug: string;
    logo_url: string | null;
    tool_type: string;
    pricing_models: string[];
    website_url: string | null;
    category_ids: number[];
    tag_ids: number[];
    created_at: Date;
    updated_at: Date;
    translations: Array<{
      id: string;
      language_code: string;
      name: string | null;
      short_description: string | null;
    }>;
    categories: Array<{
      id: number;
      name: string;
      slug: string;
      super_category: string;
      icon_url: string | null;
      color: string | null;
    }>;
  };
}

export interface UpdateUserProfileRequest {
  name?: string;
  image?: string;
  about?: string;
}

interface UserViewStore {
  // Current user data
  userProfile: UserProfile | null;
  userReviews: UserReview[];
  savedTools: SavedTool[];

  // Loading states
  isLoading: boolean;
  isLoadingReviews: boolean;
  isLoadingSavedTools: boolean;
  error: string | null;

  // UI state
  activeTab: "profile" | "reviews" | "saved-tools";
  language: string;

  // Pagination
  reviewsPage: number;
  reviewsPerPage: number;
  savedToolsPage: number;
  savedToolsPerPage: number;
  reviewsTotal: number;
  savedToolsTotal: number;

  // Filters
  reviewRatingFilter: number | null;
  reviewStatusFilter: string | null;
  savedToolsSearch: string;
  savedToolsTypeFilter: string | null;
  savedToolsCategoryFilter: string | null;

  // Edit mode
  isEditMode: boolean;
  editFormData: UpdateUserProfileRequest;

  // Actions
  setUserProfile: (profile: UserProfile | null) => void;
  setUserReviews: (reviews: UserReview[]) => void;
  setSavedTools: (tools: SavedTool[]) => void;
  setIsLoading: (loading: boolean) => void;
  setIsLoadingReviews: (loading: boolean) => void;
  setIsLoadingSavedTools: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setActiveTab: (tab: "profile" | "reviews" | "saved-tools") => void;
  setLanguage: (language: string) => void;

  // Pagination actions
  setReviewsPage: (page: number) => void;
  setSavedToolsPage: (page: number) => void;
  setReviewsTotal: (total: number) => void;
  setSavedToolsTotal: (total: number) => void;

  // Filter actions
  setReviewRatingFilter: (rating: number | null) => void;
  setReviewStatusFilter: (status: string | null) => void;
  setSavedToolsSearch: (search: string) => void;
  setSavedToolsTypeFilter: (type: string | null) => void;
  setSavedToolsCategoryFilter: (category: string | null) => void;

  // Edit actions
  setIsEditMode: (editMode: boolean) => void;
  setEditFormData: (data: Partial<UpdateUserProfileRequest>) => void;
  resetEditForm: () => void;

  // Reset state
  resetState: () => void;
}

const initialEditFormData: UpdateUserProfileRequest = {
  name: "",
  image: "",
  about: "",
};

const initialState = {
  userProfile: null,
  userReviews: [],
  savedTools: [],
  isLoading: false,
  isLoadingReviews: false,
  isLoadingSavedTools: false,
  error: null,
  activeTab: "profile" as const,
  language: "en",
  reviewsPage: 1,
  reviewsPerPage: 10,
  savedToolsPage: 1,
  savedToolsPerPage: 20,
  reviewsTotal: 0,
  savedToolsTotal: 0,
  reviewRatingFilter: null,
  reviewStatusFilter: null,
  savedToolsSearch: "",
  savedToolsTypeFilter: null,
  savedToolsCategoryFilter: null,
  isEditMode: false,
  editFormData: initialEditFormData,
};

export const useUserViewStore = create<UserViewStore>((set, get) => ({
  ...initialState,

  setUserProfile: (profile) => {
    set({ userProfile: profile });
    // Initialize edit form with current profile data
    if (profile) {
      set({
        editFormData: {
          name: profile.name,
          image: profile.image || "",
          about: profile.about || "",
        },
      });
    }
  },
  setUserReviews: (reviews) => set({ userReviews: reviews }),
  setSavedTools: (tools) => set({ savedTools: tools }),
  setIsLoading: (loading) => set({ isLoading: loading }),
  setIsLoadingReviews: (loading) => set({ isLoadingReviews: loading }),
  setIsLoadingSavedTools: (loading) => set({ isLoadingSavedTools: loading }),
  setError: (error) => set({ error }),
  setActiveTab: (tab) => set({ activeTab: tab }),
  setLanguage: (language) => set({ language }),

  // Pagination actions
  setReviewsPage: (page) => set({ reviewsPage: page }),
  setSavedToolsPage: (page) => set({ savedToolsPage: page }),
  setReviewsTotal: (total) => set({ reviewsTotal: total }),
  setSavedToolsTotal: (total) => set({ savedToolsTotal: total }),

  // Filter actions
  setReviewRatingFilter: (rating) => set({ reviewRatingFilter: rating }),
  setReviewStatusFilter: (status) => set({ reviewStatusFilter: status }),
  setSavedToolsSearch: (search) => set({ savedToolsSearch: search }),
  setSavedToolsTypeFilter: (type) => set({ savedToolsTypeFilter: type }),
  setSavedToolsCategoryFilter: (category) => set({ savedToolsCategoryFilter: category }),

  // Edit actions
  setIsEditMode: (editMode) => set({ isEditMode: editMode }),
  setEditFormData: (data) =>
    set((state) => ({
      editFormData: { ...state.editFormData, ...data },
    })),
  resetEditForm: () => {
    const { userProfile } = get();
    if (userProfile) {
      set({
        editFormData: {
          name: userProfile.name,
          image: userProfile.image || "",
          about: userProfile.about || "",
        },
      });
    }
  },

  resetState: () => set(initialState),
}));

// Selector hooks for better performance
export const useUserViewData = () => {
  const { userProfile, userReviews, savedTools, isLoading, isLoadingReviews, isLoadingSavedTools, error } = useUserViewStore();
  return { userProfile, userReviews, savedTools, isLoading, isLoadingReviews, isLoadingSavedTools, error };
};

export const useUserViewUI = () => {
  const { activeTab, language, isEditMode } = useUserViewStore();
  return { activeTab, language, isEditMode };
};

export const useUserViewActions = () => {
  const {
    setUserProfile,
    setUserReviews,
    setSavedTools,
    setIsLoading,
    setIsLoadingReviews,
    setIsLoadingSavedTools,
    setError,
    setActiveTab,
    setLanguage,
    setIsEditMode,
    resetState,
  } = useUserViewStore();
  return {
    setUserProfile,
    setUserReviews,
    setSavedTools,
    setIsLoading,
    setIsLoadingReviews,
    setIsLoadingSavedTools,
    setError,
    setActiveTab,
    setLanguage,
    setIsEditMode,
    resetState,
  };
};

// Pagination selector hooks
export const useUserViewPagination = () => {
  const {
    reviewsPage,
    reviewsPerPage,
    savedToolsPage,
    savedToolsPerPage,
    reviewsTotal,
    savedToolsTotal,
    setReviewsPage,
    setSavedToolsPage,
  } = useUserViewStore();
  return {
    reviewsPage,
    reviewsPerPage,
    savedToolsPage,
    savedToolsPerPage,
    reviewsTotal,
    savedToolsTotal,
    setReviewsPage,
    setSavedToolsPage,
  };
};

// Filter selector hooks
export const useUserViewFilters = () => {
  const {
    reviewRatingFilter,
    reviewStatusFilter,
    savedToolsSearch,
    savedToolsTypeFilter,
    savedToolsCategoryFilter,
    setReviewRatingFilter,
    setReviewStatusFilter,
    setSavedToolsSearch,
    setSavedToolsTypeFilter,
    setSavedToolsCategoryFilter,
  } = useUserViewStore();
  return {
    reviewRatingFilter,
    reviewStatusFilter,
    savedToolsSearch,
    savedToolsTypeFilter,
    savedToolsCategoryFilter,
    setReviewRatingFilter,
    setReviewStatusFilter,
    setSavedToolsSearch,
    setSavedToolsTypeFilter,
    setSavedToolsCategoryFilter,
  };
};

// Edit selector hooks
export const useUserEditState = () => {
  const {
    isEditMode,
    editFormData,
    setIsEditMode,
    setEditFormData,
    resetEditForm,
  } = useUserViewStore();
  return {
    isEditMode,
    editFormData,
    setIsEditMode,
    setEditFormData,
    resetEditForm,
  };
}; 