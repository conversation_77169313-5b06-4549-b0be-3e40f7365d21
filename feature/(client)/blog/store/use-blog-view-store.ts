import { create } from "zustand";
import { BlogPostResponse } from "@/types/blog-api";

interface BlogViewState {
  // Data
  blogPost: BlogPostResponse | null;
  
  // UI State
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setBlogPost: (blogPost: BlogPostResponse | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

// Initial state
const initialState = {
  blogPost: null,
  isLoading: false,
  error: null,
};

// Main store
export const useBlogViewStore = create<BlogViewState>((set) => ({
  // Data
  ...initialState,
  
  // Actions
  setBlogPost: (blogPost) => set({ blogPost }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  reset: () => set(initialState),
}));

// Individual selectors to avoid object creation
export const useBlogViewData = () => useBlogViewStore((state) => state.blogPost);
export const useBlogViewIsLoading = () => useBlogViewStore((state) => state.isLoading);
export const useBlogViewError = () => useBlogViewStore((state) => state.error);
export const useBlogViewSetBlogPost = () => useBlogViewStore((state) => state.setBlogPost);
export const useBlogViewSetLoading = () => useBlogViewStore((state) => state.setLoading);
export const useBlogViewSetError = () => useBlogViewStore((state) => state.setError);
export const useBlogViewReset = () => useBlogViewStore((state) => state.reset);

// For backward compatibility - but these might cause issues in SSR
export const useBlogViewUI = () => {
  const isLoading = useBlogViewIsLoading();
  const error = useBlogViewError();
  return { isLoading, error };
};

export const useBlogViewActions = () => {
  const setBlogPost = useBlogViewSetBlogPost();
  const setLoading = useBlogViewSetLoading();
  const setError = useBlogViewSetError();
  const reset = useBlogViewReset();
  return { setBlogPost, setLoading, setError, reset };
}; 