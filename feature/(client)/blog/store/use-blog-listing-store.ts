import { create } from "zustand";
import { BlogListingPost, BlogListingResponse } from "@/types/blog-api";

interface BlogListingState {
  // Data
  posts: BlogListingPost[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  } | null;
  
  // UI State
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  
  // Actions
  setPosts: (posts: BlogListingPost[]) => void;
  setPagination: (pagination: BlogListingState['pagination']) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentPage: (page: number) => void;
  setBlogListingData: (data: BlogListingResponse) => void;
  reset: () => void;
}

// Initial state
const initialState = {
  posts: [],
  pagination: null,
  isLoading: false,
  error: null,
  currentPage: 1,
};

// Main store
export const useBlogListingStore = create<BlogListingState>((set, get) => ({
  // Data
  ...initialState,
  
  // Actions
  setPosts: (posts) => set({ posts }),
  setPagination: (pagination) => set({ pagination }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  setCurrentPage: (currentPage) => set({ currentPage }),
  setBlogListingData: (data) => set({ 
    posts: data.posts, 
    pagination: data.pagination,
    currentPage: data.pagination.page
  }),
  reset: () => set(initialState),
}));

// Selector hooks
export const useBlogListingData = () => {
  const store = useBlogListingStore();
  return {
    posts: store.posts,
    pagination: store.pagination,
    isLoading: store.isLoading,
    error: store.error,
    currentPage: store.currentPage,
  };
};

export const useBlogListingActions = () => {
  const store = useBlogListingStore();
  return {
    setPosts: store.setPosts,
    setPagination: store.setPagination,
    setLoading: store.setLoading,
    setError: store.setError,
    setCurrentPage: store.setCurrentPage,
    setBlogListingData: store.setBlogListingData,
    reset: store.reset,
  };
}; 