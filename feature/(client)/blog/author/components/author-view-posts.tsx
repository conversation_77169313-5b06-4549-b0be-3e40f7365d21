"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CalendarDays, Clock } from "lucide-react";
import { AuthorPostResponse, AuthorResponse } from "@/types/blog-api";
import { useAuthorActions } from "../store/use-author-store";
import Link from "next/link";
import Image from "next/image";

interface AuthorViewPostsProps {
  posts: AuthorPostResponse[];
  author: AuthorResponse;
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  dict: any;
  lang: string;
}

const AuthorViewPosts = ({ posts, author, pagination, dict, lang }: AuthorViewPostsProps) => {
  const authorView = dict?.client?.authorView || {};
  const { setCurrentPage } = useAuthorActions();

  const formatDate = (date: Date | string | null) => {
    if (!date) return "";
    return new Date(date).toLocaleDateString(lang, {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (posts.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">
          {authorView?.noPosts || "No posts found."}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
          {`${author.name}'s Posts`}
        </h2>
        <span className="text-sm text-muted-foreground bg-muted/50 px-3 py-1 rounded-full">
          {pagination.total} {pagination.total === 1 ? 
            (authorView?.post || "post") : 
            (authorView?.posts || "posts")
          }
        </span>
      </div>

      <div className="grid gap-6">
        {posts.map((post) => (
          <div 
            key={post.id} 
            className="group rounded-xl  transition-all bg-background/50 hover:bg-background/80"
          >
            <div className="flex gap-6">
              {post.featured_image_url && (
                <div className="flex-shrink-0 relative overflow-hidden rounded-lg w-56 h-40">
                  <Link href={`/blog/${post.slug}`}>
                    <Image
                      src={post.featured_image_url}
                      alt={post.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform"
                    />
                  </Link>
                </div>
              )}
              
              <div className="flex-1">
                <div className="space-y-3">
                  <Link href={`/blog/${post.slug}`}>
                    <h3 className="text-2xl font-semibold tracking-tight group-hover:text-primary transition-colors">
                      {post.title}
                    </h3>
                  </Link>
                  
                  {post.excerpt && (
                    <p className="text-muted-foreground line-clamp-2">
                      {post.excerpt}
                    </p>
                  )}
                  
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1.5 bg-muted/50 px-2.5 py-1 rounded-full">
                      <CalendarDays className="h-4 w-4" />
                      <span>{formatDate(post.published_at)}</span>
                    </div>
                    
                    {post.categories && post.categories.length > 0 && (
                      <div className="flex items-center gap-1.5 bg-muted/50 px-2.5 py-1 rounded-full">
                        {post.categories.map((category) => (
                          <Badge 
                            key={category.id} 
                            variant="outline" 
                            className="text-xs font-medium bg-primary/10 border:bg-primary/10 hover:bg-primary/20 rounded-full"
                          >
                            {category.name}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {pagination.total_pages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-8">
          <button
            onClick={() => handlePageChange(pagination.page - 1)}
            disabled={!pagination.has_prev}
            className="px-4 py-2 text-sm border rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted/50 transition-colors"
          >
            {authorView?.previous || "Previous"}
          </button>
          
          <div className="flex gap-1">
            {Array.from({ length: pagination.total_pages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-2 text-sm border rounded-lg transition-colors ${
                  page === pagination.page
                    ? "bg-primary text-primary-foreground border-primary"
                    : "hover:bg-muted/50"
                }`}
              >
                {page}
              </button>
            ))}
          </div>
          
          <button
            onClick={() => handlePageChange(pagination.page + 1)}
            disabled={!pagination.has_next}
            className="px-4 py-2 text-sm border rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted/50 transition-colors"
          >
            {authorView?.next || "Next"}
          </button>
        </div>
      )}
    </div>
  );
};

export default AuthorViewPosts; 