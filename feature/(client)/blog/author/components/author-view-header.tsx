"use client";

import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { CalendarDays, FileText } from "lucide-react";
import { AuthorResponse } from "@/types/blog-api";

interface AuthorViewHeaderProps {
  author: AuthorResponse;
  totalPosts: number;
  dict: any;
  lang: string;
}

const AuthorViewHeader = ({ author, totalPosts, dict, lang }: AuthorViewHeaderProps) => {
  const authorView = dict?.client?.authorView || {};
  
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "destructive";
      case "AUTHOR":
        return "default";
      default:
        return "secondary";
    }
  };

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return "";
    return new Date(date).toLocaleDateString(lang, {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="mb-8 bg-gradient-to-r from-background to-muted/20 rounded-xl border">
      <div className="p-8">
        <div className="flex flex-col sm:flex-row items-start gap-6">
          <div className="relative group">
            <Avatar className="h-24 w-24 ring-4 ring-primary/10 group-hover:ring-primary/30 transition-all">
              <AvatarImage src={author.image || ""} alt={author.name || ""} />
              <AvatarFallback className="text-2xl font-medium bg-primary/10">
                {author.name?.charAt(0)?.toUpperCase() || "A"}
              </AvatarFallback>
            </Avatar>
          </div>
          
          <div className="flex-1 space-y-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-3">
              <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                {author.name || authorView?.unknownAuthor || "Unknown Author"}
              </h1>
              <Badge 
                variant={getRoleBadgeVariant(author.role)}
                className="w-fit px-3 py-1 text-sm font-medium"
              >
                {author.role === "ADMIN" 
                  ? authorView?.administrator || "Administrator"
                  : authorView?.contentCreator || "Content Creator"
                }
              </Badge>
            </div>
            
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-2 bg-muted/50 px-3 py-1.5 rounded-full">
                <FileText className="h-4 w-4" />
                <span>
                  {totalPosts} {totalPosts === 1 ? 
                    (authorView?.post || "post") : 
                    (authorView?.posts || "posts")
                  }
                </span>
              </div>
              
              {author.created_at && (
                <div className="flex items-center gap-2 bg-muted/50 px-3 py-1.5 rounded-full">
                  <CalendarDays className="h-4 w-4" />
                  <span>
                    {authorView?.memberSince || "Member since"} {formatDate(author.created_at)}
                  </span>
                </div>
              )}
            </div>
            
            <div className="prose prose-sm max-w-none text-muted-foreground">
              <p className="leading-relaxed">
                {authorView?.authorDescription || 
                  `${author.name} is a ${author.role.toLowerCase()} on our platform, sharing insights and knowledge through their blog posts.`
                }
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthorViewHeader; 