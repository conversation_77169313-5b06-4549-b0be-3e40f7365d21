"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Mail, User, Calendar } from "lucide-react";
import { AuthorResponse } from "@/types/blog-api";

interface AuthorViewSidebarProps {
  author: AuthorResponse;
  dict: any;
  lang: string;
}

const AuthorViewSidebar = ({ author, dict, lang }: AuthorViewSidebarProps) => {
  const authorView = dict?.client?.authorView || {};

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return "";
    return new Date(date).toLocaleDateString(lang, {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "destructive";
      case "AUTHOR":
        return "default";
      default:
        return "secondary";
    }
  };

  return (
    <div className="space-y-6">
      {/* Author Info Section */}
      <div className="bg-background/50 border rounded-xl p-6">
        <div className="flex items-center space-x-3 mb-6">
          <User className="h-5 w-5 text-primary" />
          <h2 className="text-lg font-semibold">
            {authorView?.aboutAuthor || "About Author"}
          </h2>
        </div>

        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Avatar className="h-14 w-14 ring-2 ring-primary/10">
              <AvatarImage src={author.image || ""} alt={author.name || ""} />
              <AvatarFallback className="text-xl font-medium bg-primary/10">
                {author.name?.charAt(0)?.toUpperCase() || "A"}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-1">
              <h3 className="text-lg font-semibold">
                {author.name || authorView?.unknownAuthor || "Unknown Author"}
              </h3>
              <Badge 
                variant={getRoleBadgeVariant(author.role)} 
                className="text-xs px-2 py-1"
              >
                {author.role === "ADMIN" 
                  ? authorView?.administrator || "Administrator"
                  : authorView?.contentCreator || "Content Creator"
                }
              </Badge>
            </div>
          </div>

          <div className="space-y-3 text-sm">
            {/* <div className="flex items-center space-x-3 text-muted-foreground p-3 bg-muted/20 rounded-lg">
              <Mail className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">{author.email}</span>
            </div> */}
            
            {author.created_at && (
              <div className="flex items-center space-x-3 text-muted-foreground p-3 bg-muted/20 rounded-lg">
                <Calendar className="h-4 w-4 flex-shrink-0" />
                <span>
                  {authorView?.memberSince || "Member since"} {formatDate(author.created_at)}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthorViewSidebar; 