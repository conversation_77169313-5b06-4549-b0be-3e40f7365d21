"use client";

import React, { useEffect } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle } from "lucide-react";
import { useGetAuthorData } from "../hooks/use-author-data";
import { useAuthorActions, useAuthorCurrentPage } from "../store/use-author-store";
import Breadcrumb from "@/components/layout/breadcrumb";
import AuthorViewHeader from "./author-view-header";
import AuthorViewPosts from "./author-view-posts";
import AuthorViewSidebar from "./author-view-sidebar";

interface AuthorViewProps {
  dict: any;
  lang: string;
  authorId: string;
}

const AuthorView = ({ dict, lang, authorId }: AuthorViewProps) => {
  const authorView = dict?.client?.authorView || {};
  const currentPage = useAuthorCurrentPage();
  const { setAuthorData, setLoading, setError } = useAuthorActions();
  
  const {
    data: authorData,
    isLoading,
    error,
  } = useGetAuthorData(authorId, currentPage, 10);

  // Update store when data changes
  useEffect(() => {
    if (authorData) {
      setAuthorData(authorData);
    }
  }, [authorData, setAuthorData]);

  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading, setLoading]);

  useEffect(() => {
    setError(error?.message || null);
  }, [error, setError]);

  // Loading skeleton
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Author Header Skeleton */}
            <div className="lg:col-span-3">
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-20 w-20 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-8 w-48" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                </div>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
              
              {/* Posts Skeleton */}
              <div className="mt-8 space-y-6">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="border rounded-lg p-6">
                    <div className="flex space-x-4">
                      <Skeleton className="h-24 w-32 rounded" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-6 w-3/4" />
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-2/3" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Sidebar Skeleton */}
            <div className="lg:col-span-1">
              <Skeleton className="h-64 w-full" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {dict?.common?.error || "An error occurred while loading the author data."}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Not found state
  if (!authorData) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {dict?.author?.notFound || "Author not found."}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div>
      <div className="">
        <Breadcrumb
          items={[
            {
              icon: "blog",
              text: authorView?.blog || "Blog",
              href: "/blog",
            },
            {
              icon: "user",
              text: authorView?.author || "Author",
              href: `/blog/author/${authorId}`,
            },
          ]}
          lang={lang}
          dict={dict}
        />
        
        <div className="container mx-auto px-4 md:px-0 py-3">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Main Content - 75% width */}
              <div className="lg:col-span-3">
                <AuthorViewHeader
                  author={authorData.author}
                  totalPosts={authorData.pagination.total}
                  dict={dict}
                  lang={lang}
                />
                
                <AuthorViewPosts
                  posts={authorData.posts}
                  author={authorData.author}
                  pagination={authorData.pagination}
                  dict={dict}
                  lang={lang}
                />
              </div>
              
              {/* Sidebar - 25% width */}
              <div className="lg:col-span-1">
                <AuthorViewSidebar
                  author={authorData.author}
                  dict={dict}
                  lang={lang}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthorView; 