import { create } from "zustand";
import { AuthorPageResponse, AuthorResponse } from "@/types/blog-api";

interface AuthorViewState {
  // Data
  authorData: AuthorPageResponse | null;
  currentPage: number;
  
  // UI State
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setAuthorData: (data: AuthorPageResponse | null) => void;
  setCurrentPage: (page: number) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

// Initial state
const initialState = {
  authorData: null,
  currentPage: 1,
  isLoading: false,
  error: null,
};

// Main store
export const useAuthorStore = create<AuthorViewState>((set) => ({
  // Data
  ...initialState,
  
  // Actions
  setAuthorData: (authorData) => set({ authorData }),
  setCurrentPage: (currentPage) => set({ currentPage }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  reset: () => set(initialState),
}));

// Individual selectors to avoid object creation
export const useAuthorData = () => useAuthorStore((state) => state.authorData);
export const useAuthorInfo = () => useAuthorStore((state) => state.authorData?.author);
export const useAuthorPosts = () => useAuthorStore((state) => state.authorData?.posts);
export const useAuthorPagination = () => useAuthorStore((state) => state.authorData?.pagination);
export const useAuthorCurrentPage = () => useAuthorStore((state) => state.currentPage);
export const useAuthorIsLoading = () => useAuthorStore((state) => state.isLoading);
export const useAuthorError = () => useAuthorStore((state) => state.error);

// Action selectors
export const useAuthorSetData = () => useAuthorStore((state) => state.setAuthorData);
export const useAuthorSetPage = () => useAuthorStore((state) => state.setCurrentPage);
export const useAuthorSetLoading = () => useAuthorStore((state) => state.setLoading);
export const useAuthorSetError = () => useAuthorStore((state) => state.setError);
export const useAuthorReset = () => useAuthorStore((state) => state.reset);

// Compound selectors for backward compatibility
export const useAuthorUI = () => {
  const isLoading = useAuthorIsLoading();
  const error = useAuthorError();
  const currentPage = useAuthorCurrentPage();
  return { isLoading, error, currentPage };
};

export const useAuthorActions = () => {
  const setAuthorData = useAuthorSetData();
  const setCurrentPage = useAuthorSetPage();
  const setLoading = useAuthorSetLoading();
  const setError = useAuthorSetError();
  const reset = useAuthorReset();
  return { setAuthorData, setCurrentPage, setLoading, setError, reset };
}; 