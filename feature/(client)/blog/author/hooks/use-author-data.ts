import { useQuery } from "@tanstack/react-query";
import { AuthorPageResponse } from "@/types/blog-api";

// API base URL
const API_BASE = "/api/v1/blog/author";

// Fetch author data with posts
async function fetchAuthorData(
  authorId: string,
  page: number = 1,
  limit: number = 10
): Promise<AuthorPageResponse> {
  const response = await fetch(
    `${API_BASE}/${authorId}?page=${page}&limit=${limit}`
  );
  
  if (!response.ok) {
    throw new Error(`Failed to fetch author data: ${response.statusText}`);
  }
  
  const data = await response.json();
  return data.data;
}

// Hook to get author data with posts
export function useGetAuthorData(
  authorId: string,
  page: number = 1,
  limit: number = 10
) {
  return useQuery({
    queryKey: ["author-data", authorId, page, limit],
    queryFn: () => fetchAuthorData(authorId, page, limit),
    enabled: !!authorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to get author data only (without posts)
export function useGetAuthorInfo(authorId: string) {
  return useQuery({
    queryKey: ["author-info", authorId],
    queryFn: () => fetchAuthorData(authorId, 1, 1),
    enabled: !!authorId,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    select: (data) => data.author, // Only return author info
  });
} 