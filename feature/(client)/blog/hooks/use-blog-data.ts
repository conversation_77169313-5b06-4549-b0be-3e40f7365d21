import { useQuery } from "@tanstack/react-query";
import { BlogPostResponse, BlogCategoryResponse, BlogTagResponse } from "@/types/blog-api";

// API base URL
const API_BASE = "/api/v1/blog";

// Fetch blog post by slug
async function fetchBlogPostBySlug(slug: string): Promise<BlogPostResponse> {
  const response = await fetch(`${API_BASE}/${slug}`);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch blog post: ${response.statusText}`);
  }
  
  const data = await response.json();
  return data.data;
}

// Fetch blog categories
async function fetchBlogCategories(): Promise<BlogCategoryResponse[]> {
  const response = await fetch(`/api/blog/categories`);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch blog categories: ${response.statusText}`);
  }
  
  const data = await response.json();
  return data.data;
}

// Fetch blog tags
async function fetchBlogTags(): Promise<BlogTagResponse[]> {
  const response = await fetch(`/api/blog/tags`);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch blog tags: ${response.statusText}`);
  }
  
  const data = await response.json();
  return data.data;
}

// Hook to get blog post by slug
export function useGetBlogPostBySlug(slug: string) {
  return useQuery({
    queryKey: ["blog-post", slug],
    queryFn: () => fetchBlogPostBySlug(slug),
    enabled: !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to get blog categories
export function useGetBlogCategories() {
  return useQuery({
    queryKey: ["blog-categories"],
    queryFn: fetchBlogCategories,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Hook to get blog tags
export function useGetBlogTags() {
  return useQuery({
    queryKey: ["blog-tags"],
    queryFn: fetchBlogTags,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
} 