import React from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { BlogListingResponse } from "@/types/blog-api";
import { useBlogListingActions } from "../store/use-blog-listing-store";

// Fetch blog listing data
const fetchBlogListing = async (page: number = 1): Promise<BlogListingResponse> => {
  const response = await fetch(`/api/v1/blog?page=${page}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch blog listing");
  }

  const data = await response.json();
  return data.data;
};

// Hook to get blog listing data
export const useGetBlogListing = (page: number = 1) => {
  const { setBlogListingData, setLoading, setError } = useBlogListingActions();

  const query = useQuery({
    queryKey: ["blog-listing", page],
    queryFn: () => fetchBlogListing(page),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
  });

  // Update store when data changes
  React.useEffect(() => {
    if (query.data) {
      setBlogListingData(query.data);
      setLoading(false);
      setError(null);
    }
  }, [query.data, setBlogListingData, setLoading, setError]);

  React.useEffect(() => {
    if (query.error) {
      setError(query.error.message);
      setLoading(false);
    }
  }, [query.error, setError, setLoading]);

  React.useEffect(() => {
    setLoading(query.isLoading);
  }, [query.isLoading, setLoading]);

  return query;
};

// Hook to prefetch next page
export const usePrefetchBlogListing = () => {
  const queryClient = useQueryClient();
  
  const prefetchNextPage = (currentPage: number) => {
    queryClient.prefetchQuery({
      queryKey: ["blog-listing", currentPage + 1],
      queryFn: () => fetchBlogListing(currentPage + 1),
      staleTime: 1000 * 60 * 5,
    });
  };

  return { prefetchNextPage };
}; 