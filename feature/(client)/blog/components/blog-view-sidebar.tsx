"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { BlogPostResponse } from "@/types/blog-api";
import { Settings } from "lucide-react";

interface BlogViewSidebarProps {
  blogPost: BlogPostResponse;
  dict: any;
  lang: string;
}

const BlogViewSidebar = ({ blogPost, dict, lang }: BlogViewSidebarProps) => {
  const blogView = dict.client.blogView;

  return (
    <div className="sticky top-[75px] space-y-4">
      <div className="w-full">
        <div className="pb-2">
          <div className="text-lg font-semibold flex items-center gap-2 text-gray-800">
            <Settings className="h-5 w-5 text-primary" />
            <span>{blogView.recentPosts}</span>
          </div>
          <div className="mt-1 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent" />
        </div>
        <div>
          {blogPost.recent_posts && blogPost.recent_posts.length > 0 ? (
            <div className="bg-white overflow-hidden">
              <div className="space-y-4">
                {blogPost.recent_posts.map((post) => (
                  <a
                    key={post.id}
                    href={`/blog/${post.slug}`}
                    className="flex items-start gap-3 group hover:bg-gray-50 p-2 -mx-2 rounded transition-colors duration-150"
                  >
                    {post.featured_image_url && (
                      <div className="relative w-16 h-16 flex-shrink-0 rounded-md overflow-hidden border border-gray-100">
                        <img
                          src={post.featured_image_url}
                          alt={post.title}
                          className="object-cover w-full h-full"
                          loading="lazy"
                        />
                      </div>
                    )}
                    <div className="flex-grow">
                      <h4 className="font-medium text-gray-800 line-clamp-3 group-hover:text-primary transition-colors duration-200 text-sm leading-snug">
                        {post.title}
                      </h4>
                      <div className="mt-1 h-px bg-gray-100 last-of-type:hidden" />
                    </div>
                  </a>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-6 text-gray-400 bg-gray-50 rounded-lg">
              <p className="text-sm">{blogView.noRecentPosts}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlogViewSidebar;
