"use client";

import React from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle } from "lucide-react";
import { useGetBlogListing } from "../hooks/use-blog-listing-data";
import { useBlogListingData, useBlogListingActions } from "../store/use-blog-listing-store";
import BlogListingGallery from "./blog-listing-gallery";
import BlogListingGrid from "./blog-listing-grid";
import BlogListingPagination from "./blog-listing-pagination";
import Breadcrumb from "@/components/layout/breadcrumb";

interface BlogListingProps {
  dict: any;
  lang: string;
  initialPage?: number;
}

const BlogListing = ({ dict, lang, initialPage = 1 }: BlogListingProps) => {
  const blogListing = dict?.client?.blogListing || {};
  const { posts, pagination, isLoading, error, currentPage } = useBlogListingData();
  const { setCurrentPage } = useBlogListingActions();
  
  // Initialize current page
  React.useEffect(() => {
    if (initialPage !== currentPage) {
      setCurrentPage(initialPage);
    }
  }, [initialPage, currentPage, setCurrentPage]);
  
  const {
    data: blogListingData,
    isLoading: queryLoading,
    error: queryError,
  } = useGetBlogListing(currentPage || initialPage);

  // Loading skeleton
  if (queryLoading || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="space-y-8">
            {/* Gallery skeleton */}
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
              <div className="lg:col-span-3">
                <Skeleton className="h-96 w-full rounded-lg" />
              </div>
              <div className="lg:col-span-2 space-y-6">
                <Skeleton className="h-44 w-full rounded-lg" />
                <Skeleton className="h-44 w-full rounded-lg" />
              </div>
            </div>
            
            {/* Grid skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 12 }).map((_, index) => (
                <div key={index} className="space-y-4">
                  <Skeleton className="h-48 w-full rounded-lg" />
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (queryError || error) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || queryError?.message || dict?.common?.error || "An error occurred while loading blog posts."}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // No posts state
  if (!posts || posts.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {blogListing?.noPosts || "No blog posts found."}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Separate posts for gallery and grid
  const galleryPosts = currentPage === 1 ? posts.slice(0, 3) : [];
  const gridPosts = currentPage === 1 ? posts.slice(3) : posts;

  return (
    <div>
      <div className="">
        <Breadcrumb
          items={[
            {
              icon: "blog",
              text: blogListing?.blog || "Blog",
              href: "/blog",
            },
          ]}
          lang={lang}
          dict={dict}
        />
        
        <div className="container mx-auto px-4 md:px-0 py-3">
          <div className="max-w-7xl mx-auto">
            <div className="space-y-12">
              {/* Gallery layout for first 3 posts (only on page 1) */}
              {galleryPosts.length > 0 && (
                <BlogListingGallery
                  posts={galleryPosts}
                  dict={dict}
                  lang={lang}
                />
              )}
              
              {/* Grid layout for remaining posts */}
              {gridPosts.length > 0 && (
                <BlogListingGrid
                  posts={gridPosts}
                  dict={dict}
                  lang={lang}
                />
              )}
              
              {/* Pagination */}
              {pagination && pagination.total_pages > 1 && (
                <BlogListingPagination
                  pagination={pagination}
                  dict={dict}
                  lang={lang}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogListing; 