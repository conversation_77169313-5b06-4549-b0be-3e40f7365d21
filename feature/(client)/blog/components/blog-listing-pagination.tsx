"use client";

import React from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useBlogListingActions } from "../store/use-blog-listing-store";

interface BlogListingPaginationProps {
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  dict: any;
  lang: string;
}

const BlogListingPagination = ({ pagination, dict, lang }: BlogListingPaginationProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setCurrentPage } = useBlogListingActions();
  
  const blogListing = dict?.client?.blogListing || {};
  const { page, total_pages, has_next, has_prev } = pagination;

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    const params = new URLSearchParams(searchParams);
    if (newPage === 1) {
      params.delete('page');
    } else {
      params.set('page', newPage.toString());
    }
    
    const queryString = params.toString();
    const newUrl = queryString ? `/blog?${queryString}` : '/blog';
    router.push(newUrl);
  };

  const generatePageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    if (total_pages <= maxVisiblePages) {
      // Show all pages if total pages is less than max visible
      for (let i = 1; i <= total_pages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages with ellipsis
      if (page <= 3) {
        // Show first 3 pages + ellipsis + last page
        for (let i = 1; i <= 3; i++) {
          pages.push(i);
        }
        if (total_pages > 4) {
          pages.push('...');
          pages.push(total_pages);
        }
      } else if (page >= total_pages - 2) {
        // Show first page + ellipsis + last 3 pages
        pages.push(1);
        if (total_pages > 4) {
          pages.push('...');
        }
        for (let i = total_pages - 2; i <= total_pages; i++) {
          pages.push(i);
        }
      } else {
        // Show first page + ellipsis + current page group + ellipsis + last page
        pages.push(1);
        pages.push('...');
        pages.push(page - 1);
        pages.push(page);
        pages.push(page + 1);
        pages.push('...');
        pages.push(total_pages);
      }
    }
    
    return pages;
  };

  const pageNumbers = generatePageNumbers();

  return (
    <div className="flex items-center justify-center space-x-2">
      {/* Previous button */}
      {has_prev ? (
        <button
          onClick={() => handlePageChange(page - 1)}
          className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-900 transition-colors"
        >
          <ChevronLeft className="h-4 w-4" />
          <span className="hidden sm:inline">
            {blogListing?.previous || "Previous"}
          </span>
        </button>
      ) : (
        <div className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-200 rounded-lg cursor-not-allowed">
          <ChevronLeft className="h-4 w-4" />
          <span className="hidden sm:inline">
            {blogListing?.previous || "Previous"}
          </span>
        </div>
      )}

      {/* Page numbers */}
      <div className="flex items-center space-x-1">
        {pageNumbers.map((pageNum, index) => (
          <React.Fragment key={index}>
            {pageNum === '...' ? (
              <span className="px-3 py-2 text-sm text-gray-500">...</span>
            ) : (
              <button
                onClick={() => handlePageChange(pageNum as number)}
                className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  pageNum === page
                    ? 'bg-primary text-white shadow-sm'
                    : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                {pageNum}
              </button>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Next button */}
      {has_next ? (
        <button
          onClick={() => handlePageChange(page + 1)}
          className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-900 transition-colors"
        >
          <span className="hidden sm:inline">
            {blogListing?.next || "Next"}
          </span>
          <ChevronRight className="h-4 w-4" />
        </button>
      ) : (
        <div className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-200 rounded-lg cursor-not-allowed">
          <span className="hidden sm:inline">
            {blogListing?.next || "Next"}
          </span>
          <ChevronRight className="h-4 w-4" />
        </div>
      )}
    </div>
  );
};

export default BlogListingPagination; 