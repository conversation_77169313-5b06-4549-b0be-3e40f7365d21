"use client";

import React from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle } from "lucide-react";
import { useGetBlogPostBySlug } from "../hooks/use-blog-data";
import Breadcrumb from "@/components/layout/breadcrumb";
import BlogViewContent from "./blog-view-content";
import BlogViewToc from "./blog-view-toc";
import BlogViewSidebar from "./blog-view-sidebar";

interface BlogViewProps {
  dict: any;
  lang: string;
  slug: string;
}

const BlogView = ({ dict, lang, slug }: BlogViewProps) => {
  const blogView = dict?.client?.blogView || {};
  
  const {
    data: blogPost,
    isLoading,
    error,
  } = useGetBlogPostBySlug(slug);

  // Loading skeleton
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
            {/* TOC Skeleton */}
            <div className="lg:col-span-1">
              <Skeleton className="h-64 w-full" />
            </div>
            
            {/* Content Skeleton */}
            <div className="lg:col-span-3">
              <div className="space-y-6">
                <Skeleton className="h-8 w-3/4 mb-4" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3 mb-8" />
                <Skeleton className="h-64 w-full" />
                <div className="space-y-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            </div>
            
            {/* Sidebar Skeleton */}
            <div className="lg:col-span-1">
              <Skeleton className="h-64 w-full" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {dict?.common?.error || "An error occurred while loading the blog post."}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Not found state
  if (!blogPost) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {dict?.blog?.notFound || "Blog post not found."}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div>
      <div className="">
        <Breadcrumb
          items={[
            {
              icon: "blog",
              text: blogView?.blog || "Blog",
              href: "/blog",
            },
          ]}
          lang={lang}
          dict={dict}
        />
        
        <div className="container mx-auto px-4 md:px-0 py-3">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
              {/* Table of Contents - 20% width */}
              <div className="lg:col-span-1 order-2 lg:order-1">
                <BlogViewToc
                  blogPost={blogPost}
                  dict={dict}
                  lang={lang}
                />
              </div>
              
              {/* Main Content - 55% width */}
              <div className="lg:col-span-2 order-1 lg:order-2">
                <BlogViewContent
                  blogPost={blogPost}
                  dict={dict}
                  lang={lang}
                />
              </div>
              
              {/* Sidebar - 25% width */}
              <div className="lg:col-span-1 order-3">
                <BlogViewSidebar
                  blogPost={blogPost}
                  dict={dict}
                  lang={lang}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogView; 