"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { formatDate } from "@/lib/utils";
import { BlogPostResponse } from "@/types/blog-api";
import { Calendar, Tag, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import {
    FacebookIcon,
    FacebookShareButton,
    LinkedinIcon,
    LinkedinShareButton,
    TwitterIcon,
    TwitterShareButton,
    WhatsappIcon,
    WhatsappShareButton,
    XIcon
} from "react-share";

// Type declaration for Twitter widgets
declare global {
  interface Window {
    twttr?: {
      widgets?: {
        load: () => void;
        createTweet?: (
          tweetId: string,
          element: HTMLElement,
          options?: any
        ) => Promise<HTMLElement>;
      };
      ready?: (callback: () => void) => void;
    };
  }
}

interface BlogViewContentProps {
  blogPost: BlogPostResponse;
  dict: any;
  lang: string;
}

const BlogViewContent = ({ blogPost, dict, lang }: BlogViewContentProps) => {
  const blogView = dict.client.blogView;
  const [isImageZoomed, setIsImageZoomed] = useState(false);
  const [zoomedImageSrc, setZoomedImageSrc] = useState<string>("");

  // Function to load Twitter widgets script
  const loadTwitterWidgets = () => {
    if (typeof window === "undefined") return;

    if (!window.twttr) {
      const script = document.createElement("script");
      script.src = "https://platform.twitter.com/widgets.js";
      script.async = true;
      script.charset = "utf-8";
      script.onload = () => {
        setTimeout(() => {
          if (window.twttr && window.twttr.widgets) {
            window.twttr.widgets.load();
          }
        }, 100);
      };
      document.head.appendChild(script);
    } else if (window.twttr && window.twttr.widgets) {
      window.twttr.widgets.load();
    }
  };

  // Handle escape key to close zoom
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isImageZoomed) {
        setIsImageZoomed(false);
      }
    };

    if (isImageZoomed) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isImageZoomed]);

  // Function to handle image zoom
  const handleImageZoom = (imageSrc: string) => {
    setZoomedImageSrc(imageSrc);
    setIsImageZoomed(true);
  };

  // Enhanced code block functionality and Twitter embed processing
  useEffect(() => {
    // Set up MutationObserver to watch for dynamically added images
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "childList") {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              // Check if the added node is an image or contains images
              if (element.tagName === "IMG" || element.querySelector("img")) {
                setTimeout(() => addImageZoomToContent(), 100);
              }
            }
          });
        }
      });
    });

    // Start observing the blog content container
    const blogContent = document.querySelector(".blog-view-content");
    if (blogContent) {
      observer.observe(blogContent, {
        childList: true,
        subtree: true,
      });
    }
    const enhanceCodeBlocks = () => {
      const codeBlocks = document.querySelectorAll(
        ".blog-view-content pre:not(.code-block-content pre)"
      );

      codeBlocks.forEach((preElement) => {
        const codeElement = preElement.querySelector("code");
        if (!codeElement) return;

        // Extract language from class name
        const className = codeElement.className || "";
        const languageMatch = className.match(/language-(\w+)/);
        const language = languageMatch ? languageMatch[1] : "text";

        // Get the code content
        const codeContent = codeElement.textContent || "";

        // Create enhanced structure
        const wrapper = document.createElement("div");
        wrapper.className = "code-block-wrapper";

        // Create header with language and copy button
        const header = document.createElement("div");
        header.className = "code-block-header";
        header.innerHTML = `
          <div class="language-label">${language}</div>
          <button class="copy-button" title="Copy code">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
              <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
            </svg>
          </button>
        `;

        // Create content container
        const contentContainer = document.createElement("div");
        contentContainer.className = "code-block-content";

        // Clone the original pre element and modify it
        const enhancedPre = preElement.cloneNode(true) as HTMLElement;
        enhancedPre.className = `language-${language}`;

        // Add copy functionality
        const copyButton = header.querySelector(".copy-button");
        if (copyButton) {
          copyButton.addEventListener("click", async () => {
            try {
              await navigator.clipboard.writeText(codeContent);
              copyButton.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="20,6 9,17 4,12"/>
                </svg>
              `;
              setTimeout(() => {
                copyButton.innerHTML = `
                   <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                     <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                     <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                   </svg>
                 `;
              }, 2000);
            } catch (err) {
              console.error("Failed to copy text: ", err);
            }
          });
        }

        // Assemble the enhanced structure
        contentContainer.appendChild(enhancedPre);
        wrapper.appendChild(header);
        wrapper.appendChild(contentContainer);

        // Replace the original pre element
        preElement.parentNode?.replaceChild(wrapper, preElement);
      });
    };

    // Process Twitter embeds
    const processTwitterEmbeds = () => {
      const twitterEmbeds = document.querySelectorAll(
        ".blog-view-content .twitter-embed-content[data-embed-html]"
      );

      twitterEmbeds.forEach((embed) => {
        const embedHtml = embed.getAttribute("data-embed-html");
        if (embedHtml && embed.innerHTML !== embedHtml) {
          // Decode HTML entities
          const decodedHtml = embedHtml
            .replace(/&lt;/g, "<")
            .replace(/&gt;/g, ">")
            .replace(/&quot;/g, '"')
            .replace(/&#x27;/g, "'")
            .replace(/&amp;/g, "&");

          embed.innerHTML = decodedHtml;
        }
      });

      // Load Twitter widgets after processing embeds
      setTimeout(() => {
        loadTwitterWidgets();
      }, 200);
    };

    // Add zoom functionality to content images
    const addImageZoomToContent = () => {
      const contentImages = document.querySelectorAll(".blog-view-content img");

      contentImages.forEach((img) => {
        const imageElement = img as HTMLImageElement;

        // Skip if no src
        if (!imageElement.src) return;

        // Remove existing event listeners to prevent duplicates
        const existingHandler = imageElement.onclick;
        if (existingHandler) {
          imageElement.removeEventListener("click", existingHandler);
        }

        // Add cursor pointer and click handler
        imageElement.style.cursor = "zoom-in";

        // Create new handler function
        const clickHandler = (e: Event) => {
          e.preventDefault();
          e.stopPropagation();
          handleImageZoom(imageElement.src);
        };

        // Add the event listener
        imageElement.addEventListener("click", clickHandler);

        // Store reference to the handler for cleanup
        (imageElement as any)._zoomHandler = clickHandler;
      });
    };

    // Run enhancements after content is rendered
    const timer1 = setTimeout(() => {
      enhanceCodeBlocks();
      processTwitterEmbeds();
      addImageZoomToContent();
    }, 100);

    // Run image zoom setup again after a longer delay to catch any late-loading images
    const timer2 = setTimeout(() => {
      addImageZoomToContent();
    }, 500);

    // Also run when images finish loading
    const timer3 = setTimeout(() => {
      addImageZoomToContent();
    }, 1000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      observer.disconnect();
    };
  }, [blogPost.content]);

  const shareUrl =
    typeof window !== "undefined"
      ? window.location.href
      : `https://yourdomain.com/${lang}/blog/${blogPost.slug}`;
  const shareTitle = blogPost.title;

  return (
    <div className="w-full">
      {/* Full Screen Image Zoom Overlay */}
      {isImageZoomed && zoomedImageSrc && (
        <div
          className="fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4 cursor-zoom-out"
          onClick={() => setIsImageZoomed(false)}
        >
          <button
            onClick={() => setIsImageZoomed(false)}
            className="absolute top-4 right-4 z-10 p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors text-white"
          >
            <X className="h-6 w-6" />
          </button>
          <div className="relative max-w-7xl max-h-full w-full h-full">
            <Image
              src={zoomedImageSrc}
              alt="Zoomed image"
              fill
              className="object-contain cursor-zoom-out"
              sizes="100vw"
              quality={100}
              priority
            />
          </div>
        </div>
      )}

      {/* Hero Section */}
      <div className="space-y-6 mb-8">
        {/* Featured Image with Categories */}
        {blogPost.featured_image_url && (
          <div
            className="relative w-full h-64 md:h-96 rounded-lg overflow-hidden cursor-zoom-in"
            onClick={() => handleImageZoom(blogPost.featured_image_url!)}
          >
            <Image
              src={blogPost.featured_image_url}
              alt={blogPost.title}
              fill
              className="object-cover"
            />
            {/* Categories Overlay */}
            {blogPost.categories && blogPost.categories.length > 0 && (
              <div className="absolute top-4 left-4 flex flex-wrap gap-2 z-10">
                {blogPost.categories
                  .filter((category) => category.parent_id === null)
                  .map((category) => (
                    <Link
                      key={category.id}
                      href={`/blog/category/${category.slug}`}
                      target="_blank"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Badge
                        variant="outline"
                        className="text-sm font-medium transition-colors cursor-pointer rounded-full bg-primary/90 backdrop-blur-sm text-white hover:bg-primary"
                      >
                        {category.name}
                      </Badge>
                    </Link>
                  ))}
              </div>
            )}
          </div>
        )}

        {/* Title */}
        <div className="space-y-4">
          <h1 className="text-3xl md:text-4xl font-bold leading-tight">
            {blogPost.title}
          </h1>

          {/* Excerpt */}
          {blogPost.excerpt && (
            <p className="text-base text-gray-600 leading-relaxed">
              {blogPost.excerpt}
            </p>
          )}

          {/* Meta Information */}
          <div className="flex flex-col md:flex-row items-start md:items-center gap-4 border-t border-b border-gray-200 py-4 my-4">
            <div className="w-full flex flex-row items-center justify-between">
              <div className="flex items-center gap-3">
                {/* Avatar */}
                <Avatar className="h-10 w-10 border-2 border-primary/20">
                  <AvatarImage
                    src={blogPost.author?.image || undefined}
                    className="object-cover"
                  />
                  <AvatarFallback className="bg-primary/10 text-primary font-medium">
                    {blogPost.author?.name?.charAt(0) || "A"}
                  </AvatarFallback>
                </Avatar>

                {/* Author name and date column */}
                <div className="flex flex-col">
                  <Link href={`/blog/author/${blogPost.author?.id}`} className="hover:underline">
                    <span className="text-sm font-semibold text-gray-900">
                      {blogPost.author?.name || "Anonymous"}
                    </span>
                  </Link>
                  <div className="flex items-center gap-1 text-xs text-gray-600">
                    <Calendar className="h-3 w-3" />
                    <span>
                      {blogPost.published_at
                        ? formatDate(new Date(blogPost.published_at), lang)
                        : formatDate(new Date(blogPost.created_at), lang)}
                    </span>
                    <span className="mx-0.5 text-gray-400">•</span>
                    <span>
                      {blogPost.content &&
                        `${Math.ceil(blogPost.content.split(/\s+/).length / 200)} min read`}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex gap-2 bg-gray-50 p-1.5 rounded-lg">
                <FacebookShareButton url={shareUrl}>
                  <FacebookIcon
                    size={28}
                    round
                    className="hover:opacity-80 transition-opacity"
                  />
                </FacebookShareButton>
                <TwitterShareButton url={shareUrl} title={shareTitle}>
                  <XIcon
                    size={28}
                    round
                    className="hover:opacity-80 transition-opacity"
                  />
                </TwitterShareButton>
                <LinkedinShareButton url={shareUrl} title={shareTitle}>
                  <LinkedinIcon
                    size={28}
                    round
                    className="hover:opacity-80 transition-opacity"
                  />
                </LinkedinShareButton>
                <WhatsappShareButton url={shareUrl} title={shareTitle}>
                  <WhatsappIcon
                    size={28}
                    round
                    className="hover:opacity-80 transition-opacity"
                  />
                </WhatsappShareButton>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div>
        <div className="">
          <div
            className="prose prose-lg max-w-none
              prose-headings:font-bold prose-headings:text-gray-900
              prose-h1:text-3xl prose-h2:text-2xl prose-h3:text-xl
              prose-p:text-gray-700 prose-p:leading-relaxed
              prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline
              prose-ul:my-6 prose-ol:my-6
              prose-li:my-2
              prose-blockquote:border-l-4 prose-blockquote:border-blue-500 prose-blockquote:pl-4 prose-blockquote:italic
              prose-code:bg-gray-100 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm
              prose-pre:bg-gray-900 prose-pre:text-white prose-pre:p-4 prose-pre:rounded-lg prose-pre:overflow-x-auto
              prose-img:rounded-lg prose-img:shadow-md
              blog-view-content"
            dangerouslySetInnerHTML={{ __html: blogPost.content }}
          />
        </div>
      </div>
      {/* Tags */}
      {blogPost.tags && blogPost.tags.length > 0 && (
        <div className="flex items-center gap-2 flex-wrap">
          <Tag className="h-4 w-4 text-gray-500" />
          <div className="flex flex-wrap gap-1">
            {blogPost.tags.map((tag) => (
              <Badge key={tag.id} variant="outline" className="text-xs">
                {tag.name}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Related Posts */}
      {blogPost.related_posts && blogPost.related_posts.length > 0 && (
        <div className="mt-8">
          <h3 className="text-2xl font-bold mb-6 text-gray-900">
            {blogView.relatedPosts}
          </h3>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
            {blogPost.related_posts.map((post) => (
              <div
                key={post.id}
                className="group rounded-xl overflow-hidden border border-gray-200 hover:border-gray-300 transition-all duration-200 bg-white"
              >
                <a href={`/blog/${post.slug}`} className="block h-full">
                  {post.featured_image_url && (
                    <div className="relative w-full h-48">
                      <Image
                        src={post.featured_image_url}
                        alt={post.title}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent" />
                    </div>
                  )}
                  <div className="p-5">
                    <h4 className="font-semibold text-lg line-clamp-2 leading-snug mb-2 text-gray-800 group-hover:text-primary transition-colors">
                      {post.title}
                    </h4>
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <span className="text-sm text-primary font-medium">
                        Read more →
                      </span>
                    </div>
                  </div>
                </a>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default BlogViewContent;
