"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { BlogPostResponse } from "@/types/blog-api";
import { List } from "lucide-react";

interface BlogViewTocProps {
  blogPost: BlogPostResponse;
  dict: any;
  lang: string;
}

interface TocItem {
  id: string;
  text: string;
  level: number;
}

const BlogViewToc = ({ blogPost, dict, lang }: BlogViewTocProps) => {
  const [tocItems, setTocItems] = useState<TocItem[]>([]);
  const [activeId, setActiveId] = useState<string>("");

  useEffect(() => {
    // Add ids to actual DOM elements after content is rendered
    const addIdsToHeadings = () => {
      // Find the content container using the prose class
      const contentElement = document.querySelector('.prose');
      if (contentElement) {
        const headings = contentElement.querySelectorAll('h2, h3, h4');
        const items: TocItem[] = [];
        
        headings.forEach((heading, index) => {
          const text = heading.textContent || '';
          const id = `heading-${index}`;
          const level = parseInt(heading.tagName.charAt(1)); // Get level from h2, h3, h4
          
          // Add id to the heading for scrolling
          heading.id = id;
          
          // Only show h2 headings in TOC for now
          if (level === 2) {
            items.push({
              id,
              text,
              level
            });
          }
        });
        
        setTocItems(items);
      }
    };

    // Use a timeout to ensure content is rendered
    const timer = setTimeout(addIdsToHeadings, 50);
    return () => clearTimeout(timer);
  }, [blogPost.content]);

  const handleHeadingClick = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      // Calculate offset for fixed header
      const headerHeight = 70; // Adjust this based on your header height
      const elementPosition = element.offsetTop - headerHeight;
      
      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      });
      
      setActiveId(id);
    }
  };

  // Intersection observer to highlight active heading
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveId(entry.target.id);
          }
        });
      },
      {
        rootMargin: '-20% 0px -80% 0px'
      }
    );

    tocItems.forEach((item) => {
      const element = document.getElementById(item.id);
      if (element) {
        observer.observe(element);
      }
    });

    return () => observer.disconnect();
  }, [tocItems]);

  if (tocItems.length === 0) {
    return null;
  }

  return (
    <div className="sticky top-[75px] rounded-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
      <div className="w-full">
        <div className="pb-3 border-b border-gray-100 dark:border-gray-700 mb-3">
          <div className="text-lg font-semibold flex items-center gap-2 text-gray-800 dark:text-gray-200">
            <List className="h-5 w-5 text-primary" />
            Table of Contents
          </div>
        </div>
        <div>
          <nav className="space-y-1.5">
            {tocItems.map((item, index) => (
              <button
                key={item.id}
                onClick={() => handleHeadingClick(item.id)}
                className={`block w-full text-left p-2.5 rounded-lg text-sm transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 hover:cursor-pointer ${
                  activeId === item.id
                    ? 'bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-300 border-l-4 border-primary font-medium'
                    : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100'
                }`}
              >
                <span className={`font-mono text-xs mr-2 ${
                  activeId === item.id ? 'text-primary dark:text-primary-300' : 'text-gray-500 dark:text-gray-400'
                }`}>
                  {index + 1}.
                </span>
                {item.text}
              </button>
            ))}
          </nav>
        </div>
      </div>
    </div>
  );
};

export default BlogViewToc;
