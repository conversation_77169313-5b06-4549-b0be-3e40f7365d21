"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { CalendarDays } from "lucide-react";
import { BlogListingPost } from "@/types/blog-api";

interface BlogListingGridProps {
  posts: BlogListingPost[];
  dict: any;
  lang: string;
}

const BlogListingGrid = ({ posts, dict, lang }: BlogListingGridProps) => {
  const blogListing = dict?.client?.blogListing || {};

  const formatDate = (date: Date | string | null) => {
    if (!date) return "";
    return new Date(date).toLocaleDateString(lang, {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (posts.length === 0) return null;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {posts.map((post) => (
        <div key={post.id} className="group">
          <Link href={`/blog/${post.slug}`}>
            <div className="space-y-4">
              {/* Image */}
              <div className="relative h-48 rounded-xl overflow-hidden bg-gray-100">
                {/* Category */}
                {post.parent_category && (
                  <div className="absolute top-3 left-3 z-10">
                    <Badge 
                      variant="outline" 
                      className="text-xs bg-primary border-primary hover:bg-primary/90 rounded-full text-white"
                    >
                      {post.parent_category.name}
                    </Badge>
                  </div>
                )}
                {post.featured_image_url ? (
                  <Image
                    src={post.featured_image_url}
                    alt={post.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                    <span className="text-gray-400 text-lg">No Image</span>
                  </div>
                )}
              </div>
              
              {/* Content */}
              <div className="space-y-3">                
                {/* Title */}
                <h3 className="font-semibold text-xl line-clamp-2 group-hover:text-primary transition-colors">
                  {post.title}
                </h3>
                
                {/* Excerpt */}
                {post.excerpt && (
                  <p className="text-gray-600 line-clamp-3 text-sm leading-relaxed">
                    {post.excerpt}
                  </p>
                )}
                
                {/* Author and Date */}
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {post.author.image && (
                      <img
                        src={post.author.image}
                        alt={post.author.name || "Author"}
                        className="w-5 h-5 rounded-full object-cover"
                      />
                    )}
                    <span className="text-sm text-gray-600">{post.author.name || "Anonymous"}</span>
                  </div>
                  <div className="h-4 w-px bg-gray-300" />
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <span>{formatDate(post.published_at)}</span>
                  </div>
                </div>
              </div>
            </div>
          </Link>
        </div>
      ))}
    </div>
  );
};

export default BlogListingGrid; 