"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { CalendarDays } from "lucide-react";
import { BlogListingPost } from "@/types/blog-api";

interface BlogListingGalleryProps {
  posts: BlogListingPost[];
  dict: any;
  lang: string;
}

const BlogListingGallery = ({ posts, dict, lang }: BlogListingGalleryProps) => {
  const blogListing = dict?.client?.blogListing || {};

  const formatDate = (date: Date | string | null) => {
    if (!date) return "";
    return new Date(date).toLocaleDateString(lang, {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (posts.length === 0) return null;

  const [firstPost, ...otherPosts] = posts;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-5 gap-3">
      {/* First post - 60% width (3 columns) */}
      <div className="lg:col-span-3">
        <div className="group relative h-[450px] rounded-xl overflow-hidden bg-gray-100">
          <Link href={`/blog/${firstPost.slug}`}>
            {firstPost.featured_image_url ? (
              <Image
                src={firstPost.featured_image_url}
                alt={firstPost.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 60vw, 50vw"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                <span className="text-gray-400 text-lg">No Image</span>
              </div>
            )}
            
            {/* Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
            
            {/* Content */}
            <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
              {/* Category */}
              {firstPost.parent_category && (
                <div className="mb-3">
                  <Badge 
                    variant="outline" 
                    className="bg-primary/90 backdrop-blur-sm text-white border-primary/90 hover:bg-primary rounded-full"
                  >
                    {firstPost.parent_category.name}
                  </Badge>
                </div>
              )}
              
              {/* Title */}
              <h2 className="text-2xl lg:text-3xl font-bold mb-3 line-clamp-2 group-hover:text-primary-foreground transition-colors">
                {firstPost.title}
              </h2>
              
              {/* Excerpt */}
              {/* {firstPost.excerpt && (
                <p className="text-gray-200 mb-4 line-clamp-2 text-xs lg:text-sm">
                  {firstPost.excerpt}
                </p>
              )} */}
              
              {/* Author and Date */}
              <div className="flex items-center gap-4 text-sm text-gray-300">
                <div className="flex items-center gap-2">
                  {firstPost.author.image && (
                    <img
                      src={firstPost.author.image}
                      alt={firstPost.author.name || "Author"}
                      className="w-6 h-6 rounded-full object-cover"
                    />
                  )}
                  <span>{firstPost.author.name || "Anonymous"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CalendarDays className="h-4 w-4" />
                  <span>{formatDate(firstPost.published_at)}</span>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </div>

      {/* Other posts - 40% width (2 columns) */}
      <div className="lg:col-span-2 space-y-4">
        {otherPosts.map((post) => (
          <div key={post.id} className="group">
            <Link href={`/blog/${post.slug}`}>
              <div className="flex gap-4 h-[215px] rounded-xl overflow-hidden bg-gray-100">
                {/* Image */}
                <div className="relative w-1/2 flex-shrink-0">
                  {post.parent_category && (
                    <div className="absolute top-3 left-3 z-10">
                      <Badge 
                        variant="outline" 
                        className="text-xs bg-primary/90 backdrop-blur-sm text-white border-primary/90 hover:bg-primary rounded-full"
                      >
                        {post.parent_category.name}
                      </Badge>
                    </div>
                  )}
                  {post.featured_image_url ? (
                    <Image
                      src={post.featured_image_url}
                      alt={post.title}
                      fill
                      className="object-cover transition-transform duration-300"
                      sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 20vw"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      <span className="text-gray-400 text-sm">No Image</span>
                    </div>
                  )}
                </div>
                
                {/* Content */}
                <div className="flex-1 py-2 flex flex-col justify-between">
                  <div>
                    {/* Title */}
                    <h3 className="font-semibold text-lg line-clamp-4 group-hover:text-primary transition-colors mb-2">
                      {post.title}
                    </h3>
                    
                    {/* Excerpt */}
                    {/* {post.excerpt && (
                      <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                        {post.excerpt}
                      </p>
                    )} */}
                  </div>
                  
                  {/* Author and Date */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      {post.author.image && (
                        <img
                          src={post.author.image}
                          alt={post.author.name || "Author"}
                          className="w-4 h-4 rounded-full object-cover"
                        />
                      )}
                      <span className="text-xs text-gray-600">{post.author.name || "Anonymous"}</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <CalendarDays className="h-3 w-3" />
                      <span>{formatDate(post.published_at)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BlogListingGallery; 