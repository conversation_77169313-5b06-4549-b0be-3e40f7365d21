import {
  useApiMutation,
  useApiQuery,
  apiPost,
  apiGet,
  apiPut,
  apiDelete,
  ApiError,
} from "@/feature/core/api/api-utils";
import {
  CreateTagRequest,
  UpdateTagRequest,
  TagResponse,
  PaginatedResponse,
  ListQueryParams,
} from "@/types/tool-api";
import { toast } from "sonner";

// Tag API functions
const tagApi = {
  getTags: async (
    params?: ListQueryParams
  ): Promise<PaginatedResponse<TagResponse>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set("page", params.page.toString());
    if (params?.limit) searchParams.set("limit", params.limit.toString());
    if (params?.search) searchParams.set("search", params.search);
    if (params?.is_active !== undefined)
      searchParams.set("is_active", params.is_active.toString());
    if (params?.language) searchParams.set("language", params.language);

    const queryString = searchParams.toString();
    const response = await apiGet<{ data: PaginatedResponse<TagResponse> }>(
      `/api/tool/tags${queryString ? `?${queryString}` : ""}`
    );
    return response.data;
  },

  getTag: async (id: number, language?: string): Promise<TagResponse> => {
    const searchParams = new URLSearchParams();
    if (language) searchParams.set("language", language);

    const queryString = searchParams.toString();
    const response = await apiGet<{ data: TagResponse }>(
      `/api/tool/tags/${id}${queryString ? `?${queryString}` : ""}`
    );
    return response.data;
  },

  createTag: async (data: CreateTagRequest): Promise<TagResponse> => {
    const response = await apiPost<{ data: TagResponse }>(
      "/api/tool/tags",
      data
    );
    return response.data;
  },

  updateTag: async (
    id: number,
    data: UpdateTagRequest
  ): Promise<TagResponse> => {
    const response = await apiPut<{ data: TagResponse }>(
      `/api/tool/tags/${id}`,
      data
    );
    return response.data;
  },

  deleteTag: async (id: number): Promise<{ id: number }> => {
    const response = await apiDelete<{ data: { id: number } }>(
      `/api/tool/tags/${id}`
    );
    return response.data;
  },
};

// Hook to get tags list
export function useGetTags(params?: ListQueryParams) {
  return useApiQuery(
    ["tool-tags", JSON.stringify(params)],
    () => tagApi.getTags(params),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

// Hook to get single tag
export function useGetTag(id: number, language?: string) {
  return useApiQuery(
    ["tool-tag", id.toString(), language || ""],
    () => tagApi.getTag(id, language),
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Hook to create tag
export function useCreateTag() {
  return useApiMutation(tagApi.createTag, {
    showErrorToast: false,
    onSuccess: (data) => {
      toast.success("Tag created successfully");
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to create tag");
    },
    invalidateQueries: ["tool-tags"],
  });
}

// Hook to update tag
export function useUpdateTag() {
  return useApiMutation(
    (variables: { id: number; data: UpdateTagRequest }) =>
      tagApi.updateTag(variables.id, variables.data),
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success("Tag updated successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to update tag");
      },
      invalidateQueries: ["tool-tags", "tool-tag"],
    }
  );
}

// Hook to delete tag
export function useDeleteTag() {
  return useApiMutation(tagApi.deleteTag, {
    showErrorToast: false,
    onSuccess: () => {
      toast.success("Tag deleted successfully");
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to delete tag");
    },
    invalidateQueries: ["tool-tags"],
  });
}

// Custom hook that aggregates all tag-related hooks
export function useTagActions() {
  const createTag = useCreateTag();
  const updateTag = useUpdateTag();
  const deleteTag = useDeleteTag();

  const isLoading =
    createTag.isPending || updateTag.isPending || deleteTag.isPending;

  return {
    createTag,
    updateTag,
    deleteTag,
    isLoading,
  };
}
