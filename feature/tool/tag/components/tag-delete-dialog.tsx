"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogDescription,
  CustomDialogFooter,
  CustomDialogHeader,
  CustomDialogTitle,
} from "@/components/custom/custom-dialog";
import {
  useTagFormState,
  useTagFormActions,
} from "@/feature/tool/tag/store/use-tool-tag-store";
import { useDeleteTag } from "@/feature/tool/tag/hooks/use-tool-tag-data";

export function TagDeleteDialog() {
  const { isDeleteDialogOpen, tagToDelete } = useTagFormState();
  const { closeAllDialogs } = useTagFormActions();
  const deleteTag = useDeleteTag();

  const isLoading = deleteTag.isPending;

  const handleDelete = async () => {
    if (!tagToDelete) return;

    try {
      await deleteTag.mutateAsync(tagToDelete.id);
      closeAllDialogs();
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      closeAllDialogs();
    }
  };

  return (
    <CustomDialog open={isDeleteDialogOpen} onOpenChange={handleClose}>
      <CustomDialogContent className="sm:max-w-[425px]">
        <CustomDialogHeader>
          <CustomDialogTitle>Delete Tag</CustomDialogTitle>
          <CustomDialogDescription>
            Are you sure you want to delete "{tagToDelete?.name}"? This action
            cannot be undone and will remove all translations.
          </CustomDialogDescription>
        </CustomDialogHeader>

        <div className="p-6">
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
            <p className="text-sm text-destructive font-medium">
              Warning: This will permanently delete the tag and all its
              translations.
            </p>
          </div>
        </div>

        <CustomDialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading}
          >
            {isLoading ? "Deleting..." : "Delete Tag"}
          </Button>
        </CustomDialogFooter>
      </CustomDialogContent>
    </CustomDialog>
  );
}
