"use client";

import React, { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogDescription,
  CustomDialogFooter,
  CustomDialogHeader,
  CustomDialogTitle,
} from "@/components/custom/custom-dialog";
import {
  useTagFormState,
  useTagFormActions,
} from "@/feature/tool/tag/store/use-tool-tag-store";
import {
  useCreateTag,
  useUpdateTag,
} from "@/feature/tool/tag/hooks/use-tool-tag-data";
import { generateSlug } from "@/lib/slug-utils";

export function TagFormDialog() {
  const { tagFormData, isCreateDialogOpen, isEditDialogOpen, tagToEdit } =
    useTagFormState();

  const { updateTagForm<PERSON>ield, closeAllDialogs } = useTagFormActions();

  const createTag = useCreateTag();
  const updateTag = useUpdateTag();

  const isOpen = isCreateDialogOpen || isEditDialogOpen;
  const isEdit = isEditDialogOpen && tagToEdit;
  const isLoading = createTag.isPending || updateTag.isPending;

  // Auto-generate slug when name changes (only for create)
  useEffect(() => {
    if (!isEdit && tagFormData.name) {
      const autoSlug = generateSlug(tagFormData.name);
      updateTagFormField("slug", autoSlug);
    }
  }, [tagFormData.name, isEdit, updateTagFormField]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tagFormData.name.trim()) {
      return;
    }

    try {
      if (isEdit && tagToEdit) {
        await updateTag.mutateAsync({
          id: tagToEdit.id,
          data: {
            name: tagFormData.name.trim(),
            description: tagFormData.description.trim() || undefined,
            slug: tagFormData.slug.trim(),
            color: tagFormData.color.trim() || undefined,
            is_active: tagFormData.is_active,
          },
        });
      } else {
        await createTag.mutateAsync({
          name: tagFormData.name.trim(),
          description: tagFormData.description.trim() || undefined,
          slug: tagFormData.slug.trim(),
          color: tagFormData.color.trim() || undefined,
          is_active: tagFormData.is_active,
        });
      }

      closeAllDialogs();
    } catch (error) {
      // Error handling is done in the hooks
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      closeAllDialogs();
    }
  };

  return (
    <CustomDialog open={isOpen} onOpenChange={handleClose}>
      <CustomDialogContent className="sm:max-w-[600px]">
        <CustomDialogHeader>
          <CustomDialogTitle>
            {isEdit ? "Edit Tag" : "Create New Tag"}
          </CustomDialogTitle>
          <CustomDialogDescription>
            {isEdit
              ? "Update the tag information. Changes will be automatically translated."
              : "Create a new tag. The information will be automatically translated to Spanish."}
          </CustomDialogDescription>
        </CustomDialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium">
                Name *
              </Label>
              <Input
                id="name"
                type="text"
                placeholder="Enter tag name"
                value={tagFormData.name}
                onChange={(e) => updateTagFormField("name", e.target.value)}
                disabled={isLoading}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug" className="text-sm font-medium">
                Slug *
              </Label>
              <Input
                id="slug"
                type="text"
                placeholder="tag-slug"
                value={tagFormData.slug}
                onChange={(e) => updateTagFormField("slug", e.target.value)}
                disabled={isLoading}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium">
              Description
            </Label>
            <Textarea
              id="description"
              placeholder="Enter tag description"
              value={tagFormData.description}
              onChange={(e) =>
                updateTagFormField("description", e.target.value)
              }
              disabled={isLoading}
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="color" className="text-sm font-medium">
              Color
            </Label>
            <Input
              id="color"
              type="text"
              placeholder="#3B82F6"
              value={tagFormData.color}
              onChange={(e) => updateTagFormField("color", e.target.value)}
              disabled={isLoading}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={tagFormData.is_active}
              onCheckedChange={(checked) =>
                updateTagFormField("is_active", checked)
              }
              disabled={isLoading}
            />
            <Label htmlFor="is_active" className="text-sm font-medium">
              Active
            </Label>
          </div>

          <CustomDialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !tagFormData.name.trim()}
            >
              {isLoading ? "Saving..." : isEdit ? "Update Tag" : "Create Tag"}
            </Button>
          </CustomDialogFooter>
        </form>
      </CustomDialogContent>
    </CustomDialog>
  );
}
