import { create } from "zustand";
import {
  TagResponse,
  CreateTagRequest,
  UpdateTagRequest,
} from "@/types/tool-api";

// Tag form data interfaces
export interface TagFormData {
  name: string;
  description: string;
  slug: string;
  color: string;
  is_active: boolean;
}

// Tag store state interface
export interface TagState {
  // Tags list state
  tags: TagResponse[];
  selectedTag: TagResponse | null;
  totalTags: number;
  currentPage: number;
  totalPages: number;
  isLoading: boolean;

  // Search and filter state
  searchQuery: string;
  isActiveFilter: boolean | null;
  languageFilter: string;

  // Form state
  tagFormData: TagFormData;
  isCreateDialogOpen: boolean;
  isEditDialogOpen: boolean;
  isDeleteDialogOpen: boolean;
  tagToEdit: TagResponse | null;
  tagToDelete: TagResponse | null;

  // Actions for tags list
  setTags: (tags: TagResponse[]) => void;
  setSelectedTag: (tag: TagResponse | null) => void;
  setTotalTags: (total: number) => void;
  setCurrentPage: (page: number) => void;
  setTotalPages: (pages: number) => void;
  setIsLoading: (loading: boolean) => void;

  // Actions for search and filter
  setSearchQuery: (query: string) => void;
  setIsActiveFilter: (filter: boolean | null) => void;
  setLanguageFilter: (language: string) => void;
  resetFilters: () => void;

  // Actions for form state
  setTagFormData: (data: Partial<TagFormData>) => void;
  updateTagFormField: (
    field: keyof TagFormData,
    value: string | boolean
  ) => void;
  resetTagForm: () => void;

  // Actions for dialogs
  setIsCreateDialogOpen: (open: boolean) => void;
  setIsEditDialogOpen: (open: boolean) => void;
  setIsDeleteDialogOpen: (open: boolean) => void;
  setTagToEdit: (tag: TagResponse | null) => void;
  setTagToDelete: (tag: TagResponse | null) => void;

  // Helper actions
  openCreateDialog: () => void;
  openEditDialog: (tag: TagResponse) => void;
  openDeleteDialog: (tag: TagResponse) => void;
  closeAllDialogs: () => void;

  // Global actions
  resetAllState: () => void;
}

// Initial state values
const initialTagFormData: TagFormData = {
  name: "",
  description: "",
  slug: "",
  color: "",
  is_active: true,
};

// Create the Zustand store
export const useTagStore = create<TagState>((set, get) => ({
  // Initial state
  tags: [],
  selectedTag: null,
  totalTags: 0,
  currentPage: 1,
  totalPages: 1,
  isLoading: false,

  searchQuery: "",
  isActiveFilter: null,
  languageFilter: "en",

  tagFormData: initialTagFormData,
  isCreateDialogOpen: false,
  isEditDialogOpen: false,
  isDeleteDialogOpen: false,
  tagToEdit: null,
  tagToDelete: null,

  // Actions for tags list
  setTags: (tags) => set({ tags }),
  setSelectedTag: (tag) => set({ selectedTag: tag }),
  setTotalTags: (total) => set({ totalTags: total }),
  setCurrentPage: (page) => set({ currentPage: page }),
  setTotalPages: (pages) => set({ totalPages: pages }),
  setIsLoading: (loading) => set({ isLoading: loading }),

  // Actions for search and filter
  setSearchQuery: (query) => set({ searchQuery: query }),
  setIsActiveFilter: (filter) => set({ isActiveFilter: filter }),
  setLanguageFilter: (language) => set({ languageFilter: language }),
  resetFilters: () =>
    set({
      searchQuery: "",
      isActiveFilter: null,
      languageFilter: "en",
      currentPage: 1,
    }),

  // Actions for form state
  setTagFormData: (data) =>
    set((state) => ({
      tagFormData: { ...state.tagFormData, ...data },
    })),

  updateTagFormField: (field, value) =>
    set((state) => ({
      tagFormData: { ...state.tagFormData, [field]: value },
    })),

  resetTagForm: () => set({ tagFormData: initialTagFormData }),

  // Actions for dialogs
  setIsCreateDialogOpen: (open) => set({ isCreateDialogOpen: open }),
  setIsEditDialogOpen: (open) => set({ isEditDialogOpen: open }),
  setIsDeleteDialogOpen: (open) => set({ isDeleteDialogOpen: open }),
  setTagToEdit: (tag) => set({ tagToEdit: tag }),
  setTagToDelete: (tag) => set({ tagToDelete: tag }),

  // Helper actions
  openCreateDialog: () => {
    set({
      isCreateDialogOpen: true,
      tagFormData: initialTagFormData,
    });
  },

  openEditDialog: (tag) => {
    set({
      isEditDialogOpen: true,
      tagToEdit: tag,
      tagFormData: {
        name: tag.name,
        description: tag.translations?.[0]?.description || "",
        slug: tag.slug,
        color: tag.color || "",
        is_active: tag.is_active,
      },
    });
  },

  openDeleteDialog: (tag) => {
    set({
      isDeleteDialogOpen: true,
      tagToDelete: tag,
    });
  },

  closeAllDialogs: () => {
    set({
      isCreateDialogOpen: false,
      isEditDialogOpen: false,
      isDeleteDialogOpen: false,
      tagToEdit: null,
      tagToDelete: null,
    });
  },

  // Global actions
  resetAllState: () =>
    set({
      tags: [],
      selectedTag: null,
      totalTags: 0,
      currentPage: 1,
      totalPages: 1,
      isLoading: false,
      searchQuery: "",
      isActiveFilter: null,
      languageFilter: "en",
      tagFormData: initialTagFormData,
      isCreateDialogOpen: false,
      isEditDialogOpen: false,
      isDeleteDialogOpen: false,
      tagToEdit: null,
      tagToDelete: null,
    }),
}));

// Selector hooks for specific parts of the state
export const useTagListState = () => {
  const {
    tags,
    selectedTag,
    totalTags,
    currentPage,
    totalPages,
    isLoading,
    searchQuery,
    isActiveFilter,
    languageFilter,
  } = useTagStore();

  return {
    tags,
    selectedTag,
    totalTags,
    currentPage,
    totalPages,
    isLoading,
    searchQuery,
    isActiveFilter,
    languageFilter,
  };
};

export const useTagListActions = () => {
  const {
    setTags,
    setSelectedTag,
    setTotalTags,
    setCurrentPage,
    setTotalPages,
    setIsLoading,
    setSearchQuery,
    setIsActiveFilter,
    setLanguageFilter,
    resetFilters,
  } = useTagStore();

  return {
    setTags,
    setSelectedTag,
    setTotalTags,
    setCurrentPage,
    setTotalPages,
    setIsLoading,
    setSearchQuery,
    setIsActiveFilter,
    setLanguageFilter,
    resetFilters,
  };
};

export const useTagFormState = () => {
  const {
    tagFormData,
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    tagToEdit,
    tagToDelete,
  } = useTagStore();

  return {
    tagFormData,
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    tagToEdit,
    tagToDelete,
  };
};

export const useTagFormActions = () => {
  const {
    setTagFormData,
    updateTagFormField,
    resetTagForm,
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    setIsDeleteDialogOpen,
    setTagToEdit,
    setTagToDelete,
    openCreateDialog,
    openEditDialog,
    openDeleteDialog,
    closeAllDialogs,
  } = useTagStore();

  return {
    setTagFormData,
    updateTagFormField,
    resetTagForm,
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    setIsDeleteDialogOpen,
    setTagToEdit,
    setTagToDelete,
    openCreateDialog,
    openEditDialog,
    openDeleteDialog,
    closeAllDialogs,
  };
};
