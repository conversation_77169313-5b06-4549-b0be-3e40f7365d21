"use client";

import React, { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, X, Image, ExternalLink } from "lucide-react";
import { useUploadMedia } from "@/feature/media/hooks/use-media-data";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface LogoUploadProps {
  value?: string;
  onChange: (url: string) => void;
  dict?: any;
}

export function LogoUpload({ value, onChange, dict }: LogoUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [manualUrl, setManualUrl] = useState("");
  const [showManualInput, setShowManualInput] = useState(false);

  const uploadMutation = useUploadMedia();

  const handleFileSelect = useCallback(
    async (files: FileList | null) => {
      if (!files || files.length === 0) return;

      const file = files[0];

      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast.error("Please select an image file");
        return;
      }

      // Validate file size (5MB limit for logos)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Logo file size must be less than 5MB");
        return;
      }

      try {
        const result = await uploadMutation.mutateAsync({
          file,
          folder_path: "tools/logos",
        });

        if (result.success && result.data.url) {
          onChange(result.data.url);
          toast.success("Logo uploaded successfully");
        }
      } catch (error) {
        console.error("Upload failed:", error);
      }
    },
    [uploadMutation, onChange]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
      handleFileSelect(e.dataTransfer.files);
    },
    [handleFileSelect]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleManualUrlSubmit = useCallback(() => {
    if (manualUrl.trim()) {
      onChange(manualUrl.trim());
      setManualUrl("");
      setShowManualInput(false);
      toast.success("Logo URL added successfully");
    }
  }, [manualUrl, onChange]);

  const removeLogo = useCallback(() => {
    onChange("");
    toast.success("Logo removed");
  }, [onChange]);

  return (
    <div className="space-y-4">
      <Label className="text-sm font-medium">
        {dict?.tool?.logo || "Logo"}
      </Label>

      {value ? (
        <Card className="shadow-none mt-2">
          <CardContent className=" px-4 ">
            <div className="flex items-start space-x-4">
              <div className="relative">
                <img
                  src={value}
                  alt="Tool Logo"
                  className="w-20 h-20 object-cover rounded-lg border"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                  onClick={removeLogo}
                >
                  <X className="h-3 w-3 text-white" />
                </Button>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  Current Logo
                </p>
                <a
                  href={value}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1 mt-1"
                >
                  View original <ExternalLink className="h-3 w-3" />
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3 pt-3">
          {/* File Upload Zone */}
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer",
              isDragOver
                ? "border-primary bg-primary/5"
                : "border-muted-foreground/25 hover:border-primary/50"
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => {
              const input = document.createElement("input");
              input.type = "file";
              input.accept = "image/*";
              input.onchange = (e) => {
                const target = e.target as HTMLInputElement;
                handleFileSelect(target.files);
              };
              input.click();
            }}
          >
            <div className="flex flex-col items-center gap-2">
              <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                <Upload className="w-4 h-4 text-muted-foreground" />
              </div>
              <div>
                <p className="text-sm font-medium">
                  {dict?.tool?.uploadLogo || "Upload Logo"}
                </p>
                <p className="text-xs text-muted-foreground">
                  {dict?.tool?.uploadLogoDescription ||
                    "Click to upload or drag and drop (Max 5MB)"}
                </p>
              </div>
            </div>
          </div>

          {/* Manual URL Input */}
          {showManualInput ? (
            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  type="url"
                  placeholder="https://example.com/logo.png"
                  value={manualUrl}
                  onChange={(e) => setManualUrl(e.target.value)}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleManualUrlSubmit}
                  disabled={!manualUrl.trim()}
                >
                  Add
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowManualInput(false);
                    setManualUrl("");
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            // <Button
            //   type="button"
            //   variant="outline"
            //   onClick={() => setShowManualInput(true)}
            //   className="w-full"
            // >
            //   {dict?.tool?.orEnterUrl || "Or enter URL manually"}
            // </Button>
            <div></div>
          )}
        </div>
      )}

      {uploadMutation.isPending && (
        <div className="text-sm text-muted-foreground">
          {dict?.tool?.uploading || "Uploading..."}
        </div>
      )}
    </div>
  );
}
