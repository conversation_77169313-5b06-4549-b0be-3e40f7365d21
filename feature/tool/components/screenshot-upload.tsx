"use client";

import React, { useState, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, X, Image, ExternalLink, Plus } from "lucide-react";
import { useUploadMultipleFiles } from "@/feature/media/hooks/use-media-data";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface ScreenshotUploadProps {
  value: string[];
  onChange: (urls: string[]) => void;
  maxFiles?: number;
  dict?: any;
}

export function ScreenshotUpload({
  value = [],
  onChange,
  maxFiles = 10,
  dict,
}: ScreenshotUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [manualUrl, setManualUrl] = useState("");
  const [showManualInput, setShowManualInput] = useState(false);

  const uploadMutation = useUploadMultipleFiles();

  const handleFileSelect = useCallback(
    async (files: FileList | null) => {
      if (!files || files.length === 0) return;

      const fileArray = Array.from(files);

      // Check if adding these files would exceed the limit
      if (value.length + fileArray.length > maxFiles) {
        toast.error(`Maximum ${maxFiles} screenshots allowed`);
        return;
      }

      // Validate file types
      const invalidFiles = fileArray.filter(
        (file) => !file.type.startsWith("image/")
      );
      if (invalidFiles.length > 0) {
        toast.error("Please select only image files");
        return;
      }

      // Validate file sizes (10MB limit for screenshots)
      const oversizedFiles = fileArray.filter(
        (file) => file.size > 10 * 1024 * 1024
      );
      if (oversizedFiles.length > 0) {
        toast.error("Screenshot files must be less than 10MB each");
        return;
      }

      try {
        const results = await uploadMutation.mutateAsync({
          files: fileArray,
          folder_path: "tools/screenshots",
        });

        const newUrls = results
          .filter((result) => result.success && result.data.url)
          .map((result) => result.data.url);

        if (newUrls.length > 0) {
          onChange([...value, ...newUrls]);
          toast.success(
            `${newUrls.length} screenshot(s) uploaded successfully`
          );
        }
      } catch (error) {
        console.error("Upload failed:", error);
      }
    },
    [uploadMutation, onChange, value, maxFiles]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
      handleFileSelect(e.dataTransfer.files);
    },
    [handleFileSelect]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleManualUrlSubmit = useCallback(() => {
    if (manualUrl.trim()) {
      if (value.length >= maxFiles) {
        toast.error(`Maximum ${maxFiles} screenshots allowed`);
        return;
      }

      if (value.includes(manualUrl.trim())) {
        toast.error("This URL is already added");
        return;
      }

      onChange([...value, manualUrl.trim()]);
      setManualUrl("");
      setShowManualInput(false);
      toast.success("Screenshot URL added successfully");
    }
  }, [manualUrl, onChange, value, maxFiles]);

  const removeScreenshot = useCallback(
    (index: number) => {
      const newUrls = value.filter((_, i) => i !== index);
      onChange(newUrls);
      toast.success("Screenshot removed");
    },
    [onChange, value]
  );

  const canAddMore = value.length < maxFiles;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">
          {dict?.tool?.screenshots || "Screenshots"}
        </Label>
        <span className="text-xs text-muted-foreground">
          {value.length} / {maxFiles}
        </span>
      </div>

      {/* Existing Screenshots */}
      {value.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {value.map((url, index) => (
            <Card key={index} className="shadow-none p-0 py-4 rounded-lg">
              <CardContent className="px-4">
                <div className="relative">
                  <img
                    src={url}
                    alt={`Screenshot ${index + 1}`}
                    className="w-full h-24 object-cover rounded-sm border"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                    onClick={() => removeScreenshot(index)}
                  >
                    <X className="h-3 w-3 text-white" />
                  </Button>
                </div>
                <div className="flex items-center justify-center">
                  <a
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-primary/90 hover:text-primary flex items-center gap-1 mt-2 "
                  >
                    View <ExternalLink className="h-3 w-3" />
                  </a>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Upload Zone */}
      {canAddMore && (
        <div className="space-y-3">
          {/* File Upload Zone */}
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer",
              isDragOver
                ? "border-primary bg-primary/5"
                : "border-muted-foreground/25 hover:border-primary/50"
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => {
              const input = document.createElement("input");
              input.type = "file";
              input.accept = "image/*";
              input.multiple = true;
              input.onchange = (e) => {
                const target = e.target as HTMLInputElement;
                handleFileSelect(target.files);
              };
              input.click();
            }}
          >
            <div className="flex flex-col items-center space-y-2">
              <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                <Upload className="w-5 h-5 text-muted-foreground" />
              </div>
              <div>
                <p className="text-sm font-medium">
                  {dict?.tool?.uploadScreenshots || "Upload Screenshots"}
                </p>
                <p className="text-xs text-muted-foreground">
                  {dict?.tool?.uploadScreenshotsDescription ||
                    "Click to upload or drag and drop multiple images (Max 10MB each)"}
                </p>
              </div>
            </div>
          </div>

          {/* Manual URL Input */}
          {showManualInput ? (
            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  type="url"
                  placeholder="https://example.com/screenshot.png"
                  value={manualUrl}
                  onChange={(e) => setManualUrl(e.target.value)}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleManualUrlSubmit}
                  disabled={!manualUrl.trim()}
                >
                  Add
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowManualInput(false);
                    setManualUrl("");
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            // <Button
            //   type="button"
            //   variant="outline"
            //   onClick={() => setShowManualInput(true)}
            //   className="w-full"
            // >
            //   {dict?.tool?.orEnterUrl || "Or enter URL manually"}
            // </Button>
            <div></div>
          )}
        </div>
      )}

      {uploadMutation.isPending && (
        <div className="text-sm text-muted-foreground">
          {dict?.tool?.uploading || "Uploading screenshots..."}
        </div>
      )}

      {value.length >= maxFiles && (
        <p className="text-sm text-amber-600">
          {dict?.tool?.maxScreenshotsReached ||
            `Maximum ${maxFiles} screenshots reached`}
        </p>
      )}
    </div>
  );
}
