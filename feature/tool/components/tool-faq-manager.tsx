"use client";

import React, { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogDescription,
} from "@/components/custom/custom-dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Plus,
  Edit2,
  Trash2,
  GripVertical,
  HelpCircle,
  ChevronDown,
  ChevronUp,
  AlertCircle,
  MessageCircle,
  Lightbulb,
  CheckCircle2,
} from "lucide-react";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";

interface FAQ {
  id?: string;
  question: string;
  answer: string;
  order?: number;
  isNew?: boolean;
}

interface ToolFAQManagerProps {
  value: FAQ[];
  onChange: (faqs: FAQ[]) => void;
  maxFAQs?: number;
}

export function ToolFAQManager({
  value = [],
  onChange,
  maxFAQs = 10,
}: ToolFAQManagerProps) {
  const [faqs, setFAQs] = useState<FAQ[]>(value);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [question, setQuestion] = useState("");
  const [answer, setAnswer] = useState("");
  const [expandedFAQs, setExpandedFAQs] = useState<Set<number>>(new Set());
  const isSyncingRef = useRef(false);

  // Sync with parent value when it changes
  useEffect(() => {
    isSyncingRef.current = true;
    setFAQs(value);
    // Reset sync flag after state update completes
    const timeoutId = setTimeout(() => {
      isSyncingRef.current = false;
    }, 0);
    return () => clearTimeout(timeoutId);
  }, [value]);

  // Helper function to update FAQs and notify parent
  const updateFAQs = (newFAQs: FAQ[]) => {
    if (!isSyncingRef.current) {
      setFAQs(newFAQs);
      onChange(newFAQs);
    }
  };

  const handleOpenDialog = (index: number | null = null) => {
    if (index !== null) {
      const faq = faqs[index];
      setQuestion(faq.question);
      setAnswer(faq.answer);
      setEditingIndex(index);
    } else {
      setQuestion("");
      setAnswer("");
      setEditingIndex(null);
    }
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingIndex(null);
    setQuestion("");
    setAnswer("");
  };

  const handleSaveFAQ = () => {
    if (!question.trim() || !answer.trim()) {
      return;
    }

    const newFAQ: FAQ = {
      question: question.trim(),
      answer: answer.trim(),
      order: editingIndex !== null ? faqs[editingIndex].order : faqs.length + 1,
      isNew: editingIndex === null,
    };

    if (editingIndex !== null) {
      // Update existing FAQ
      const updatedFAQs = [...faqs];
      updatedFAQs[editingIndex] = { ...faqs[editingIndex], ...newFAQ };
      updateFAQs(updatedFAQs);
    } else {
      // Add new FAQ
      if (faqs.length < maxFAQs) {
        updateFAQs([...faqs, newFAQ]);
      }
    }

    handleCloseDialog();
  };

  const handleDeleteFAQ = (index: number) => {
    const updatedFAQs = faqs.filter((_, i) => i !== index);
    // Reorder remaining FAQs
    const reorderedFAQs = updatedFAQs.map((faq, i) => ({
      ...faq,
      order: i + 1,
    }));
    updateFAQs(reorderedFAQs);
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(faqs);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order
    const reorderedFAQs = items.map((faq, index) => ({
      ...faq,
      order: index + 1,
    }));

    updateFAQs(reorderedFAQs);
  };

  const toggleFAQExpansion = (index: number) => {
    const newExpanded = new Set(expandedFAQs);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedFAQs(newExpanded);
  };

  const canAddMore = faqs.length < maxFAQs;

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400 pr-20">
                FAQ Progress
              </span>
              <div className="flex items-center space-x-2">
                <Badge
                  variant={faqs.length >= 5 ? "default" : "secondary"}
                  className="text-xs"
                >
                  {faqs.length}/{maxFAQs}
                </Badge>
                {faqs.length >= 5 && (
                  <CheckCircle2 className="h-4 w-4 text-primary" />
                )}
              </div>
            </div>
            <Progress value={(faqs.length / maxFAQs) * 100} className="h-2" />
            {/* <p className="text-xs text-gray-500 dark:text-gray-400">
            {faqs.length < 5
              ? `Add ${5 - faqs.length} more FAQs for better user experience`
              : "Great! You have a comprehensive FAQ section"}
          </p> */}
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => handleOpenDialog()}
            disabled={!canAddMore}
            className="flex items-center border-primary text-primary hover:bg-primary hover:text-white transition-colors"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add FAQ
          </Button>
        </div>
      </div>

      {/* Alerts */}
      {!canAddMore && (
        <Alert className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950">
          <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          <AlertDescription className="text-amber-800 dark:text-amber-200">
            You've reached the maximum of {maxFAQs} FAQs per tool. Consider
            editing existing ones for better clarity.
          </AlertDescription>
        </Alert>
      )}

      {/* FAQ List */}
      {faqs.length === 0 ? (
        <Card className="border-dashed border-2 border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50">
          <CardContent className="flex flex-col items-center justify-center py-2 text-center">
            <div className="flex items-center justify-center w-14 h-14 bg-gray-100 dark:bg-gray-700 rounded-full mb-4">
              <MessageCircle className="h-7 w-7 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              No FAQs yet
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-lg text-sm">
              Create your first FAQ to help users understand your tool better.
              Great FAQs can reduce support requests by up to 80%.
            </p>
            <Button
              type="button"
              onClick={() => handleOpenDialog()}
              disabled={!canAddMore}
              className="bg-primary hover:bg-primary/90"
            >
              <Plus className="mr-2 h-4 w-4" />
              Create First FAQ
            </Button>
          </CardContent>
        </Card>
      ) : (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="faqs">
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="space-y-3"
              >
                {faqs.map((faq, index) => (
                  <Draggable
                    key={`faq-${index}`}
                    draggableId={`faq-${index}`}
                    index={index}
                  >
                    {(provided, snapshot) => (
                      <Card
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className={`border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md ${
                          snapshot.isDragging
                            ? "shadow-xl rotate-2 scale-105 border-primary"
                            : "hover:border-gray-300 dark:hover:border-gray-600"
                        }`}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-3 flex-1">
                              <div
                                {...provided.dragHandleProps}
                                className="mt-1 cursor-grab text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                                title="Drag to reorder"
                              >
                                <GripVertical className="h-4 w-4" />
                              </div>
                              <div className="flex-1 space-y-2">
                                <div className="flex items-center space-x-2">
                                  <Badge
                                    variant="outline"
                                    className="text-xs bg-primary/10 border-primary/20 text-primary/90 dark:bg-primary dark:border-primary/90 dark:text-primary/30"
                                  >
                                    FAQ {index + 1}
                                  </Badge>
                                  {faq.isNew && (
                                    <Badge
                                      variant="secondary"
                                      className="text-xs bg-primary/10 text-primary/90 dark:bg-primary dark:text-primary/40"
                                    >
                                      New
                                    </Badge>
                                  )}
                                </div>
                                <div className="font-medium text-gray-900 dark:text-gray-100">
                                  {faq.question}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleFAQExpansion(index)}
                                className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
                                title={
                                  expandedFAQs.has(index)
                                    ? "Collapse"
                                    : "Expand"
                                }
                              >
                                {expandedFAQs.has(index) ? (
                                  <ChevronUp className="h-4 w-4" />
                                ) : (
                                  <ChevronDown className="h-4 w-4" />
                                )}
                              </Button>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleOpenDialog(index)}
                                className="h-8 w-8 p-0 hover:bg-primary/10 dark:hover:bg-primary text-primary dark:text-primary/40"
                                title="Edit FAQ"
                              >
                                <Edit2 className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteFAQ(index)}
                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-100 dark:hover:bg-red-900 dark:text-red-400"
                                title="Delete FAQ"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        {expandedFAQs.has(index) && (
                          <CardContent className="pt-0 border-t border-gray-100 dark:border-gray-700">
                            <div className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                              {faq.answer}
                            </div>
                          </CardContent>
                        )}
                      </Card>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}

      {/* FAQ Form Dialog */}
      <CustomDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <CustomDialogContent className="max-w-2xl">
          <CustomDialogHeader>
            <CustomDialogTitle>
              {editingIndex !== null ? "Edit FAQ" : "Add New FAQ"}
            </CustomDialogTitle>
            <CustomDialogDescription>
              {editingIndex !== null
                ? "Update the FAQ question and answer."
                : "Add a new frequently asked question for your tool."}
            </CustomDialogDescription>
          </CustomDialogHeader>
          <div className="space-y-6 p-6">
            <div className="space-y-2">
              <Label
                htmlFor="question"
                className="text-sm font-medium text-gray-900 dark:text-gray-100"
              >
                Question *
              </Label>
              <Input
                id="question"
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                placeholder="What is the main purpose of this tool?"
                className="mt-1 focus:ring-2 focus:ring-primary focus:border-primary"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Keep it clear and specific. Think about what users commonly ask.
              </p>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="answer"
                className="text-sm font-medium text-gray-900 dark:text-gray-100"
              >
                Answer *
              </Label>
              <Textarea
                id="answer"
                value={answer}
                onChange={(e) => setAnswer(e.target.value)}
                placeholder="Provide a detailed answer to help users understand..."
                rows={6}
                className="mt-1 focus:ring-2 focus:ring-primary focus:border-primary"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Be comprehensive but concise. Include key benefits and next
                steps if relevant.
              </p>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100 dark:border-gray-700">
              <Button
                type="button"
                variant="outline"
                onClick={handleCloseDialog}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleSaveFAQ}
                disabled={!question.trim() || !answer.trim()}
                className="bg-primary hover:bg-primary/90 px-6"
              >
                {editingIndex !== null ? "Update FAQ" : "Add FAQ"}
              </Button>
            </div>
          </div>
        </CustomDialogContent>
      </CustomDialog>
    </div>
  );
}
