"use client";

import React from "react";
import { Search, Filter, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useToolFiltersState,
  useToolFiltersActions,
} from "../store/use-tool-store";
import { ToolType, PricingModel, ToolStatus } from "@/types/tool-api";

export function ToolFilters() {
  const {
    searchQuery,
    statusFilter,
    toolTypeFilter,
    pricingModelFilter,
    isPublishedFilter,
    languageFilter,
  } = useToolFiltersState();

  const {
    setSearchQuery,
    setStatusFilter,
    setToolTypeFilter,
    setPricingModelFilter,
    setIsPublishedFilter,
    setLanguageFilter,
    resetFilters,
  } = useToolFiltersActions();

  const hasActiveFilters =
    searchQuery ||
    statusFilter !== "all" ||
    toolTypeFilter !== "all" ||
    pricingModelFilter !== "all" ||
    isPublishedFilter !== "all";

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search tools..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Language Filter */}
        <Select value={languageFilter} onValueChange={setLanguageFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="en">English</SelectItem>
            <SelectItem value="es">Español</SelectItem>
          </SelectContent>
        </Select>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <Button
            variant="outline"
            onClick={resetFilters}
            className="border-primary text-primary hover:bg-primary hover:text-white"
          >
            <X className="mr-2 h-4 w-4" />
            Clear
          </Button>
        )}
      </div>

      <div className="flex flex-wrap gap-4">
        {/* Status Filter */}
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            {Object.values(ToolStatus).map((status) => (
              <SelectItem key={status} value={status}>
                {status}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Tool Type Filter */}
        <Select value={toolTypeFilter} onValueChange={setToolTypeFilter}>
          <SelectTrigger className="w-[160px]">
            <SelectValue placeholder="Tool Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            {Object.values(ToolType).map((type) => (
              <SelectItem key={type} value={type}>
                {type.replace("_", " ")}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Pricing Model Filter */}
        <Select
          value={pricingModelFilter}
          onValueChange={setPricingModelFilter}
        >
          <SelectTrigger className="w-[160px]">
            <SelectValue placeholder="Pricing" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Pricing</SelectItem>
            {Object.values(PricingModel).map((pricing) => (
              <SelectItem key={pricing} value={pricing}>
                {pricing.replace("_", " ")}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Published Filter */}
        <Select value={isPublishedFilter} onValueChange={setIsPublishedFilter}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Published" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="true">Published</SelectItem>
            <SelectItem value="false">Draft</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
