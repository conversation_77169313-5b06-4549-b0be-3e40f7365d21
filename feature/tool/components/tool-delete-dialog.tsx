"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  useToolDeleteState,
  useToolDeleteActions,
} from "../store/use-tool-store";
import { useDeleteTool } from "../hooks/use-tool-data";

export function ToolDeleteDialog() {
  const { isDeleteDialogOpen, deletingTool } = useToolDeleteState();
  const { closeDeleteDialog } = useToolDeleteActions();
  const deleteTool = useDeleteTool();

  const handleDelete = async () => {
    if (!deletingTool) return;

    try {
      await deleteTool.mutateAsync(deletingTool.id);
      closeDeleteDialog();
    } catch (error) {
      // Error is handled by the hook's onError callback
    }
  };

  const toolName =
    deletingTool?.translations[0]?.name || deletingTool?.name || "";

  return (
    <Dialog open={isDeleteDialogOpen} onOpenChange={closeDeleteDialog}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Are you absolutely sure?</DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the tool{" "}
            <span className="font-semibold">"{toolName}"</span> and remove all
            associated data from our servers.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={closeDeleteDialog}
            disabled={deleteTool.isPending}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDelete}
            disabled={deleteTool.isPending}
            className="bg-red-600 hover:bg-red-700"
          >
            {deleteTool.isPending ? "Deleting..." : "Delete"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
