"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useToolFiltersState } from "../store/use-tool-store";
import { useGetTools } from "../hooks/use-tool-data";
import { ToolTable } from "./tool-table";
import { ToolDeleteDialog } from "./tool-delete-dialog";
import { ToolTranslationDialog } from "./tool-translation-dialog";
import { ToolFilters } from "./tool-filters";
import { ToolType, PricingModel, ToolStatus } from "@/types/tool-api";

export function ToolManagement() {
  const router = useRouter();
  const {
    searchQuery,
    statusFilter,
    toolTypeFilter,
    pricingModelFilter,
    isPublishedFilter,
    languageFilter,
    currentPage,
    pageSize,
  } = useToolFiltersState();

  // Build query parameters for API call
  const queryParams = {
    page: currentPage,
    limit: pageSize,
    language: languageFilter,
    ...(searchQuery && { search: searchQuery }),
    ...(statusFilter !== "all" && { status: statusFilter as ToolStatus }),
    ...(toolTypeFilter !== "all" && { tool_type: toolTypeFilter as ToolType }),
    ...(pricingModelFilter !== "all" && {
      pricing_model: pricingModelFilter as PricingModel,
    }),
    ...(isPublishedFilter !== "all" && {
      is_published: isPublishedFilter === "true",
    }),
  };

  const { data: toolsData, isLoading, error } = useGetTools(queryParams);

  const handleAddTool = () => {
    router.push("/admin/tools/add");
  };

  return (
    <div className="space-y-6">
      <Card className="shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-2xl font-bold">Tool Management</CardTitle>
          <Button
            onClick={handleAddTool}
            className="bg-primary hover:bg-primary/90"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Tool
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          <ToolFilters />
          <ToolTable data={toolsData} isLoading={isLoading} error={error} />
        </CardContent>
      </Card>

      <ToolDeleteDialog />
      <ToolTranslationDialog />
    </div>
  );
}
