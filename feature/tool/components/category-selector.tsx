"use client";

import React, { useState, useMemo } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Search,
  ChevronDown,
  ChevronRight,
  Palette,
  PenTool,
  Mic,
  Video,
  Share2,
  X,
} from "lucide-react";
import { SuperCategory, ToolConfigCategory } from "@/types/tool-api";

interface CategorySelectorProps {
  categories: Record<SuperCategory, ToolConfigCategory[]>;
  selectedIds: number[];
  onChange: (selectedIds: number[]) => void;
  maxHeight?: string;
}

const superCategoryIcons: Record<SuperCategory, React.ReactNode> = {
  [SuperCategory.WRITING]: <PenTool className="h-4 w-4" />,
  [SuperCategory.IMAGE_GENERATION]: <Palette className="h-4 w-4" />,
  [SuperCategory.AUDIO]: <Mic className="h-4 w-4" />,
  [SuperCategory.VIDEO_GENERATION]: <Video className="h-4 w-4" />,
  [SuperCategory.SOCIAL_MEDIA]: <Share2 className="h-4 w-4" />,
};

const superCategoryColors: Record<SuperCategory, string> = {
  [SuperCategory.WRITING]:
    "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",
  [SuperCategory.IMAGE_GENERATION]:
    "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",
  [SuperCategory.AUDIO]:
    "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",
  [SuperCategory.VIDEO_GENERATION]:
    "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",
  [SuperCategory.SOCIAL_MEDIA]:
    "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",
};

export function CategorySelector({
  categories,
  selectedIds,
  onChange,
  maxHeight = "400px",
}: CategorySelectorProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedSections, setExpandedSections] = useState<Set<SuperCategory>>(
    new Set([SuperCategory.WRITING]) // Expand writing by default
  );

  // Filter categories based on search term
  const filteredCategories = useMemo(() => {
    if (!searchTerm) return categories;

    const filtered: Record<SuperCategory, ToolConfigCategory[]> = {} as any;

    Object.entries(categories).forEach(([superCat, cats]) => {
      const matchingCats = cats.filter((cat) =>
        cat.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      if (matchingCats.length > 0) {
        filtered[superCat as SuperCategory] = matchingCats;
      }
    });

    return filtered;
  }, [categories, searchTerm]);

  // Get selected categories for display
  const selectedCategories = useMemo(() => {
    const selected: ToolConfigCategory[] = [];
    Object.values(categories).forEach((cats) => {
      cats.forEach((cat) => {
        if (selectedIds.includes(cat.id)) {
          selected.push(cat);
        }
      });
    });
    return selected;
  }, [categories, selectedIds]);

  const toggleSection = (superCategory: SuperCategory) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(superCategory)) {
      newExpanded.delete(superCategory);
    } else {
      newExpanded.add(superCategory);
    }
    setExpandedSections(newExpanded);
  };

  const toggleCategory = (categoryId: number) => {
    if (selectedIds.includes(categoryId)) {
      onChange(selectedIds.filter((id) => id !== categoryId));
    } else {
      onChange([...selectedIds, categoryId]);
    }
  };

  const removeCategory = (categoryId: number, event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    onChange(selectedIds.filter((id) => id !== categoryId));
  };

  const clearAll = () => {
    onChange([]);
  };

  return (
    <div className="space-y-4">
      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search categories..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 h-10"
        />
      </div>

      {/* Selected Categories */}
      {selectedCategories.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Selected ({selectedCategories.length})
            </Label>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={clearAll}
              className="h-7 px-3 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Clear All
            </Button>
          </div>
          <div
            className="flex flex-wrap gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-y-auto"
            style={{ maxHeight: "100px" }}
          >
            {selectedCategories.map((category) => (
              <Badge
                key={category.id}
                variant="secondary"
                className="text-xs flex items-center gap-1.5 px-2.5 py-1 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 shadow-sm"
              >
                <span className="text-gray-700 dark:text-gray-300">
                  {category.name}
                </span>
                <button
                  type="button"
                  onClick={(e) => removeCategory(category.id, e)}
                  className="h-3 w-3 cursor-pointer hover:text-red-500 transition-colors flex items-center justify-center"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Categories by Super Category */}
      <div className="border rounded-lg bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <div className="overflow-y-auto custom-scrollbar" style={{ maxHeight }}>
          <div className="p-4 space-y-3">
            {Object.entries(filteredCategories).map(([superCat, cats]) => {
              const superCategory = superCat as SuperCategory;
              const isExpanded = expandedSections.has(superCategory);
              const selectedInSection = cats.filter((cat) =>
                selectedIds.includes(cat.id)
              ).length;

              return (
                <Collapsible
                  key={superCategory}
                  open={isExpanded}
                  onOpenChange={() => toggleSection(superCategory)}
                >
                  <CollapsibleTrigger asChild>
                    <Button
                      type="button"
                      variant="ghost"
                      className={`w-full justify-between py-2 px-3 h-auto rounded-lg border transition-all ${superCategoryColors[superCategory]}`}
                    >
                      <div className="flex items-center gap-3">
                        {superCategoryIcons[superCategory]}
                        <span className="font-medium text-sm">
                          {superCategory.replace("_", " ")}
                        </span>
                        <Badge
                          variant="outline"
                          className="text-xs font-normal border-gray-300 dark:border-gray-600"
                        >
                          {cats.length}
                        </Badge>
                        {selectedInSection > 0 && (
                          <Badge className="text-xs bg-primary text-primary-foreground">
                            {selectedInSection} selected
                          </Badge>
                        )}
                      </div>
                      {isExpanded ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </CollapsibleTrigger>

                  <CollapsibleContent className="space-y-2 pt-2">
                    <div className="pl-6 space-y-2">
                      <div
                        className="overflow-y-auto pr-2"
                        style={{ maxHeight: "200px" }}
                      >
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                          {cats.map((category) => (
                            <div
                              key={category.id}
                              className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-transparent hover:border-gray-200 dark:hover:border-gray-600"
                            >
                              <Checkbox
                                id={`category-${category.id}`}
                                checked={selectedIds.includes(category.id)}
                                onCheckedChange={(checked) => {
                                  // Handle the CheckedState type properly
                                  if (checked !== "indeterminate") {
                                    toggleCategory(category.id);
                                  }
                                }}
                                className="flex-shrink-0"
                              />
                              <Label
                                htmlFor={`category-${category.id}`}
                                className="text-sm font-normal cursor-pointer flex-1 leading-relaxed text-gray-700 dark:text-gray-300"
                              >
                                {category.name}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              );
            })}
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
        {Object.values(filteredCategories).reduce(
          (sum, cats) => sum + cats.length,
          0
        )}{" "}
        categories available
        {searchTerm && " (filtered)"}
      </div>
    </div>
  );
}
