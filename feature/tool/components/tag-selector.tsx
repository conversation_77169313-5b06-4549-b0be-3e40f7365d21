"use client";

import React, { useState, useMemo } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, X, Hash, SortAsc, SortDesc } from "lucide-react";
import { ToolConfigTag } from "@/types/tool-api";

interface TagSelectorProps {
  tags: ToolConfigTag[];
  selectedIds: number[];
  onChange: (selectedIds: number[]) => void;
  maxHeight?: string;
}

type SortOption = "name-asc" | "name-desc" | "selected-first";

export function TagSelector({
  tags,
  selectedIds,
  onChange,
  maxHeight = "400px",
}: TagSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<SortOption>("name-asc");
  const [showOnlySelected, setShowOnlySelected] = useState(false);

  // Filter and sort tags
  const processedTags = useMemo(() => {
    let filtered = tags;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter((tag) =>
        tag.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by selected only
    if (showOnlySelected) {
      filtered = filtered.filter((tag) => selectedIds.includes(tag.id));
    }

    // Sort tags
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case "name-desc":
          return b.name.localeCompare(a.name);
        case "selected-first":
          const aSelected = selectedIds.includes(a.id);
          const bSelected = selectedIds.includes(b.id);
          if (aSelected && !bSelected) return -1;
          if (!aSelected && bSelected) return 1;
          return a.name.localeCompare(b.name);
        case "name-asc":
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return sorted;
  }, [tags, searchTerm, sortBy, showOnlySelected, selectedIds]);

  // Get selected tags for display
  const selectedTags = useMemo(() => {
    return tags.filter((tag) => selectedIds.includes(tag.id));
  }, [tags, selectedIds]);

  // Group tags by first letter for better organization
  const groupedTags = useMemo(() => {
    const groups: Record<string, ToolConfigTag[]> = {};

    processedTags.forEach((tag) => {
      const firstLetter = tag.name.charAt(0).toUpperCase();
      if (!groups[firstLetter]) {
        groups[firstLetter] = [];
      }
      groups[firstLetter].push(tag);
    });

    return groups;
  }, [processedTags]);

  const toggleTag = (tagId: number) => {
    if (selectedIds.includes(tagId)) {
      onChange(selectedIds.filter((id) => id !== tagId));
    } else {
      onChange([...selectedIds, tagId]);
    }
  };

  const removeTag = (tagId: number, event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    onChange(selectedIds.filter((id) => id !== tagId));
  };

  const clearAll = () => {
    onChange([]);
  };

  return (
    <div className="space-y-4">
      {/* Search and Controls */}
      <div className="space-y-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search tags..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 h-10"
          />
        </div>

        <div className="flex items-center gap-3 flex-wrap">
          <Select
            value={sortBy}
            onValueChange={(value: SortOption) => setSortBy(value)}
          >
            <SelectTrigger className="w-40 h-9">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name-asc">
                <div className="flex items-center gap-2">
                  <SortAsc className="h-4 w-4" />
                  Name A-Z
                </div>
              </SelectItem>
              <SelectItem value="name-desc">
                <div className="flex items-center gap-2">
                  <SortDesc className="h-4 w-4" />
                  Name Z-A
                </div>
              </SelectItem>
              <SelectItem value="selected-first">
                <div className="flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  Selected First
                </div>
              </SelectItem>
            </SelectContent>
          </Select>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="show-selected"
              checked={showOnlySelected}
              onCheckedChange={(checked) => {
                if (checked !== "indeterminate") {
                  setShowOnlySelected(checked === true);
                }
              }}
            />
            <Label
              htmlFor="show-selected"
              className="text-sm text-gray-600 dark:text-gray-400"
            >
              Show only selected
            </Label>
          </div>
        </div>
      </div>

      {/* Selected Tags */}
      {selectedTags.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Selected ({selectedTags.length})
            </Label>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={clearAll}
              className="h-7 px-3 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Clear All
            </Button>
          </div>
          <div
            className="flex flex-wrap gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-y-auto"
            style={{ maxHeight: "120px" }}
          >
            {selectedTags.map((tag) => (
              <Badge
                key={tag.id}
                variant="secondary"
                className="text-xs flex items-center gap-1.5 px-2.5 py-1 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 shadow-sm"
              >
                <span className="text-gray-700 dark:text-gray-300">
                  {tag.name}
                </span>
                <button
                  type="button"
                  onClick={(e) => removeTag(tag.id, e)}
                  className="h-3 w-3 cursor-pointer hover:text-red-500 transition-colors flex items-center justify-center"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Tags List */}
      <div className="border rounded-lg bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <div className="overflow-y-auto custom-scrollbar" style={{ maxHeight }}>
          <div className="p-4 space-y-4">
            {Object.keys(groupedTags).length === 0 ? (
              <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                <Hash className="h-12 w-12 mx-auto mb-3 opacity-30" />
                <p className="text-sm font-medium">No tags found</p>
                {searchTerm && (
                  <p className="text-xs mt-1 text-gray-400">
                    Try adjusting your search terms
                  </p>
                )}
              </div>
            ) : (
              Object.entries(groupedTags)
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([letter, letterTags]) => (
                  <div key={letter} className="space-y-3">
                    <div className="flex items-center gap-3 pb-2 border-b border-gray-100 dark:border-gray-700">
                      <div className="w-7 h-7 rounded-full bg-primary/10 text-primary flex items-center justify-center text-sm font-semibold">
                        {letter}
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                        {letterTags.length} tags
                      </span>
                    </div>

                    <div
                      className="overflow-y-auto pr-2"
                      style={{ maxHeight: "250px" }}
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-2">
                        {letterTags.map((tag) => (
                          <div
                            key={tag.id}
                            className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-transparent hover:border-gray-200 dark:hover:border-gray-600"
                          >
                            <Checkbox
                              id={`tag-${tag.id}`}
                              checked={selectedIds.includes(tag.id)}
                              onCheckedChange={(checked) => {
                                if (checked !== "indeterminate") {
                                  toggleTag(tag.id);
                                }
                              }}
                              className="flex-shrink-0"
                            />
                            <Label
                              htmlFor={`tag-${tag.id}`}
                              className="text-sm font-normal cursor-pointer flex-1 leading-relaxed text-gray-700 dark:text-gray-300"
                            >
                              {tag.name}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))
            )}
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
        Showing {processedTags.length} of {tags.length} tags
        {searchTerm && " (filtered)"}
        {showOnlySelected && " (selected only)"}
      </div>
    </div>
  );
}
