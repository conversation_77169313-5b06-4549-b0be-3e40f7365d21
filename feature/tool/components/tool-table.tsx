"use client";

import React from "react";
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Pencil,
  Trash2,
  ExternalLink,
  Eye,
  Languages,
} from "lucide-react";
import {
  useToolDeleteActions,
  useToolFiltersState,
  useToolFiltersActions,
  useToolTranslationActions,
} from "../store/use-tool-store";
import { useRouter } from "next/navigation";
import { PaginatedResponse, ToolResponse } from "@/types/tool-api";
import { CustomPagination } from "@/components/custom/custom-pagination";

interface ToolTableProps {
  data?: PaginatedResponse<ToolResponse>;
  isLoading: boolean;
  error: any;
}

export function ToolTable({ data, isLoading, error }: ToolTableProps) {
  const router = useRouter();
  const { openDeleteDialog } = useToolDeleteActions();
  const { openTranslationDialog } = useToolTranslationActions();
  const { currentPage, pageSize } = useToolFiltersState();
  const { setCurrentPage } = useToolFiltersActions();

  const getStatusBadge = (status: string) => {
    const statusColors: Record<string, string> = {
      PENDING: "bg-yellow-100 text-yellow-800",
      APPROVED: "bg-green-100 text-green-800",
      REJECTED: "bg-red-100 text-red-800",
      PUBLISHED: "bg-primary/10 text-primary",
      DRAFT: "bg-gray-100 text-gray-800",
      ARCHIVED: "bg-gray-100 text-gray-600",
    };

    return (
      <Badge className={statusColors[status] || "bg-gray-100 text-gray-800"}>
        {status}
      </Badge>
    );
  };

  const getToolTypeBadge = (toolType: string) => {
    const typeColors: Record<string, string> = {
      SAAS: "bg-primary/10 text-primary",
      MOBILE_APP: "bg-green-100 text-green-800",
      DESKTOP_APP: "bg-purple-100 text-purple-800",
      AI_MODEL: "bg-orange-100 text-orange-800",
      CHROME_EXTENSION: "bg-teal-100 text-teal-800",
    };

    return (
      <Badge className={typeColors[toolType] || "bg-gray-100 text-gray-800"}>
        {toolType.replace("_", " ")}
      </Badge>
    );
  };

  const getPricingBadges = (pricingModels: string[]) => {
    const pricingColors: Record<string, string> = {
      FREE: "bg-green-100 text-green-800",
      FREEMIUM: "bg-primary/10 text-primary",
      PAID: "bg-red-100 text-red-800",
      SUBSCRIPTION: "bg-purple-100 text-purple-800",
      ONE_TIME: "bg-yellow-100 text-yellow-800",
      USAGE_BASED: "bg-orange-100 text-orange-800",
      CUSTOM: "bg-gray-100 text-gray-800",
    };

    return (
      <div className="flex flex-wrap gap-1">
        {pricingModels.map((model, index) => (
          <Badge
            key={index}
            className={pricingColors[model] || "bg-gray-100 text-gray-800"}
          >
            {model.replace("_", " ")}
          </Badge>
        ))}
      </div>
    );
  };

  const handleEdit = (tool: ToolResponse) => {
    router.push(`/admin/tools/edit/${tool.id}`);
  };

  const handleDelete = (tool: ToolResponse) => {
    openDeleteDialog(tool);
  };

  const handleTranslate = (tool: ToolResponse) => {
    openTranslationDialog(tool);
  };

  const handleView = (tool: ToolResponse) => {
    if (tool.website_url) {
      window.open(tool.website_url, "_blank");
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading tools</p>
      </div>
    );
  }

  if (!data?.items?.length) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No tools found</p>
      </div>
    );
  }

  const startNumber = (currentPage - 1) * pageSize;

  return (
    <div className="space-y-4 ">
      <div className="rounded-md border px-2">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">#</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Pricing</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Published</TableHead>
              {/* <TableHead>Created</TableHead> */}
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.items.map((tool, index) => {
              const displayName = tool.translations[0]?.name || tool.name;
              const shortDescription = tool.translations[0]?.short_description;

              return (
                <TableRow key={tool.id}>
                  <TableCell className="font-medium">
                    {startNumber + index + 1}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{displayName}</div>
                      {shortDescription && (
                        <div className="text-sm text-gray-500 max-w-xs truncate">
                          {shortDescription}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{getToolTypeBadge(tool.tool_type)}</TableCell>
                  <TableCell>{getPricingBadges(tool.pricing_models)}</TableCell>
                  <TableCell>{getStatusBadge(tool.status)}</TableCell>
                  <TableCell>
                    <Badge
                      className={
                        tool.is_published
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800"
                      }
                    >
                      {tool.is_published ? "Yes" : "No"}
                    </Badge>
                  </TableCell>
                  {/* <TableCell>
                    {format(new Date(tool.created_at), "MMM dd, yyyy")}
                  </TableCell> */}
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {tool.website_url && (
                          <DropdownMenuItem onClick={() => handleView(tool)}>
                            <ExternalLink className="mr-2 h-4 w-4" />
                            View
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={() => handleEdit(tool)}>
                          <Pencil className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleTranslate(tool)}>
                          <Languages className="mr-2 h-4 w-4" />
                          Translate
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDelete(tool)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4 text-destructive" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {data.pagination && (
        <CustomPagination
          currentPage={data.pagination.page}
          totalPages={data.pagination.total_pages}
          onPageChange={setCurrentPage}
          hasNext={data.pagination.has_next}
          hasPrev={data.pagination.has_prev}
        />
      )}
    </div>
  );
}
