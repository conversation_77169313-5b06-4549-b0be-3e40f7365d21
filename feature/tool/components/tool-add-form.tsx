"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import {
  ArrowL<PERSON>t,
  Save,
  Eye,
  Plus,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Image,
  FileText,
  Share2,
  Search,
  HelpCircle,
  Loader2,
} from "lucide-react";
import { TipTapEditorComplete } from "@/feature/rich-text/components/tiptap-editor-complete";
import {
  useCreateTool,
  useUpdateTool,
  useGetTool,
} from "../hooks/use-tool-data";
import { useToolConfig } from "../hooks/use-tool-config";
import { ToolSection } from "./tool-section";
import { CategorySelector } from "./category-selector";
import { TagSelector } from "./tag-selector";
import { ToolFAQManager } from "./tool-faq-manager";
import { LogoUpload } from "./logo-upload";
import { ScreenshotUpload } from "./screenshot-upload";
import {
  CreateToolRequest,
  ToolType,
  PricingModel,
  ToolStatus,
} from "@/types/tool-api";
import { Separator } from "@/components/ui/separator";

const createToolSchema = z.object({
  name: z.string().min(1, "Tool name is required"),
  website_url: z
    .string()
    .url("Must be a valid URL")
    .optional()
    .or(z.literal("")),
  logo_url: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  screenshot_urls: z.array(z.string().url()).optional(),
  tool_type: z.nativeEnum(ToolType),
  pricing_models: z
    .array(z.nativeEnum(PricingModel))
    .min(1, "At least one pricing model is required"),
  status: z.nativeEnum(ToolStatus),
  is_published: z.boolean(),
  is_available: z.boolean(),
  category_ids: z.array(z.number()).optional(),
  tag_ids: z.array(z.number()).optional(),
  twitter_url: z
    .string()
    .url("Must be a valid URL")
    .optional()
    .or(z.literal("")),
  facebook_url: z
    .string()
    .url("Must be a valid URL")
    .optional()
    .or(z.literal("")),
  linkedin_url: z
    .string()
    .url("Must be a valid URL")
    .optional()
    .or(z.literal("")),
  github_url: z
    .string()
    .url("Must be a valid URL")
    .optional()
    .or(z.literal("")),
  contact_email: z
    .string()
    .email("Must be a valid email")
    .optional()
    .or(z.literal("")),
  support_email: z
    .string()
    .email("Must be a valid email")
    .optional()
    .or(z.literal("")),
  // Translation fields
  short_description: z.string().optional(),
  introduction: z.string().optional(),
  what_is_it: z.string().optional(),
  how_to_use: z.string().optional(),
  full_feature: z.string().optional(),
  short_feature: z.string().optional(),
  pricing: z.string().optional(),
  meta_title: z.string().optional(),
  meta_description: z.string().optional(),
  // FAQ fields
  faqs: z
    .array(
      z.object({
        question: z.string().min(1, "FAQ question is required"),
        answer: z.string().min(1, "FAQ answer is required"),
        order: z.number().optional(),
        isNew: z.boolean().optional(),
      })
    )
    .max(10, "Maximum 10 FAQs allowed")
    .optional(),
});

type CreateToolFormData = z.infer<typeof createToolSchema>;

interface ToolAddFormProps {
  toolId?: string;
  mode?: "add" | "edit";
  dict?: any;
  lang?: string;
}

export function ToolAddForm({
  toolId,
  mode = "add",
  dict,
  lang,
}: ToolAddFormProps) {
  const router = useRouter();
  const [faqs, setFAQs] = useState<
    Array<{ question: string; answer: string; order?: number; isNew?: boolean }>
  >([]);
  const isEditMode = mode === "edit" && toolId;

  const form = useForm<CreateToolFormData>({
    resolver: zodResolver(createToolSchema),
    defaultValues: {
      name: "",
      website_url: "",
      logo_url: "",
      screenshot_urls: [],
      tool_type: ToolType.SAAS,
      pricing_models: [PricingModel.FREE],
      status: ToolStatus.DRAFT,
      is_published: false,
      is_available: true,
      category_ids: [],
      tag_ids: [],
      twitter_url: "",
      facebook_url: "",
      linkedin_url: "",
      github_url: "",
      contact_email: "",
      support_email: "",
      short_description: "",
      introduction: "",
      what_is_it: "",
      how_to_use: "",
      full_feature: "",
      short_feature: "",
      pricing: "",
      meta_title: "",
      meta_description: "",
      faqs: [],
    },
  });

  const createTool = useCreateTool();
  const updateTool = useUpdateTool();
  const { data: existingTool, isLoading: isLoadingTool } = useGetTool(
    toolId || ""
  );

  // Fetch tool configuration
  const { data: toolConfig, isLoading: isLoadingConfig } = useToolConfig();

  // Load existing tool data for editing
  useEffect(() => {
    if (isEditMode && existingTool) {
      const translation = existingTool.translations?.[0];
      form.reset({
        name: existingTool.name,
        website_url: existingTool.website_url || "",
        logo_url: existingTool.logo_url || "",
        screenshot_urls: existingTool.screenshot_urls || [],
        tool_type: existingTool.tool_type,
        pricing_models: existingTool.pricing_models,
        status: existingTool.status,
        is_published: existingTool.is_published,
        is_available: existingTool.is_available,
        category_ids: existingTool.category_ids || [],
        tag_ids: existingTool.tag_ids || [],
        twitter_url: existingTool.twitter_url || "",
        facebook_url: existingTool.facebook_url || "",
        linkedin_url: existingTool.linkedin_url || "",
        github_url: existingTool.github_url || "",
        contact_email: existingTool.contact_email || "",
        support_email: existingTool.support_email || "",
        short_description: translation?.short_description || "",
        introduction: translation?.introduction || "",
        what_is_it: translation?.what_is_it || "",
        how_to_use: translation?.how_to_use || "",
        full_feature: translation?.full_feature || "",
        short_feature: translation?.short_feature || "",
        pricing: translation?.pricing || "",
        meta_title: translation?.meta_title || "",
        meta_description: translation?.meta_description || "",
        faqs:
          existingTool.faqs?.map((faq) => {
            const faqTranslation = faq.translations?.[0];
            return {
              question: faqTranslation?.question || "",
              answer: faqTranslation?.answer || "",
              order: faq.order,
              isNew: false,
            };
          }) || [],
      });
      setFAQs(
        existingTool.faqs?.map((faq) => {
          const faqTranslation = faq.translations?.[0];
          return {
            question: faqTranslation?.question || "",
            answer: faqTranslation?.answer || "",
            order: faq.order,
            isNew: false,
          };
        }) || []
      );
    }
  }, [existingTool, isEditMode]);

  const handleBack = () => {
    router.push("/admin/tools");
  };

  const onSubmit = async (data: CreateToolFormData) => {
    try {
      // Include FAQs in the submission data
      const submitData = {
        ...data,
        faqs: faqs.filter((faq) => faq.question.trim() && faq.answer.trim()),
      };

      if (isEditMode) {
        await updateTool.mutateAsync({ id: toolId!, data: submitData });
      } else {
        await createTool.mutateAsync(submitData);
      }
      router.push("/admin/tools");
    } catch (error: any) {
      // Error toasts are handled by the hooks
      console.error("Tool submission error:", error);
    }
  };

  const handleSaveAsDraft = () => {
    form.setValue("status", ToolStatus.DRAFT);
    form.setValue("is_published", false);
    form.handleSubmit(onSubmit)();
  };

  const handlePublish = () => {
    form.setValue("status", ToolStatus.PUBLISHED);
    form.setValue("is_published", true);
    form.handleSubmit(onSubmit)();
  };

  const isLoading =
    createTool.isPending || updateTool.isPending || isLoadingTool;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 shadow-none rounded-lg mb-6">
          <div className="px-6 py-4 bg-gray-100 border-gray-200 dark:border-gray-700 rounded-2xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 ">
                <Button
                  variant="ghost"
                  onClick={handleBack}
                  className="flex items-center bg-gray-200/60 hover:bg-gray-200"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Tools
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {isEditMode ? "Edit Tool" : "Add New Tool"}
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isEditMode
                      ? "Update tool information and settings"
                      : "Create a comprehensive tool listing"}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  onClick={handleSaveAsDraft}
                  disabled={isLoading}
                  className="flex items-center"
                >
                  <Save className="mr-2 h-4 w-4" />
                  Save Draft
                </Button>
                <Button
                  onClick={handlePublish}
                  disabled={isLoading}
                  className="flex items-center bg-primary hover:bg-primary/90"
                >
                  <Eye className="mr-2 h-4 w-4" />
                  {isLoading ? "Publishing..." : "Publish"}
                </Button>
              </div>
            </div>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information Section */}
            <ToolSection
              title="Basic Information"
              description="Essential details about your tool"
              icon={<Settings className="h-4 w-4" />}
              required
              defaultExpanded={true}
            >
              <div className="space-y-6">
                {/* Status & Settings Section */}
                <div className="pt-6 bg-primary/5 border border-primary/30 rounded-2xl p-5">
                  <div className="mb-4">
                    <h4 className="text-lg font-semibold text-gray-900 mb-1">
                      Status & Settings
                    </h4>
                    <p className="text-sm text-gray-500">
                      Configure publication and availability settings
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">
                            Status
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.values(ToolStatus).map((status) => (
                                <SelectItem key={status} value={status}>
                                  {status}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="is_available"
                      render={({ field }) => (
                        <FormItem className="flex flex-col space-y-2">
                          <FormLabel className="text-sm font-medium text-gray-700">
                            Available
                          </FormLabel>
                          <div className="flex items-center space-x-2">
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <span className="text-sm text-gray-500">
                              {field.value ? "Available" : "Unavailable"}
                            </span>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="is_published"
                      render={({ field }) => (
                        <FormItem className="flex flex-col space-y-2">
                          <FormLabel className="text-sm font-medium text-gray-700">
                            Published
                          </FormLabel>
                          <div className="flex items-center space-x-2">
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <span className="text-sm text-gray-500">
                              {field.value ? "Published" : "Draft"}
                            </span>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                <div className="space-y-4">
                  {/* Tool Name and Tool Type in a row */}
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="w-full md:w-[70%]">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">
                              Tool Name *
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="Enter tool name"
                                className="mt-1"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="w-full md:w-[30%]">
                      <FormField
                        control={form.control}
                        name="tool_type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">
                              Tool Type *
                            </FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className="mt-1 w-full">
                                  <SelectValue placeholder="Select tool type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value={ToolType.SAAS}>
                                  SaaS
                                </SelectItem>
                                <SelectItem value={ToolType.MOBILE_APP}>
                                  Mobile App
                                </SelectItem>
                                <SelectItem value={ToolType.DESKTOP_APP}>
                                  Desktop App
                                </SelectItem>
                                <SelectItem value={ToolType.AI_MODEL}>
                                  AI Model
                                </SelectItem>
                                <SelectItem value={ToolType.CHROME_EXTENSION}>
                                  Chrome Extension
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Pricing Models and Logo in a row */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="pricing_models"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">
                            Pricing Models *
                          </FormLabel>
                          <div className="grid grid-cols-2 gap-2 p-3 border rounded-lg bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600">
                            {Object.values(PricingModel).map((pricing) => (
                              <div
                                key={pricing}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  id={pricing}
                                  checked={field.value?.includes(pricing)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      field.onChange([
                                        ...(field.value || []),
                                        pricing,
                                      ]);
                                    } else {
                                      field.onChange(
                                        field.value?.filter(
                                          (item) => item !== pricing
                                        )
                                      );
                                    }
                                  }}
                                />
                                <Label
                                  htmlFor={pricing}
                                  className="text-xs font-normal cursor-pointer text-gray-700 dark:text-gray-300"
                                >
                                  {pricing.replace("_", " ")}
                                </Label>
                              </div>
                            ))}
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="logo_url"
                      render={({ field }) => (
                        <FormItem>
                          <LogoUpload
                            value={field.value || ""}
                            onChange={field.onChange}
                            dict={dict}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Website URL */}
                <FormField
                  control={form.control}
                  name="website_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        Website URL
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="https://example.com"
                          className="mt-1"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Categories and Tags in a row */}
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Categories Selection - 70% width */}
                  {toolConfig?.categories && (
                    <div className="w-full lg:w-[60%]">
                      <FormField
                        control={form.control}
                        name="category_ids"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">
                              Categories
                            </FormLabel>
                            <CategorySelector
                              categories={toolConfig.categories}
                              selectedIds={field.value || []}
                              onChange={field.onChange}
                              maxHeight="300px"
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}

                  {/* Tags Selection - 30% width */}
                  {toolConfig?.tags && (
                    <div className="w-full lg:w-[40%]">
                      <FormField
                        control={form.control}
                        name="tag_ids"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">
                              Tags
                            </FormLabel>
                            <TagSelector
                              tags={toolConfig.tags}
                              selectedIds={field.value || []}
                              onChange={field.onChange}
                              maxHeight="250px"
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                </div>
              </div>
            </ToolSection>

            {/* Screenshots Section */}
            <ToolSection
              title="Screenshots & Media"
              description="Visual content to showcase your tool"
              icon={<Image className="h-4 w-4" />}
              defaultExpanded={true}
            >
              <FormField
                control={form.control}
                name="screenshot_urls"
                render={({ field }) => (
                  <FormItem>
                    <ScreenshotUpload
                      value={field.value || []}
                      onChange={field.onChange}
                      maxFiles={10}
                      dict={dict}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </ToolSection>
            {/* Content & Descriptions Section - ALL FIXED EDITORS */}
            <ToolSection
              title="Content & Descriptions"
              description="Detailed information about your tool"
              icon={<FileText className="h-4 w-4" />}
              defaultExpanded={true}
            >
              <div className="space-y-6">
                <FormField
                  control={form.control}
                  name="short_description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base font-semibold text-gray-700">
                        Short Description
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Brief description of the tool"
                          rows={3}
                          className="mt-1"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="introduction"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base font-semibold text-gray-700">
                        Introduction
                      </FormLabel>
                      <FormControl>
                        <TipTapEditorComplete
                          content={field.value || ""}
                          onChange={field.onChange}
                          placeholder="Write a detailed introduction about your tool..."
                          minHeight="150px"
                          showWordCount={true}
                          characterLimit={1000}
                          showPreview={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="what_is_it"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base font-semibold text-gray-700">
                        What is it?
                      </FormLabel>
                      <FormControl>
                        <TipTapEditorComplete
                          content={field.value || ""}
                          onChange={field.onChange}
                          placeholder="Explain what this tool is and what it does..."
                          minHeight="150px"
                          showWordCount={true}
                          characterLimit={800}
                          showPreview={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="how_to_use"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base font-semibold text-gray-700">
                        How to use
                      </FormLabel>
                      <FormControl>
                        <TipTapEditorComplete
                          content={field.value || ""}
                          onChange={field.onChange}
                          placeholder="Provide step-by-step instructions on how to use this tool..."
                          minHeight="150px"
                          showWordCount={true}
                          characterLimit={1200}
                          showPreview={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="full_feature"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base font-semibold text-gray-700">
                        Full Features
                      </FormLabel>
                      <FormControl>
                        <TipTapEditorComplete
                          content={field.value || ""}
                          onChange={field.onChange}
                          placeholder="Describe all features and capabilities of your tool..."
                          minHeight="200px"
                          showWordCount={true}
                          characterLimit={2000}
                          showPreview={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="short_feature"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base font-semibold text-gray-700">
                        Short Features
                      </FormLabel>
                      <FormControl>
                        <TipTapEditorComplete
                          content={field.value || ""}
                          onChange={field.onChange}
                          placeholder="Highlight the main features and benefits..."
                          minHeight="150px"
                          showWordCount={true}
                          characterLimit={800}
                          showPreview={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="pricing"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base font-semibold text-gray-700">
                        Pricing Information
                      </FormLabel>
                      <FormControl>
                        <TipTapEditorComplete
                          content={field.value || ""}
                          onChange={field.onChange}
                          placeholder="Provide detailed pricing information, plans, and features..."
                          minHeight="200px"
                          showWordCount={true}
                          characterLimit={1500}
                          showPreview={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </ToolSection>

            {/* Social Media & Contact Section */}
            <ToolSection
              title="Social Media & Contact"
              description="Connect with your audience"
              icon={<Share2 className="h-4 w-4" />}
              defaultExpanded={true}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="twitter_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        Twitter URL
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="https://twitter.com/username"
                          className="mt-1"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="facebook_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        Facebook URL
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="https://facebook.com/page"
                          className="mt-1"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="linkedin_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        LinkedIn URL
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="https://linkedin.com/company/name"
                          className="mt-1"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="github_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        GitHub URL
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="https://github.com/username"
                          className="mt-1"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contact_email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        Contact Email
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="<EMAIL>"
                          className="mt-1"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="support_email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        Support Email
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="<EMAIL>"
                          className="mt-1"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </ToolSection>

            {/* SEO Section */}
            <ToolSection
              title="SEO Settings"
              description="Optimize for search engines"
              icon={<Search className="h-4 w-4" />}
              defaultExpanded={true}
            >
              <div className="space-y-6">
                <FormField
                  control={form.control}
                  name="meta_title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        Meta Title
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="SEO optimized title (recommended: 50-60 characters)"
                          className="mt-1"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="meta_description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        Meta Description
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="SEO meta description (recommended: 150-160 characters)"
                          rows={4}
                          className="mt-1"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </ToolSection>

            {/* FAQ Section */}
            <ToolSection
              title="Frequently Asked Questions"
              description="Help users understand your tool better"
              icon={<HelpCircle className="h-4 w-4" />}
              defaultExpanded={true}
            >
              <ToolFAQManager
                value={faqs}
                onChange={(newFAQs) => {
                  setFAQs(newFAQs);
                  form.setValue("faqs", newFAQs);
                }}
                maxFAQs={10}
              />
            </ToolSection>
          </form>
        </Form>
      </div>
    </div>
  );
}
