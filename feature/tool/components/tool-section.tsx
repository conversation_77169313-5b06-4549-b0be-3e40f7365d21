"use client";

import React, { useState, ReactNode } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";

interface ToolSectionProps {
  title: string;
  description?: string;
  icon?: ReactNode;
  children: ReactNode;
  defaultExpanded?: boolean;
  required?: boolean;
  className?: string;
}

export function ToolSection({
  title,
  description,
  icon,
  children,
  defaultExpanded = true,
  required = false,
  className,
}: ToolSectionProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <Card
      className={cn(
        "shadow-none border border-gray-200 overflow-hidden transition-all duration-200 p-0 pt-4",
        className
      )}
    >
      <CardHeader className="">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {icon && (
              <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                <div className="text-primary">{icon}</div>
              </div>
            )}
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
                {required && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Required
                  </span>
                )}
              </div>
              {description && (
                <p className="text-xs text-gray-500">{description}</p>
              )}
            </div>
          </div>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={toggleExpanded}
            className="ml-4 h-8 w-8 p-0 text-gray-400 hover:text-primary hover:bg-primary/10 transition-colors"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>

      <div
        className={cn(
          "transition-all duration-300 ease-in-out overflow-hidden",
          isExpanded ? "max-h-[9999px] opacity-100" : "max-h-0 opacity-0"
        )}
      >
        <CardContent className="pt-0 pb-6">
          <div className="border-t border-gray-100 pt-6">{children}</div>
        </CardContent>
      </div>
    </Card>
  );
}
