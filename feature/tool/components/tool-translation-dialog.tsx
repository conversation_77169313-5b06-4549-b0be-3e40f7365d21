"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Languages, Loader2 } from "lucide-react";
import {
  useToolTranslationState,
  useToolTranslationActions,
} from "../store/use-tool-store";
import {
  useGetAvailableLanguages,
  useTranslateTool,
} from "../hooks/use-tool-data";

export function ToolTranslationDialog() {
  const [isTranslating, setIsTranslating] = useState(false);
  const { isTranslationDialogOpen, translatingTool } = useToolTranslationState();
  const { closeTranslationDialog } = useToolTranslationActions();
  const translateTool = useTranslateTool();

  const { data: languageData, isLoading: isLoadingLanguages } =
    useGetAvailableLanguages(translatingTool?.id || "");

  const handleTranslate = async (languageCode: string, languageName: string) => {
    if (!translatingTool) return;

    setIsTranslating(true);
    try {
      await translateTool.mutateAsync({
        toolId: translatingTool.id,
        language: languageCode,
      });
      closeTranslationDialog();
    } catch (error) {
      // Error is handled by the hook
    } finally {
      setIsTranslating(false);
    }
  };

  return (
    <Dialog open={isTranslationDialogOpen} onOpenChange={closeTranslationDialog}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Languages className="h-5 w-5" />
            Translate Tool
          </DialogTitle>
          <DialogDescription>
            Select a language to translate "{translatingTool?.name}" to.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Available Languages */}
          <div>
            <h3 className="text-sm font-medium mb-3">Available Languages</h3>
            {isLoadingLanguages ? (
              <div className="space-y-2">
                {Array.from({ length: 2 }).map((_, index) => (
                  <Skeleton key={index} className="h-10 w-full" />
                ))}
              </div>
            ) : languageData?.available_languages?.length ? (
              <div className="grid gap-2">
                {languageData.available_languages.map((language) => (
                  <div
                    key={language.code}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{language.name}</span>
                      <Badge variant="secondary" className="text-xs">
                        {language.code}
                      </Badge>
                    </div>
                    <Button
                      size="sm"
                      onClick={() =>
                        handleTranslate(language.code, language.name)
                      }
                      disabled={isTranslating || translateTool.isPending}
                      className="min-w-[100px]"
                    >
                      {isTranslating ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Translating...
                        </>
                      ) : (
                        "Translate"
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Languages className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No languages available for translation</p>
                <p className="text-xs text-gray-400 mt-1">
                  All supported languages have been translated
                </p>
              </div>
            )}
          </div>

          {/* Existing Languages */}
          {languageData?.existing_languages?.length && (
            <div>
              <h3 className="text-sm font-medium mb-3">Existing Translations</h3>
              <div className="flex flex-wrap gap-2">
                {languageData.existing_languages.map((langCode) => (
                  <Badge
                    key={langCode}
                    variant="outline"
                    className="text-green-700 border-green-300 bg-green-50"
                  >
                    {langCode.toUpperCase()}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={closeTranslationDialog}
            disabled={isTranslating || translateTool.isPending}
          >
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 