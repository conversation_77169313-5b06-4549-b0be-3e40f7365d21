"use client";

import { create } from "zustand";
import { ToolFAQResponse } from "@/types/tool-api";

interface FAQFormData {
  question: string;
  answer: string;
  order?: number;
}

interface ToolFAQState {
  // FAQ List State
  faqs: ToolFAQResponse[];
  isLoading: boolean;
  error: string | null;

  // FAQ Form State
  isFormOpen: boolean;
  editingFAQ: ToolFAQResponse | null;
  formData: FAQFormData;

  // FAQ Management State
  isDragMode: boolean;
  selectedFAQs: string[];

  // Actions
  setFAQs: (faqs: ToolFAQResponse[]) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Form Actions
  openCreateForm: () => void;
  openEditForm: (faq: ToolFAQResponse) => void;
  closeForm: () => void;
  setFormData: (data: Partial<FAQFormData>) => void;
  resetForm: () => void;

  // Management Actions
  toggleDragMode: () => void;
  setDragMode: (enabled: boolean) => void;
  toggleFAQSelection: (id: string) => void;
  selectAllFAQs: (faqIds: string[]) => void;
  clearSelection: () => void;

  // FAQ Operations
  addFAQ: (faq: ToolFAQResponse) => void;
  updateFAQ: (faq: ToolFAQResponse) => void;
  removeFAQ: (id: string) => void;
  reorderFAQs: (reorderedFAQs: ToolFAQResponse[]) => void;

  // Reset
  resetState: () => void;
}

const initialFormData: FAQFormData = {
  question: "",
  answer: "",
  order: undefined,
};

export const useToolFAQStore = create<ToolFAQState>((set, get) => ({
  // Initial State
  faqs: [],
  isLoading: false,
  error: null,
  isFormOpen: false,
  editingFAQ: null,
  formData: initialFormData,
  isDragMode: false,
  selectedFAQs: [],

  // Basic Actions
  setFAQs: (faqs) => set({ faqs }),
  setIsLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),

  // Form Actions
  openCreateForm: () =>
    set({
      isFormOpen: true,
      editingFAQ: null,
      formData: initialFormData,
    }),

  openEditForm: (faq) => {
    const translation = faq.translations?.[0];
    set({
      isFormOpen: true,
      editingFAQ: faq,
      formData: {
        question: translation?.question || "",
        answer: translation?.answer || "",
        order: faq.order,
      },
    });
  },

  closeForm: () =>
    set({
      isFormOpen: false,
      editingFAQ: null,
      formData: initialFormData,
    }),

  setFormData: (data) =>
    set((state) => ({
      formData: { ...state.formData, ...data },
    })),

  resetForm: () => set({ formData: initialFormData }),

  // Management Actions
  toggleDragMode: () =>
    set((state) => ({
      isDragMode: !state.isDragMode,
      selectedFAQs: [], // Clear selection when toggling
    })),

  setDragMode: (enabled) =>
    set({
      isDragMode: enabled,
      selectedFAQs: enabled ? [] : get().selectedFAQs,
    }),

  toggleFAQSelection: (id) =>
    set((state) => ({
      selectedFAQs: state.selectedFAQs.includes(id)
        ? state.selectedFAQs.filter((faqId) => faqId !== id)
        : [...state.selectedFAQs, id],
    })),

  selectAllFAQs: (faqIds) => set({ selectedFAQs: faqIds }),

  clearSelection: () => set({ selectedFAQs: [] }),

  // FAQ Operations
  addFAQ: (faq) =>
    set((state) => ({
      faqs: [...state.faqs, faq].sort((a, b) => a.order - b.order),
    })),

  updateFAQ: (updatedFAQ) =>
    set((state) => ({
      faqs: state.faqs
        .map((faq) => (faq.id === updatedFAQ.id ? updatedFAQ : faq))
        .sort((a, b) => a.order - b.order),
    })),

  removeFAQ: (id) =>
    set((state) => ({
      faqs: state.faqs.filter((faq) => faq.id !== id),
      selectedFAQs: state.selectedFAQs.filter((faqId) => faqId !== id),
    })),

  reorderFAQs: (reorderedFAQs) => set({ faqs: reorderedFAQs }),

  // Reset
  resetState: () =>
    set({
      faqs: [],
      isLoading: false,
      error: null,
      isFormOpen: false,
      editingFAQ: null,
      formData: initialFormData,
      isDragMode: false,
      selectedFAQs: [],
    }),
}));

// Selector Hooks
export const useToolFAQState = () => {
  const {
    faqs,
    isLoading,
    error,
    isFormOpen,
    editingFAQ,
    formData,
    isDragMode,
    selectedFAQs,
  } = useToolFAQStore();

  return {
    faqs,
    isLoading,
    error,
    isFormOpen,
    editingFAQ,
    formData,
    isDragMode,
    selectedFAQs,
    faqCount: faqs.length,
    hasMaxFAQs: faqs.length >= 10,
    hasSelectedFAQs: selectedFAQs.length > 0,
  };
};

export const useToolFAQActions = () => {
  const {
    setFAQs,
    setIsLoading,
    setError,
    openCreateForm,
    openEditForm,
    closeForm,
    setFormData,
    resetForm,
    toggleDragMode,
    setDragMode,
    toggleFAQSelection,
    selectAllFAQs,
    clearSelection,
    addFAQ,
    updateFAQ,
    removeFAQ,
    reorderFAQs,
    resetState,
  } = useToolFAQStore();

  return {
    setFAQs,
    setIsLoading,
    setError,
    openCreateForm,
    openEditForm,
    closeForm,
    setFormData,
    resetForm,
    toggleDragMode,
    setDragMode,
    toggleFAQSelection,
    selectAllFAQs,
    clearSelection,
    addFAQ,
    updateFAQ,
    removeFAQ,
    reorderFAQs,
    resetState,
  };
};
