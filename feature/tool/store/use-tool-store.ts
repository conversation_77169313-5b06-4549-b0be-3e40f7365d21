import { create } from "zustand";
import { ToolResponse } from "@/types/tool-api";

interface ToolStore {
  // Form dialog state
  isFormDialogOpen: boolean;
  formMode: "create" | "edit";
  editingTool: ToolResponse | null;

  // Delete dialog state
  isDeleteDialogOpen: boolean;
  deletingTool: ToolResponse | null;

  // Translation dialog state
  isTranslationDialogOpen: boolean;
  translatingTool: ToolResponse | null;

  // Filters and search state
  searchQuery: string;
  statusFilter: string;
  toolTypeFilter: string;
  pricingModelFilter: string;
  isPublishedFilter: string;
  languageFilter: string;
  currentPage: number;
  pageSize: number;

  // Actions for form dialog
  openCreateDialog: () => void;
  openEditDialog: (tool: ToolResponse) => void;
  closeFormDialog: () => void;

  // Actions for delete dialog
  openDeleteDialog: (tool: ToolResponse) => void;
  closeDeleteDialog: () => void;

  // Actions for translation dialog
  openTranslationDialog: (tool: ToolResponse) => void;
  closeTranslationDialog: () => void;

  // Actions for filters and search
  setSearchQuery: (query: string) => void;
  setStatusFilter: (status: string) => void;
  setToolTypeFilter: (toolType: string) => void;
  setPricingModelFilter: (pricingModel: string) => void;
  setIsPublishedFilter: (isPublished: string) => void;
  setLanguageFilter: (language: string) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  resetFilters: () => void;

  // Reset all state
  resetState: () => void;
}

const initialState = {
  // Form dialog state
  isFormDialogOpen: false,
  formMode: "create" as const,
  editingTool: null,

  // Delete dialog state
  isDeleteDialogOpen: false,
  deletingTool: null,

  // Translation dialog state
  isTranslationDialogOpen: false,
  translatingTool: null,

  // Filters and search state
  searchQuery: "",
  statusFilter: "all",
  toolTypeFilter: "all",
  pricingModelFilter: "all",
  isPublishedFilter: "all",
  languageFilter: "en",
  currentPage: 1,
  pageSize: 10,
};

export const useToolStore = create<ToolStore>((set) => ({
  ...initialState,

  // Actions for form dialog
  openCreateDialog: () =>
    set({
      isFormDialogOpen: true,
      formMode: "create",
      editingTool: null,
    }),

  openEditDialog: (tool: ToolResponse) =>
    set({
      isFormDialogOpen: true,
      formMode: "edit",
      editingTool: tool,
    }),

  closeFormDialog: () =>
    set({
      isFormDialogOpen: false,
      formMode: "create",
      editingTool: null,
    }),

  // Actions for delete dialog
  openDeleteDialog: (tool: ToolResponse) =>
    set({
      isDeleteDialogOpen: true,
      deletingTool: tool,
    }),

  closeDeleteDialog: () =>
    set({
      isDeleteDialogOpen: false,
      deletingTool: null,
    }),

  // Actions for translation dialog
  openTranslationDialog: (tool: ToolResponse) =>
    set({
      isTranslationDialogOpen: true,
      translatingTool: tool,
    }),

  closeTranslationDialog: () =>
    set({
      isTranslationDialogOpen: false,
      translatingTool: null,
    }),

  // Actions for filters and search
  setSearchQuery: (query: string) =>
    set({ searchQuery: query, currentPage: 1 }),

  setStatusFilter: (status: string) =>
    set({ statusFilter: status, currentPage: 1 }),

  setToolTypeFilter: (toolType: string) =>
    set({ toolTypeFilter: toolType, currentPage: 1 }),

  setPricingModelFilter: (pricingModel: string) =>
    set({ pricingModelFilter: pricingModel, currentPage: 1 }),

  setIsPublishedFilter: (isPublished: string) =>
    set({ isPublishedFilter: isPublished, currentPage: 1 }),

  setLanguageFilter: (language: string) => set({ languageFilter: language }),

  setCurrentPage: (page: number) => set({ currentPage: page }),

  setPageSize: (size: number) => set({ pageSize: size, currentPage: 1 }),

  resetFilters: () =>
    set({
      searchQuery: "",
      statusFilter: "all",
      toolTypeFilter: "all",
      pricingModelFilter: "all",
      isPublishedFilter: "all",
      currentPage: 1,
    }),

  // Reset all state
  resetState: () => set(initialState),
}));

// Selector hooks for better performance
export const useToolFormState = () => {
  const { isFormDialogOpen, formMode, editingTool } = useToolStore();
  return { isFormDialogOpen, formMode, editingTool };
};

export const useToolDeleteState = () => {
  const { isDeleteDialogOpen, deletingTool } = useToolStore();
  return { isDeleteDialogOpen, deletingTool };
};

export const useToolTranslationState = () => {
  const { isTranslationDialogOpen, translatingTool } = useToolStore();
  return { isTranslationDialogOpen, translatingTool };
};

export const useToolFiltersState = () => {
  const {
    searchQuery,
    statusFilter,
    toolTypeFilter,
    pricingModelFilter,
    isPublishedFilter,
    languageFilter,
    currentPage,
    pageSize,
  } = useToolStore();
  return {
    searchQuery,
    statusFilter,
    toolTypeFilter,
    pricingModelFilter,
    isPublishedFilter,
    languageFilter,
    currentPage,
    pageSize,
  };
};

export const useToolFormActions = () => {
  const { openCreateDialog, openEditDialog, closeFormDialog } = useToolStore();
  return { openCreateDialog, openEditDialog, closeFormDialog };
};

export const useToolDeleteActions = () => {
  const { openDeleteDialog, closeDeleteDialog } = useToolStore();
  return { openDeleteDialog, closeDeleteDialog };
};

export const useToolTranslationActions = () => {
  const { openTranslationDialog, closeTranslationDialog } = useToolStore();
  return { openTranslationDialog, closeTranslationDialog };
};

export const useToolFiltersActions = () => {
  const {
    setSearchQuery,
    setStatusFilter,
    setToolTypeFilter,
    setPricingModelFilter,
    setIsPublishedFilter,
    setLanguageFilter,
    setCurrentPage,
    setPageSize,
    resetFilters,
  } = useToolStore();
  return {
    setSearchQuery,
    setStatusFilter,
    setToolTypeFilter,
    setPricingModelFilter,
    setIsPublishedFilter,
    setLanguageFilter,
    setCurrentPage,
    setPageSize,
    resetFilters,
  };
};
