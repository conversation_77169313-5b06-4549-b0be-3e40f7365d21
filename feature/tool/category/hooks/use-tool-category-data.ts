import {
  useApiMutation,
  useApiQuery,
  apiPost,
  apiGet,
  apiPut,
  apiDelete,
  ApiError,
} from "@/feature/core/api/api-utils";
import {
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CategoryResponse,
  PaginatedResponse,
  ListQueryParams,
} from "@/types/tool-api";
import { toast } from "sonner";

// Category API functions
const categoryApi = {
  getCategories: async (
    params?: ListQueryParams
  ): Promise<PaginatedResponse<CategoryResponse>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set("page", params.page.toString());
    if (params?.limit) searchParams.set("limit", params.limit.toString());
    if (params?.search) searchParams.set("search", params.search);
    if (params?.is_active !== undefined)
      searchParams.set("is_active", params.is_active.toString());
    if (params?.language) searchParams.set("language", params.language);

    const queryString = searchParams.toString();
    const response = await apiGet<{
      data: PaginatedResponse<CategoryResponse>;
    }>(`/api/tool/categories${queryString ? `?${queryString}` : ""}`);
    return response.data;
  },

  getCategory: async (
    id: number,
    language?: string
  ): Promise<CategoryResponse> => {
    const searchParams = new URLSearchParams();
    if (language) searchParams.set("language", language);

    const queryString = searchParams.toString();
    const response = await apiGet<{ data: CategoryResponse }>(
      `/api/tool/categories/${id}${queryString ? `?${queryString}` : ""}`
    );
    return response.data;
  },

  createCategory: async (
    data: CreateCategoryRequest
  ): Promise<CategoryResponse> => {
    const response = await apiPost<{ data: CategoryResponse }>(
      "/api/tool/categories",
      data
    );
    return response.data;
  },

  updateCategory: async (
    id: number,
    data: UpdateCategoryRequest
  ): Promise<CategoryResponse> => {
    const response = await apiPut<{ data: CategoryResponse }>(
      `/api/tool/categories/${id}`,
      data
    );
    return response.data;
  },

  deleteCategory: async (id: number): Promise<{ id: number }> => {
    const response = await apiDelete<{ data: { id: number } }>(
      `/api/tool/categories/${id}`
    );
    return response.data;
  },
};

// Hook to get categories list
export function useGetCategories(params?: ListQueryParams) {
  return useApiQuery(
    ["tool-categories", JSON.stringify(params)],
    () => categoryApi.getCategories(params),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

// Hook to get single category
export function useGetCategory(id: number, language?: string) {
  return useApiQuery(
    ["tool-category", id.toString(), language || ""],
    () => categoryApi.getCategory(id, language),
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Hook to create category
export function useCreateCategory() {
  return useApiMutation(categoryApi.createCategory, {
    showErrorToast: false,
    onSuccess: (data) => {
      toast.success("Category created successfully");
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to create category");
    },
    invalidateQueries: ["tool-categories"],
  });
}

// Hook to update category
export function useUpdateCategory() {
  return useApiMutation(
    (variables: { id: number; data: UpdateCategoryRequest }) =>
      categoryApi.updateCategory(variables.id, variables.data),
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success("Category updated successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to update category");
      },
      invalidateQueries: ["tool-categories", "tool-category"],
    }
  );
}

// Hook to delete category
export function useDeleteCategory() {
  return useApiMutation(categoryApi.deleteCategory, {
    showErrorToast: false,
    onSuccess: () => {
      toast.success("Category deleted successfully");
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to delete category");
    },
    invalidateQueries: ["tool-categories"],
  });
}

// Custom hook that aggregates all category-related hooks
export function useCategoryActions() {
  const createCategory = useCreateCategory();
  const updateCategory = useUpdateCategory();
  const deleteCategory = useDeleteCategory();

  const isLoading =
    createCategory.isPending ||
    updateCategory.isPending ||
    deleteCategory.isPending;

  return {
    createCategory,
    updateCategory,
    deleteCategory,
    isLoading,
  };
}
