"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogDescription,
  CustomDialogFooter,
  CustomDialogHeader,
  CustomDialogTitle,
} from "@/components/custom/custom-dialog";
import {
  useCategoryFormState,
  useCategoryFormActions,
} from "@/feature/tool/category/store/use-tool-category-store";
import { useDeleteCategory } from "@/feature/tool/category/hooks/use-tool-category-data";

export function CategoryDeleteDialog() {
  const { isDeleteDialogOpen, categoryToDelete } = useCategoryFormState();
  const { closeAllDialogs } = useCategoryFormActions();
  const deleteCategory = useDeleteCategory();

  const isLoading = deleteCategory.isPending;

  const handleDelete = async () => {
    if (!categoryToDelete) return;

    try {
      await deleteCategory.mutateAsync(categoryToDelete.id);
      closeAllDialogs();
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      closeAllDialogs();
    }
  };

  return (
    <CustomDialog
      open={isDeleteDialogOpen}
      onOpenChange={handleClose}
      
    >
      <CustomDialogContent className="sm:max-w-lg">
        <CustomDialogHeader>
          <CustomDialogTitle>Delete Category</CustomDialogTitle>
          <CustomDialogDescription>
            Are you sure you want to delete "{categoryToDelete?.name}"? This
            action cannot be undone and will remove all translations.
          </CustomDialogDescription>
        </CustomDialogHeader>

        <div className="p-6">
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
            <p className="text-sm text-destructive font-medium">
              Warning: This will permanently delete the category and all its
              translations.
            </p>
          </div>
        </div>

        <CustomDialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading}
            className="text-white"
          >
            {isLoading ? "Deleting..." : "Delete Category"}
          </Button>
        </CustomDialogFooter>
      </CustomDialogContent>
    </CustomDialog>
  );
}
