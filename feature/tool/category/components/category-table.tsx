"use client";

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MoreHorizontal, Plus, Search, Edit, Trash2 } from "lucide-react";
import { useGetCategories } from "@/feature/tool/category/hooks/use-tool-category-data";
import {
  useCategoryListState,
  useCategoryListActions,
  useCategoryFormActions,
} from "@/feature/tool/category/store/use-tool-category-store";
import { CategoryResponse, SuperCategory } from "@/types/tool-api";

export function CategoryTable() {
  const { searchQuery, isActiveFilter, languageFilter, currentPage } =
    useCategoryListState();

  const {
    setSearchQuery,
    setIsActiveFilter,
    setLanguageFilter,
    setCurrentPage,
  } = useCategoryListActions();

  const { openCreateDialog, openEditDialog, openDeleteDialog } =
    useCategoryFormActions();

  // Fetch categories with current filters
  const { data, isLoading, error } = useGetCategories({
    page: currentPage,
    limit: 10,
    search: searchQuery || undefined,
    is_active: isActiveFilter ?? undefined,
    language: languageFilter,
  });

  const categories = data?.items || [];
  const totalPages = data?.pagination?.total_pages || 1;
  const totalItems = data?.pagination?.total || 0;

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1); // Reset to first page on search
  };

  const handleActiveFilterChange = (value: string) => {
    if (value === "all") {
      setIsActiveFilter(null);
    } else {
      setIsActiveFilter(value === "active");
    }
    setCurrentPage(1);
  };

  const handleLanguageChange = (value: string) => {
    setLanguageFilter(value);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? "default" : "secondary"}>
        {isActive ? "Active" : "Inactive"}
      </Badge>
    );
  };

  const getSuperCategoryLabel = (superCategory: SuperCategory) => {
    switch (superCategory) {
      case SuperCategory.WRITING:
        return "Writing";
      case SuperCategory.IMAGE_GENERATION:
        return "Image Generation";
      case SuperCategory.AUDIO:
        return "Audio";
      case SuperCategory.VIDEO_GENERATION:
        return "Video Generation";
      case SuperCategory.SOCIAL_MEDIA:
        return "Social Media";
      default:
        return superCategory;
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-destructive">
          Error loading categories: {error.message}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Categories</h2>
          <p className="text-muted-foreground">
            Manage tool categories and their translations
          </p>
        </div>
        <Button onClick={openCreateDialog} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Category
        </Button>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4 flex-wrap">
        <div className="relative flex-1 min-w-[200px]">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search categories..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select
          value={
            isActiveFilter === null
              ? "all"
              : isActiveFilter
                ? "active"
                : "inactive"
          }
          onValueChange={handleActiveFilterChange}
        >
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>

        <Select value={languageFilter} onValueChange={handleLanguageChange}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Language" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="en">English</SelectItem>
            <SelectItem value="es">Spanish</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[60px]">No</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Super Category</TableHead>
              <TableHead>Slug</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[70px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="h-4 bg-muted rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-muted rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-muted rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-muted rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-muted rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-muted rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-muted rounded animate-pulse" />
                  </TableCell>
                </TableRow>
              ))
            ) : categories.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <p className="text-muted-foreground">No categories found</p>
                    <Button variant="outline" onClick={openCreateDialog}>
                      Create your first category
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              categories.map((category: CategoryResponse, index: number) => {
                // Get the translation for the selected language
                const translation =
                  category.translations?.find(
                    (t) => t.language_code === languageFilter
                  ) || category.translations?.[0]; // Fallback to first translation

                const displayName = translation?.name || category.name;
                const displayDescription =
                  translation?.description || "No description";

                // Truncate description to 30 characters
                const truncatedDescription =
                  displayDescription.length > 30
                    ? displayDescription.substring(0, 30) + "..."
                    : displayDescription;

                return (
                  <TableRow key={category.id}>
                    <TableCell className="text-sm text-muted-foreground">
                      {(currentPage - 1) * 10 + index + 1}
                    </TableCell>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {category.icon_url && (
                          <img
                            src={category.icon_url}
                            alt=""
                            className="h-6 w-6 rounded"
                          />
                        )}
                        <span>{displayName}</span>
                        {category.color && (
                          <div
                            className="w-3 h-3 rounded-full border"
                            style={{ backgroundColor: category.color }}
                          />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {getSuperCategoryLabel(category.super_category)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <code className="text-sm bg-muted px-2 py-1 rounded">
                        {category.slug}
                      </code>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">
                        {truncatedDescription}
                      </span>
                    </TableCell>
                    <TableCell>{getStatusBadge(category.is_active)}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem
                            onClick={() => openEditDialog(category)}
                            className="flex items-center gap-2"
                          >
                            <Edit className="h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => openDeleteDialog(category)}
                            className="flex items-center gap-2 text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {categories.length} of {totalItems} categories
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage <= 1}
            >
              Previous
            </Button>
            <span className="text-sm">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage >= totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
