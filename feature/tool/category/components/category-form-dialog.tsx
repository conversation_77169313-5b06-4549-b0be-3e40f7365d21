"use client";

import React, { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogDescription,
  CustomDialogFooter,
  CustomDialogHeader,
  CustomDialogTitle,
} from "@/components/custom/custom-dialog";
import {
  useCategoryFormState,
  useCategoryFormActions,
} from "@/feature/tool/category/store/use-tool-category-store";
import { SuperCategory } from "@/types/tool-api";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useCreateCategory,
  useUpdateCategory,
} from "@/feature/tool/category/hooks/use-tool-category-data";
import { generateSlug } from "@/lib/slug-utils";

export function CategoryFormDialog() {
  const {
    categoryFormData,
    isCreateDialogOpen,
    isEditDialogOpen,
    categoryToEdit,
  } = useCategoryFormState();

  const { updateCategoryFormField, closeAllDialogs } = useCategoryFormActions();

  const createCategory = useCreateCategory();
  const updateCategory = useUpdateCategory();

  const isOpen = isCreateDialogOpen || isEditDialogOpen;
  const isEdit = isEditDialogOpen && categoryToEdit;
  const isLoading = createCategory.isPending || updateCategory.isPending;

  // Auto-generate slug when name changes (only for create)
  useEffect(() => {
    if (!isEdit && categoryFormData.name) {
      const autoSlug = generateSlug(categoryFormData.name);
      updateCategoryFormField("slug", autoSlug);
    }
  }, [categoryFormData.name, isEdit, updateCategoryFormField]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!categoryFormData.name.trim()) {
      return;
    }

    try {
      if (isEdit && categoryToEdit) {
        await updateCategory.mutateAsync({
          id: categoryToEdit.id,
          data: {
            name: categoryFormData.name.trim(),
            description: categoryFormData.description.trim() || undefined,
            slug: categoryFormData.slug.trim(),
            super_category: categoryFormData.super_category,
            icon_url: categoryFormData.icon_url.trim() || undefined,
            color: categoryFormData.color.trim() || undefined,
            is_active: categoryFormData.is_active,
          },
        });
      } else {
        await createCategory.mutateAsync({
          name: categoryFormData.name.trim(),
          description: categoryFormData.description.trim() || undefined,
          slug: categoryFormData.slug.trim(),
          super_category: categoryFormData.super_category,
          icon_url: categoryFormData.icon_url.trim() || undefined,
          color: categoryFormData.color.trim() || undefined,
          is_active: categoryFormData.is_active,
        });
      }

      closeAllDialogs();
    } catch (error) {
      // Error handling is done in the hooks
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      closeAllDialogs();
    }
  };

  return (
    <CustomDialog open={isOpen} onOpenChange={handleClose}>
      <CustomDialogContent className="sm:max-w-[600px]">
        <CustomDialogHeader>
          <CustomDialogTitle>
            {isEdit ? "Edit Category" : "Create New Category"}
          </CustomDialogTitle>
          <CustomDialogDescription>
            {isEdit
              ? "Update the category information. Changes will be automatically translated."
              : "Create a new category. The information will be automatically translated to Spanish."}
          </CustomDialogDescription>
        </CustomDialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 p-6">
          <div className="space-y-2">
            <Label htmlFor="super_category" className="text-sm font-medium">
              Super Category *
            </Label>
            <Select
              value={categoryFormData.super_category}
              onValueChange={(value) =>
                updateCategoryFormField(
                  "super_category",
                  value as SuperCategory
                )
              }
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select super category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={SuperCategory.WRITING}>Writing</SelectItem>
                <SelectItem value={SuperCategory.IMAGE_GENERATION}>
                  Image Generation
                </SelectItem>
                <SelectItem value={SuperCategory.AUDIO}>Audio</SelectItem>
                <SelectItem value={SuperCategory.VIDEO_GENERATION}>
                  Video Generation
                </SelectItem>
                <SelectItem value={SuperCategory.SOCIAL_MEDIA}>
                  Social Media
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium">
                Name *
              </Label>
              <Input
                id="name"
                type="text"
                placeholder="Enter category name"
                value={categoryFormData.name}
                onChange={(e) =>
                  updateCategoryFormField("name", e.target.value)
                }
                disabled={isLoading}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug" className="text-sm font-medium">
                Slug *
              </Label>
              <Input
                id="slug"
                type="text"
                placeholder="category-slug"
                value={categoryFormData.slug}
                onChange={(e) =>
                  updateCategoryFormField("slug", e.target.value)
                }
                disabled={isLoading}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium">
              Description
            </Label>
            <Textarea
              id="description"
              placeholder="Enter category description"
              value={categoryFormData.description}
              onChange={(e) =>
                updateCategoryFormField("description", e.target.value)
              }
              disabled={isLoading}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="icon_url" className="text-sm font-medium">
                Icon URL
              </Label>
              <Input
                id="icon_url"
                type="url"
                placeholder="https://example.com/icon.svg"
                value={categoryFormData.icon_url}
                onChange={(e) =>
                  updateCategoryFormField("icon_url", e.target.value)
                }
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="color" className="text-sm font-medium">
                Color
              </Label>
              <Input
                id="color"
                type="text"
                placeholder="#3B82F6"
                value={categoryFormData.color}
                onChange={(e) =>
                  updateCategoryFormField("color", e.target.value)
                }
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={categoryFormData.is_active}
              onCheckedChange={(checked) =>
                updateCategoryFormField("is_active", checked)
              }
              disabled={isLoading}
            />
            <Label htmlFor="is_active" className="text-sm font-medium">
              Active
            </Label>
          </div>

          <CustomDialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !categoryFormData.name.trim()}
            >
              {isLoading
                ? "Saving..."
                : isEdit
                  ? "Update Category"
                  : "Create Category"}
            </Button>
          </CustomDialogFooter>
        </form>
      </CustomDialogContent>
    </CustomDialog>
  );
}
