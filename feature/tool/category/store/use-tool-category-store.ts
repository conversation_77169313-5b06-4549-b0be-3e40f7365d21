import { create } from "zustand";
import {
  CategoryResponse,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  SuperCategory,
} from "@/types/tool-api";

// Category form data interfaces
export interface CategoryFormData {
  name: string;
  description: string;
  slug: string;
  super_category: SuperCategory;
  icon_url: string;
  color: string;
  is_active: boolean;
}

// Category store state interface
export interface CategoryState {
  // Categories list state
  categories: CategoryResponse[];
  selectedCategory: CategoryResponse | null;
  totalCategories: number;
  currentPage: number;
  totalPages: number;
  isLoading: boolean;

  // Search and filter state
  searchQuery: string;
  isActiveFilter: boolean | null;
  languageFilter: string;

  // Form state
  categoryFormData: CategoryFormData;
  isCreateDialogOpen: boolean;
  isEditDialogOpen: boolean;
  isDeleteDialogOpen: boolean;
  categoryToEdit: CategoryResponse | null;
  categoryToDelete: CategoryResponse | null;

  // Actions for categories list
  setCategories: (categories: CategoryResponse[]) => void;
  setSelectedCategory: (category: CategoryResponse | null) => void;
  setTotalCategories: (total: number) => void;
  setCurrentPage: (page: number) => void;
  setTotalPages: (pages: number) => void;
  setIsLoading: (loading: boolean) => void;

  // Actions for search and filter
  setSearchQuery: (query: string) => void;
  setIsActiveFilter: (filter: boolean | null) => void;
  setLanguageFilter: (language: string) => void;
  resetFilters: () => void;

  // Actions for form state
  setCategoryFormData: (data: Partial<CategoryFormData>) => void;
  updateCategoryFormField: (
    field: keyof CategoryFormData,
    value: string | boolean
  ) => void;
  resetCategoryForm: () => void;

  // Actions for dialogs
  setIsCreateDialogOpen: (open: boolean) => void;
  setIsEditDialogOpen: (open: boolean) => void;
  setIsDeleteDialogOpen: (open: boolean) => void;
  setCategoryToEdit: (category: CategoryResponse | null) => void;
  setCategoryToDelete: (category: CategoryResponse | null) => void;

  // Helper actions
  openCreateDialog: () => void;
  openEditDialog: (category: CategoryResponse) => void;
  openDeleteDialog: (category: CategoryResponse) => void;
  closeAllDialogs: () => void;

  // Global actions
  resetAllState: () => void;
}

// Initial state values
const initialCategoryFormData: CategoryFormData = {
  name: "",
  description: "",
  slug: "",
  super_category: SuperCategory.WRITING,
  icon_url: "",
  color: "",
  is_active: true,
};

// Create the Zustand store
export const useCategoryStore = create<CategoryState>((set, get) => ({
  // Initial state
  categories: [],
  selectedCategory: null,
  totalCategories: 0,
  currentPage: 1,
  totalPages: 1,
  isLoading: false,

  searchQuery: "",
  isActiveFilter: null,
  languageFilter: "en",

  categoryFormData: initialCategoryFormData,
  isCreateDialogOpen: false,
  isEditDialogOpen: false,
  isDeleteDialogOpen: false,
  categoryToEdit: null,
  categoryToDelete: null,

  // Actions for categories list
  setCategories: (categories) => set({ categories }),
  setSelectedCategory: (category) => set({ selectedCategory: category }),
  setTotalCategories: (total) => set({ totalCategories: total }),
  setCurrentPage: (page) => set({ currentPage: page }),
  setTotalPages: (pages) => set({ totalPages: pages }),
  setIsLoading: (loading) => set({ isLoading: loading }),

  // Actions for search and filter
  setSearchQuery: (query) => set({ searchQuery: query }),
  setIsActiveFilter: (filter) => set({ isActiveFilter: filter }),
  setLanguageFilter: (language) => set({ languageFilter: language }),
  resetFilters: () =>
    set({
      searchQuery: "",
      isActiveFilter: null,
      languageFilter: "en",
      currentPage: 1,
    }),

  // Actions for form state
  setCategoryFormData: (data) =>
    set((state) => ({
      categoryFormData: { ...state.categoryFormData, ...data },
    })),

  updateCategoryFormField: (field, value) =>
    set((state) => ({
      categoryFormData: { ...state.categoryFormData, [field]: value },
    })),

  resetCategoryForm: () => set({ categoryFormData: initialCategoryFormData }),

  // Actions for dialogs
  setIsCreateDialogOpen: (open) => set({ isCreateDialogOpen: open }),
  setIsEditDialogOpen: (open) => set({ isEditDialogOpen: open }),
  setIsDeleteDialogOpen: (open) => set({ isDeleteDialogOpen: open }),
  setCategoryToEdit: (category) => set({ categoryToEdit: category }),
  setCategoryToDelete: (category) => set({ categoryToDelete: category }),

  // Helper actions
  openCreateDialog: () => {
    set({
      isCreateDialogOpen: true,
      categoryFormData: initialCategoryFormData,
    });
  },

  openEditDialog: (category) => {
    set({
      isEditDialogOpen: true,
      categoryToEdit: category,
      categoryFormData: {
        name: category.name,
        description: category.translations?.[0]?.description || "",
        slug: category.slug,
        super_category: category.super_category,
        icon_url: category.icon_url || "",
        color: category.color || "",
        is_active: category.is_active,
      },
    });
  },

  openDeleteDialog: (category) => {
    set({
      isDeleteDialogOpen: true,
      categoryToDelete: category,
    });
  },

  closeAllDialogs: () => {
    set({
      isCreateDialogOpen: false,
      isEditDialogOpen: false,
      isDeleteDialogOpen: false,
      categoryToEdit: null,
      categoryToDelete: null,
    });
  },

  // Global actions
  resetAllState: () =>
    set({
      categories: [],
      selectedCategory: null,
      totalCategories: 0,
      currentPage: 1,
      totalPages: 1,
      isLoading: false,
      searchQuery: "",
      isActiveFilter: null,
      languageFilter: "en",
      categoryFormData: initialCategoryFormData,
      isCreateDialogOpen: false,
      isEditDialogOpen: false,
      isDeleteDialogOpen: false,
      categoryToEdit: null,
      categoryToDelete: null,
    }),
}));

// Selector hooks for specific parts of the state
export const useCategoryListState = () => {
  const {
    categories,
    selectedCategory,
    totalCategories,
    currentPage,
    totalPages,
    isLoading,
    searchQuery,
    isActiveFilter,
    languageFilter,
  } = useCategoryStore();

  return {
    categories,
    selectedCategory,
    totalCategories,
    currentPage,
    totalPages,
    isLoading,
    searchQuery,
    isActiveFilter,
    languageFilter,
  };
};

export const useCategoryListActions = () => {
  const {
    setCategories,
    setSelectedCategory,
    setTotalCategories,
    setCurrentPage,
    setTotalPages,
    setIsLoading,
    setSearchQuery,
    setIsActiveFilter,
    setLanguageFilter,
    resetFilters,
  } = useCategoryStore();

  return {
    setCategories,
    setSelectedCategory,
    setTotalCategories,
    setCurrentPage,
    setTotalPages,
    setIsLoading,
    setSearchQuery,
    setIsActiveFilter,
    setLanguageFilter,
    resetFilters,
  };
};

export const useCategoryFormState = () => {
  const {
    categoryFormData,
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    categoryToEdit,
    categoryToDelete,
  } = useCategoryStore();

  return {
    categoryFormData,
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    categoryToEdit,
    categoryToDelete,
  };
};

export const useCategoryFormActions = () => {
  const {
    setCategoryFormData,
    updateCategoryFormField,
    resetCategoryForm,
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    setIsDeleteDialogOpen,
    setCategoryToEdit,
    setCategoryToDelete,
    openCreateDialog,
    openEditDialog,
    openDeleteDialog,
    closeAllDialogs,
  } = useCategoryStore();

  return {
    setCategoryFormData,
    updateCategoryFormField,
    resetCategoryForm,
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    setIsDeleteDialogOpen,
    setCategoryToEdit,
    setCategoryToDelete,
    openCreateDialog,
    openEditDialog,
    openDeleteDialog,
    closeAllDialogs,
  };
};
