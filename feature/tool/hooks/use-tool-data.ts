import {
  useApiMutation,
  useApiQuery,
  apiPost,
  apiGet,
  apiPut,
  apiDelete,
  ApiError,
} from "@/feature/core/api/api-utils";
import {
  CreateToolRequest,
  UpdateToolRequest,
  ToolResponse,
  PaginatedResponse,
  ToolQueryParams,
} from "@/types/tool-api";
import { toast } from "sonner";

// Tool API functions
const toolApi = {
  getTools: async (
    params?: ToolQueryParams
  ): Promise<PaginatedResponse<ToolResponse>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set("page", params.page.toString());
    if (params?.limit) searchParams.set("limit", params.limit.toString());
    if (params?.search) searchParams.set("search", params.search);
    if (params?.tool_type) searchParams.set("tool_type", params.tool_type);
    if (params?.pricing_model)
      searchParams.set("pricing_model", params.pricing_model);
    if (params?.status) searchParams.set("status", params.status);
    if (params?.is_published !== undefined)
      searchParams.set("is_published", params.is_published.toString());
    if (params?.category_id)
      searchParams.set("category_id", params.category_id.toString());
    if (params?.tag_id) searchParams.set("tag_id", params.tag_id.toString());
    if (params?.language) searchParams.set("language", params.language);

    const queryString = searchParams.toString();
    const response = await apiGet<{
      data: PaginatedResponse<ToolResponse>;
    }>(`/api/tool${queryString ? `?${queryString}` : ""}`);
    return response.data;
  },

  getTool: async (id: string, language?: string): Promise<ToolResponse> => {
    const searchParams = new URLSearchParams();
    if (language) searchParams.set("language", language);

    const queryString = searchParams.toString();
    const response = await apiGet<{ data: ToolResponse }>(
      `/api/tool/${id}${queryString ? `?${queryString}` : ""}`
    );
    return response.data;
  },

  createTool: async (data: CreateToolRequest): Promise<ToolResponse> => {
    const response = await apiPost<{ data: ToolResponse }>("/api/tool", data);
    return response.data;
  },

  updateTool: async (
    id: string,
    data: UpdateToolRequest
  ): Promise<ToolResponse> => {
    const response = await apiPut<{ data: ToolResponse }>(
      `/api/tool/${id}`,
      data
    );
    return response.data;
  },

  deleteTool: async (id: string): Promise<{ id: string }> => {
    const response = await apiDelete<{ data: { id: string } }>(
      `/api/tool/${id}`
    );
    return response.data;
  },
};

// Hook to get tools list
export function useGetTools(params?: ToolQueryParams) {
  return useApiQuery(
    ["tools", JSON.stringify(params)],
    () => toolApi.getTools(params),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

// Hook to get single tool
export function useGetTool(id: string, language?: string) {
  return useApiQuery(
    ["tool", id, language || ""],
    () => toolApi.getTool(id, language),
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Hook to create tool
export function useCreateTool() {
  return useApiMutation(toolApi.createTool, {
    showErrorToast: false,
    onSuccess: (data) => {
      toast.success("Tool created successfully");
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to create tool");
    },
    invalidateQueries: ["tools", "tool"],
  });
}

// Hook to update tool
export function useUpdateTool() {
  return useApiMutation(
    (variables: { id: string; data: UpdateToolRequest }) =>
      toolApi.updateTool(variables.id, variables.data),
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success("Tool updated successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to update tool");
      },
      invalidateQueries: ["tools", "tool"],
    }
  );
}

// Hook to delete tool
export function useDeleteTool() {
  return useApiMutation(toolApi.deleteTool, {
    showErrorToast: false,
    onSuccess: () => {
      toast.success("Tool deleted successfully");
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to delete tool");
    },
    invalidateQueries: ["tools"],
  });
}

// Hook to translate tool to specific language
export function useTranslateTool() {
  return useApiMutation(
    async (data: { toolId: string; language: string }) => {
      return apiPost(`/api/tool/${data.toolId}/translate`, {
        language: data.language,
      });
    },
    {
      showErrorToast: false,
      onSuccess: (data, variables) => {
        toast.success(
          `Tool translated to ${variables.language} successfully!`
        );
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to translate tool");
      },
    }
  );
}

// Hook to get available languages for translation
export function useGetAvailableLanguages(toolId: string) {
  return useApiQuery(
    ["tool-available-languages", toolId],
    async () => {
      const response = await apiGet<{
        data: {
          available_languages: Array<{ code: string; name: string }>;
          existing_languages: string[];
        };
      }>(`/api/tool/${toolId}/translate`);
      return response.data;
    },
    {
      enabled: !!toolId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Custom hook that aggregates all tool-related hooks
export function useToolActions() {
  const createTool = useCreateTool();
  const updateTool = useUpdateTool();
  const deleteTool = useDeleteTool();

  const isLoading =
    createTool.isPending || updateTool.isPending || deleteTool.isPending;

  return {
    createTool,
    updateTool,
    deleteTool,
    isLoading,
  };
}
