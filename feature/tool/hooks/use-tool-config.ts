import { useApiQuery, apiGet } from "@/feature/core/api/api-utils";
import { ToolConfigResponse } from "@/types/tool-api";

// Tool Config API function
const toolConfigApi = {
  getToolConfig: async (language?: string): Promise<ToolConfigResponse> => {
    const searchParams = new URLSearchParams();
    if (language) searchParams.set("language", language);

    const queryString = searchParams.toString();
    const response = await apiGet<{ data: ToolConfigResponse }>(
      `/api/tool/config${queryString ? `?${queryString}` : ""}`
    );
    return response.data;
  },
};

// Hook to get tool configuration
export function useToolConfig(language?: string) {
  return useApiQuery(
    ["tool-config", language || "en"],
    () => toolConfigApi.getToolConfig(language),
    {
      staleTime: 10 * 60 * 1000, // 10 minutes - config data doesn't change often
      cacheTime: 30 * 60 * 1000, // 30 minutes
    }
  );
}
