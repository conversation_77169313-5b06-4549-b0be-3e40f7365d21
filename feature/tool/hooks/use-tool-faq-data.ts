"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useApiQuery, useApiMutation } from "@/feature/core/api/api-utils";
import {
  apiGet,
  apiPost,
  apiPut,
  apiDelete,
} from "@/feature/core/api/api-utils";
import {
  ToolFAQResponse,
  CreateToolFAQRequest,
  UpdateToolFAQRequest,
  ToolFAQQueryParams,
} from "@/types/tool-api";

// Query Keys
export const toolFAQKeys = {
  all: ["tool-faqs"],
  byTool: (toolId: string) => ["tool-faqs", "by-tool", toolId],
  single: (id: string) => ["tool-faqs", "single", id],
};

// Get FAQs for a tool
export function useGetToolFAQs(params: ToolFAQQueryParams) {
  return useApiQuery(
    toolFAQKeys.byTool(params.tool_id),
    async (): Promise<ToolFAQResponse[]> => {
      const queryParams = new URLSearchParams({
        tool_id: params.tool_id,
        ...(params.language && { language: params.language }),
        ...(params.is_active !== undefined && {
          is_active: params.is_active.toString(),
        }),
      });

      return apiGet(`/api/tool/faqs?${queryParams.toString()}`);
    },
    {
      enabled: !!params.tool_id,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Get single FAQ
export function useGetToolFAQ(id: string, language = "en") {
  return useApiQuery(
    toolFAQKeys.single(id),
    async (): Promise<ToolFAQResponse> => {
      return apiGet(`/api/tool/faqs/${id}?language=${language}`);
    },
    {
      enabled: !!id,
    }
  );
}

// Create FAQ
export function useCreateToolFAQ() {
  const queryClient = useQueryClient();

  return useApiMutation(
    async (data: CreateToolFAQRequest): Promise<ToolFAQResponse> => {
      return apiPost("/api/tool/faqs", data);
    },
    {
      showErrorToast: false,
      onSuccess: (data, variables) => {
        toast.success("FAQ created successfully!");

        // Invalidate tool FAQs query
        queryClient.invalidateQueries({
          queryKey: toolFAQKeys.byTool(variables.tool_id),
        });

        // Invalidate tool data to refresh FAQ count
        queryClient.invalidateQueries({
          queryKey: ["tools", "single", variables.tool_id],
        });
      },
      onError: (error) => {
        toast.error(error.message || "Failed to create FAQ");
      },
    }
  );
}

// Update FAQ
export function useUpdateToolFAQ() {
  const queryClient = useQueryClient();

  return useApiMutation(
    async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateToolFAQRequest;
    }): Promise<ToolFAQResponse> => {
      return apiPut(`/api/tool/faqs/${id}`, data);
    },
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success("FAQ updated successfully!");

        // Invalidate related queries
        queryClient.invalidateQueries({
          queryKey: toolFAQKeys.single(data.id),
        });
        queryClient.invalidateQueries({
          queryKey: toolFAQKeys.byTool(data.tool_id),
        });

        // Invalidate tool data
        queryClient.invalidateQueries({
          queryKey: ["tools", "single", data.tool_id],
        });
      },
      onError: (error) => {
        toast.error(error.message || "Failed to update FAQ");
      },
    }
  );
}

// Delete FAQ
export function useDeleteToolFAQ() {
  const queryClient = useQueryClient();

  return useApiMutation(
    async (id: string): Promise<void> => {
      return apiDelete(`/api/tool/faqs/${id}`);
    },
    {
      showErrorToast: false,
      onSuccess: (_, id) => {
        toast.success("FAQ deleted successfully!");

        // Remove from cache
        queryClient.removeQueries({
          queryKey: toolFAQKeys.single(id),
        });

        // Invalidate all FAQ queries
        queryClient.invalidateQueries({
          queryKey: toolFAQKeys.all,
        });
      },
      onError: (error) => {
        toast.error(error.message || "Failed to delete FAQ");
      },
    }
  );
}

// Bulk operations
export function useReorderToolFAQs() {
  const queryClient = useQueryClient();

  return useApiMutation(
    async ({
      toolId,
      faqs,
    }: {
      toolId: string;
      faqs: Array<{ id: string; order: number }>;
    }): Promise<void> => {
      // Update each FAQ's order
      await Promise.all(
        faqs.map(({ id, order }) => apiPut(`/api/tool/faqs/${id}`, { order }))
      );
    },
    {
      showErrorToast: false,
      onSuccess: (_, { toolId }) => {
        toast.success("FAQ order updated successfully!");

        // Invalidate tool FAQs query
        queryClient.invalidateQueries({
          queryKey: toolFAQKeys.byTool(toolId),
        });
      },
      onError: (error) => {
        toast.error(error.message || "Failed to reorder FAQs");
      },
    }
  );
}
