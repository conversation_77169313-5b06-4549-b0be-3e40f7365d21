import {
  useApiMutation,
  useApiQuery,
  apiPost,
  apiGet,
  apiPut,
  apiDelete,
  apiRequest,
  ApiError,
} from "@/feature/core/api/api-utils";
import {
  ReviewResponse,
  CreateReviewRequest,
  UpdateReviewRequest,
  AdminReviewApprovalRequest,
  ReviewQueryParams,
  PaginatedResponse,
  ReviewStatsResponse,
} from "@/types/tool-api";
import { toast } from "sonner";

// Review API functions
const reviewApi = {
  getReviews: async (
    params?: ReviewQueryParams
  ): Promise<PaginatedResponse<ReviewResponse>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set("page", params.page.toString());
    if (params?.limit) searchParams.set("limit", params.limit.toString());
    if (params?.search) searchParams.set("search", params.search);
    if (params?.tool_id) searchParams.set("tool_id", params.tool_id);
    if (params?.user_id) searchParams.set("user_id", params.user_id);
    if (params?.rating) searchParams.set("rating", params.rating.toString());
    if (params?.is_published !== undefined)
      searchParams.set("is_published", params.is_published.toString());

    const queryString = searchParams.toString();
    const response = await apiGet<{
      data: PaginatedResponse<ReviewResponse>;
    }>(`/api/tool/review${queryString ? `?${queryString}` : ""}`);
    return response.data;
  },

  getReviewStats: async (): Promise<ReviewStatsResponse> => {
    const response = await apiGet<{ data: ReviewStatsResponse }>(
      "/api/tool/review?stats=true"
    );
    return response.data;
  },

  getReview: async (id: string): Promise<ReviewResponse> => {
    const response = await apiGet<{ data: ReviewResponse }>(
      `/api/tool/review/${id}`
    );
    return response.data;
  },

  approveReview: async (
    review_id: string,
    is_published: boolean
  ): Promise<ReviewResponse> => {
    const response = await apiPost<{ data: ReviewResponse }>(
      "/api/tool/review",
      { review_id, is_published }
    );
    return response.data;
  },

  updateReview: async (
    id: string,
    data: UpdateReviewRequest & AdminReviewApprovalRequest
  ): Promise<ReviewResponse> => {
    const response = await apiPut<{ data: ReviewResponse }>(
      `/api/tool/review/${id}`,
      data
    );
    return response.data;
  },

  deleteReview: async (id: string): Promise<{ id: string }> => {
    const response = await apiDelete<{ data: { id: string } }>(
      `/api/tool/review/${id}`
    );
    return response.data;
  },

  deleteReviews: async (review_ids: string[]): Promise<{ deleted_count: number }> => {
    const response = await apiRequest<{ data: { deleted_count: number } }>(
      "/api/tool/review",
      {
        method: "DELETE",
        body: JSON.stringify({ review_ids }),
      }
    );
    return response.data;
  },
};

// Public review API functions
const publicReviewApi = {
  getPublicReviews: async (
    params?: {
      tool_id?: string;
      rating?: number;
      page?: number;
      limit?: number;
    }
  ): Promise<PaginatedResponse<ReviewResponse>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set("page", params.page.toString());
    if (params?.limit) searchParams.set("limit", params.limit.toString());
    if (params?.tool_id) searchParams.set("tool_id", params.tool_id);
    if (params?.rating) searchParams.set("rating", params.rating.toString());

    const queryString = searchParams.toString();
    const response = await apiGet<{
      data: PaginatedResponse<ReviewResponse>;
    }>(`/api/v1/tool/review${queryString ? `?${queryString}` : ""}`);
    return response.data;
  },

  createReview: async (data: CreateReviewRequest): Promise<ReviewResponse> => {
    const response = await apiPost<{ data: ReviewResponse }>(
      "/api/v1/tool/review",
      data
    );
    return response.data;
  },
};

// Admin hooks for review management
export function useGetReviews(params?: ReviewQueryParams) {
  return useApiQuery(
    ["admin-reviews", JSON.stringify(params)],
    () => reviewApi.getReviews(params),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

export function useGetReviewStats() {
  return useApiQuery(
    ["review-stats"],
    () => reviewApi.getReviewStats(),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

export function useGetReview(id: string) {
  return useApiQuery(
    ["admin-review", id],
    () => reviewApi.getReview(id),
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

export function useApproveReview() {
  return useApiMutation(
    (variables: { review_id: string; is_published: boolean }) =>
      reviewApi.approveReview(variables.review_id, variables.is_published),
    {
      showErrorToast: false,
      onSuccess: (data, variables) => {
        toast.success(
          `Review ${variables.is_published ? "approved" : "rejected"} successfully`
        );
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to update review");
      },
      invalidateQueries: ["admin-reviews", "review-stats"],
    }
  );
}

export function useUpdateReview() {
  return useApiMutation(
    (variables: {
      id: string;
      data: UpdateReviewRequest & AdminReviewApprovalRequest;
    }) => reviewApi.updateReview(variables.id, variables.data),
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success("Review updated successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to update review");
      },
      invalidateQueries: ["admin-reviews", "admin-review"],
    }
  );
}

export function useDeleteReview() {
  return useApiMutation(reviewApi.deleteReview, {
    showErrorToast: false,
    onSuccess: () => {
      toast.success("Review deleted successfully");
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to delete review");
    },
    invalidateQueries: ["admin-reviews", "review-stats"],
  });
}

export function useDeleteReviews() {
  return useApiMutation(reviewApi.deleteReviews, {
    showErrorToast: false,
    onSuccess: (data) => {
      toast.success(`${data.deleted_count} reviews deleted successfully`);
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to delete reviews");
    },
    invalidateQueries: ["admin-reviews", "review-stats"],
  });
}

// Public hooks for reviews
export function useGetPublicReviews(params?: {
  tool_id?: string;
  rating?: number;
  page?: number;
  limit?: number;
}) {
  return useApiQuery(
    ["public-reviews", JSON.stringify(params)],
    () => publicReviewApi.getPublicReviews(params),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

export function useCreateReview() {
  return useApiMutation(publicReviewApi.createReview, {
    showErrorToast: false,
    onSuccess: (data) => {
      toast.success("Review submitted successfully! It will be published after admin approval.");
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to submit review");
    },
    invalidateQueries: ["public-reviews"],
  });
}

// Custom hook that aggregates all review-related actions
export function useReviewActions() {
  const approveReview = useApproveReview();
  const updateReview = useUpdateReview();
  const deleteReview = useDeleteReview();
  const deleteReviews = useDeleteReviews();

  const isLoading =
    approveReview.isPending ||
    updateReview.isPending ||
    deleteReview.isPending ||
    deleteReviews.isPending;

  return {
    approveReview,
    updateReview,
    deleteReview,
    deleteReviews,
    isLoading,
  };
} 