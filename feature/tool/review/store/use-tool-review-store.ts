import { create } from "zustand";
import { ReviewResponse, CreateReviewRequest, UpdateReviewRequest } from "@/types/tool-api";

// Review form data interfaces
export interface ReviewFormData {
  tool_id: string;
  rating: number;
  content: string;
  is_published: boolean;
}

// Review store state interface
export interface ReviewState {
  // Reviews list state
  reviews: ReviewResponse[];
  selectedReview: ReviewResponse | null;
  totalReviews: number;
  currentPage: number;
  totalPages: number;
  isLoading: boolean;

  // Search and filter state
  searchQuery: string;
  toolFilter: string;
  userFilter: string;
  ratingFilter: number | null;
  isPublishedFilter: boolean | null;

  // Form state
  reviewFormData: ReviewFormData;
  isCreateDialogOpen: boolean;
  isEditDialogOpen: boolean;
  isDeleteDialogOpen: boolean;
  isApprovalDialogOpen: boolean;
  reviewToEdit: ReviewResponse | null;
  reviewToDelete: ReviewResponse | null;
  reviewToApprove: ReviewResponse | null;

  // Bulk operations
  selectedReviewIds: string[];
  isBulkDeleteDialogOpen: boolean;

  // Stats
  reviewStats: {
    totalReviews: number;
    averageRating: number;
    ratingDistribution: Record<number, number>;
    publishedReviews: number;
    pendingReviews: number;
  } | null;

  // Actions for reviews list
  setReviews: (reviews: ReviewResponse[]) => void;
  setSelectedReview: (review: ReviewResponse | null) => void;
  setTotalReviews: (total: number) => void;
  setCurrentPage: (page: number) => void;
  setTotalPages: (pages: number) => void;
  setIsLoading: (loading: boolean) => void;

  // Actions for search and filter
  setSearchQuery: (query: string) => void;
  setToolFilter: (filter: string) => void;
  setUserFilter: (filter: string) => void;
  setRatingFilter: (filter: number | null) => void;
  setIsPublishedFilter: (filter: boolean | null) => void;
  resetFilters: () => void;

  // Actions for form state
  setReviewFormData: (data: Partial<ReviewFormData>) => void;
  updateReviewFormField: (
    field: keyof ReviewFormData,
    value: string | number | boolean
  ) => void;
  resetReviewForm: () => void;

  // Actions for dialogs
  setIsCreateDialogOpen: (open: boolean) => void;
  setIsEditDialogOpen: (open: boolean) => void;
  setIsDeleteDialogOpen: (open: boolean) => void;
  setIsApprovalDialogOpen: (open: boolean) => void;
  setReviewToEdit: (review: ReviewResponse | null) => void;
  setReviewToDelete: (review: ReviewResponse | null) => void;
  setReviewToApprove: (review: ReviewResponse | null) => void;

  // Actions for bulk operations
  setSelectedReviewIds: (ids: string[]) => void;
  toggleReviewSelection: (id: string) => void;
  selectAllReviews: () => void;
  clearReviewSelection: () => void;
  setIsBulkDeleteDialogOpen: (open: boolean) => void;

  // Actions for stats
  setReviewStats: (stats: {
    totalReviews: number;
    averageRating: number;
    ratingDistribution: Record<number, number>;
    publishedReviews: number;
    pendingReviews: number;
  }) => void;

  // Helper actions
  openCreateDialog: () => void;
  openEditDialog: (review: ReviewResponse) => void;
  openDeleteDialog: (review: ReviewResponse) => void;
  openApprovalDialog: (review: ReviewResponse) => void;
  openBulkDeleteDialog: () => void;
  closeAllDialogs: () => void;

  // Global actions
  resetAllState: () => void;
}

// Initial form data
const initialReviewFormData: ReviewFormData = {
  tool_id: "",
  rating: 5,
  content: "",
  is_published: false,
};

// Create store
export const useReviewStore = create<ReviewState>((set, get) => ({
  // Initial state
  reviews: [],
  selectedReview: null,
  totalReviews: 0,
  currentPage: 1,
  totalPages: 1,
  isLoading: false,

  // Initial filter state
  searchQuery: "",
  toolFilter: "",
  userFilter: "",
  ratingFilter: null,
  isPublishedFilter: null,

  // Initial form state
  reviewFormData: initialReviewFormData,
  isCreateDialogOpen: false,
  isEditDialogOpen: false,
  isDeleteDialogOpen: false,
  isApprovalDialogOpen: false,
  reviewToEdit: null,
  reviewToDelete: null,
  reviewToApprove: null,

  // Initial bulk operations state
  selectedReviewIds: [],
  isBulkDeleteDialogOpen: false,

  // Initial stats
  reviewStats: null,

  // Actions for reviews list
  setReviews: (reviews) => set({ reviews }),
  setSelectedReview: (review) => set({ selectedReview: review }),
  setTotalReviews: (total) => set({ totalReviews: total }),
  setCurrentPage: (page) => set({ currentPage: page }),
  setTotalPages: (pages) => set({ totalPages: pages }),
  setIsLoading: (loading) => set({ isLoading: loading }),

  // Actions for search and filter
  setSearchQuery: (query) => set({ searchQuery: query }),
  setToolFilter: (filter) => set({ toolFilter: filter }),
  setUserFilter: (filter) => set({ userFilter: filter }),
  setRatingFilter: (filter) => set({ ratingFilter: filter }),
  setIsPublishedFilter: (filter) => set({ isPublishedFilter: filter }),
  resetFilters: () =>
    set({
      searchQuery: "",
      toolFilter: "",
      userFilter: "",
      ratingFilter: null,
      isPublishedFilter: null,
    }),

  // Actions for form state
  setReviewFormData: (data) =>
    set((state) => ({
      reviewFormData: { ...state.reviewFormData, ...data },
    })),
  updateReviewFormField: (field, value) =>
    set((state) => ({
      reviewFormData: { ...state.reviewFormData, [field]: value },
    })),
  resetReviewForm: () => set({ reviewFormData: initialReviewFormData }),

  // Actions for dialogs
  setIsCreateDialogOpen: (open) => set({ isCreateDialogOpen: open }),
  setIsEditDialogOpen: (open) => set({ isEditDialogOpen: open }),
  setIsDeleteDialogOpen: (open) => set({ isDeleteDialogOpen: open }),
  setIsApprovalDialogOpen: (open) => set({ isApprovalDialogOpen: open }),
  setReviewToEdit: (review) => set({ reviewToEdit: review }),
  setReviewToDelete: (review) => set({ reviewToDelete: review }),
  setReviewToApprove: (review) => set({ reviewToApprove: review }),

  // Actions for bulk operations
  setSelectedReviewIds: (ids) => set({ selectedReviewIds: ids }),
  toggleReviewSelection: (id) =>
    set((state) => ({
      selectedReviewIds: state.selectedReviewIds.includes(id)
        ? state.selectedReviewIds.filter((reviewId) => reviewId !== id)
        : [...state.selectedReviewIds, id],
    })),
  selectAllReviews: () =>
    set((state) => ({
      selectedReviewIds: state.reviews.map((review) => review.id),
    })),
  clearReviewSelection: () => set({ selectedReviewIds: [] }),
  setIsBulkDeleteDialogOpen: (open) => set({ isBulkDeleteDialogOpen: open }),

  // Actions for stats
  setReviewStats: (stats) => set({ reviewStats: stats }),

  // Helper actions
  openCreateDialog: () => {
    set({
      isCreateDialogOpen: true,
      reviewFormData: initialReviewFormData,
    });
  },

  openEditDialog: (review) => {
    set({
      isEditDialogOpen: true,
      reviewToEdit: review,
      reviewFormData: {
        tool_id: review.tool_id,
        rating: review.rating,
        content: review.content || "",
        is_published: review.is_published,
      },
    });
  },

  openDeleteDialog: (review) => {
    set({
      isDeleteDialogOpen: true,
      reviewToDelete: review,
    });
  },

  openApprovalDialog: (review) => {
    set({
      isApprovalDialogOpen: true,
      reviewToApprove: review,
    });
  },

  openBulkDeleteDialog: () => {
    set({
      isBulkDeleteDialogOpen: true,
    });
  },

  closeAllDialogs: () => {
    set({
      isCreateDialogOpen: false,
      isEditDialogOpen: false,
      isDeleteDialogOpen: false,
      isApprovalDialogOpen: false,
      isBulkDeleteDialogOpen: false,
      reviewToEdit: null,
      reviewToDelete: null,
      reviewToApprove: null,
    });
  },

  // Global actions
  resetAllState: () =>
    set({
      reviews: [],
      selectedReview: null,
      totalReviews: 0,
      currentPage: 1,
      totalPages: 1,
      isLoading: false,
      searchQuery: "",
      toolFilter: "",
      userFilter: "",
      ratingFilter: null,
      isPublishedFilter: null,
      reviewFormData: initialReviewFormData,
      isCreateDialogOpen: false,
      isEditDialogOpen: false,
      isDeleteDialogOpen: false,
      isApprovalDialogOpen: false,
      reviewToEdit: null,
      reviewToDelete: null,
      reviewToApprove: null,
      selectedReviewIds: [],
      isBulkDeleteDialogOpen: false,
      reviewStats: null,
    }),
}));

// Selector hooks for specific parts of the state
export const useReviewListState = () => {
  const {
    reviews,
    selectedReview,
    totalReviews,
    currentPage,
    totalPages,
    isLoading,
    searchQuery,
    toolFilter,
    userFilter,
    ratingFilter,
    isPublishedFilter,
    reviewStats,
  } = useReviewStore();

  return {
    reviews,
    selectedReview,
    totalReviews,
    currentPage,
    totalPages,
    isLoading,
    searchQuery,
    toolFilter,
    userFilter,
    ratingFilter,
    isPublishedFilter,
    reviewStats,
  };
};

export const useReviewListActions = () => {
  const {
    setReviews,
    setSelectedReview,
    setTotalReviews,
    setCurrentPage,
    setTotalPages,
    setIsLoading,
    setSearchQuery,
    setToolFilter,
    setUserFilter,
    setRatingFilter,
    setIsPublishedFilter,
    resetFilters,
    setReviewStats,
  } = useReviewStore();

  return {
    setReviews,
    setSelectedReview,
    setTotalReviews,
    setCurrentPage,
    setTotalPages,
    setIsLoading,
    setSearchQuery,
    setToolFilter,
    setUserFilter,
    setRatingFilter,
    setIsPublishedFilter,
    resetFilters,
    setReviewStats,
  };
};

export const useReviewFormState = () => {
  const {
    reviewFormData,
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    isApprovalDialogOpen,
    reviewToEdit,
    reviewToDelete,
    reviewToApprove,
  } = useReviewStore();

  return {
    reviewFormData,
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    isApprovalDialogOpen,
    reviewToEdit,
    reviewToDelete,
    reviewToApprove,
  };
};

export const useReviewFormActions = () => {
  const {
    setReviewFormData,
    updateReviewFormField,
    resetReviewForm,
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    setIsDeleteDialogOpen,
    setIsApprovalDialogOpen,
    setReviewToEdit,
    setReviewToDelete,
    setReviewToApprove,
    openCreateDialog,
    openEditDialog,
    openDeleteDialog,
    openApprovalDialog,
    closeAllDialogs,
  } = useReviewStore();

  return {
    setReviewFormData,
    updateReviewFormField,
    resetReviewForm,
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    setIsDeleteDialogOpen,
    setIsApprovalDialogOpen,
    setReviewToEdit,
    setReviewToDelete,
    setReviewToApprove,
    openCreateDialog,
    openEditDialog,
    openDeleteDialog,
    openApprovalDialog,
    closeAllDialogs,
  };
};

export const useReviewBulkState = () => {
  const {
    selectedReviewIds,
    isBulkDeleteDialogOpen,
  } = useReviewStore();

  return {
    selectedReviewIds,
    isBulkDeleteDialogOpen,
  };
};

export const useReviewBulkActions = () => {
  const {
    setSelectedReviewIds,
    toggleReviewSelection,
    selectAllReviews,
    clearReviewSelection,
    setIsBulkDeleteDialogOpen,
    openBulkDeleteDialog,
  } = useReviewStore();

  return {
    setSelectedReviewIds,
    toggleReviewSelection,
    selectAllReviews,
    clearReviewSelection,
    setIsBulkDeleteDialogOpen,
    openBulkDeleteDialog,
  };
}; 