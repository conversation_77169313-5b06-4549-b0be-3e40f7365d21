"use client";

import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useApproveReview } from "@/feature/tool/review/hooks/use-tool-review-data";
import {
  useReviewFormState,
  useReviewFormActions,
  useReviewBulkState,
  useReviewBulkActions,
} from "@/feature/tool/review/store/use-tool-review-store";
import { toast } from "sonner";

export function ReviewApprovalDialog() {
  const { isApprovalDialogOpen, reviewToApprove } = useReviewFormState();
  const { selectedReviewIds } = useReviewBulkState();
  const { setIsApprovalDialogOpen } = useReviewFormActions();
  const { clearReviewSelection } = useReviewBulkActions();

  const approveReviewMutation = useApproveReview();

  const isMultipleApproval = selectedReviewIds.length > 1;
  const reviewCount = selectedReviewIds.length;

  // Determine if this is an approval or unpublish action
  const isApproval = reviewToApprove ? !reviewToApprove.is_published : true;

  const handleApproval = async () => {
    try {
      if (isMultipleApproval) {
        // For bulk operations, assume we're approving (publishing)
        for (const reviewId of selectedReviewIds) {
          await approveReviewMutation.mutateAsync({
            review_id: reviewId,
            is_published: true,
          });
        }
        toast.success(`Successfully approved ${reviewCount} reviews`);
        clearReviewSelection();
      } else if (reviewToApprove) {
        await approveReviewMutation.mutateAsync({
          review_id: reviewToApprove.id,
          is_published: !reviewToApprove.is_published,
        });
        toast.success(
          isApproval
            ? "Review approved successfully"
            : "Review unpublished successfully"
        );
      }
      setIsApprovalDialogOpen(false);
    } catch (error) {
      console.error("Error updating review(s):", error);
      toast.error(
        isMultipleApproval
          ? "Failed to approve reviews. Please try again."
          : `Failed to ${isApproval ? "approve" : "unpublish"} review. Please try again.`
      );
    }
  };

  const handleClose = () => {
    setIsApprovalDialogOpen(false);
  };

  return (
    <AlertDialog open={isApprovalDialogOpen} onOpenChange={handleClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {isMultipleApproval
              ? "Approve Reviews"
              : isApproval
                ? "Approve Review"
                : "Unpublish Review"}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {isMultipleApproval
              ? `Are you sure you want to approve these ${reviewCount} reviews? They will be visible to all users.`
              : isApproval
                ? "Are you sure you want to approve this review? It will be visible to all users."
                : "Are you sure you want to unpublish this review? It will no longer be visible to users."}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleApproval}
            disabled={approveReviewMutation.isPending}
            className={
              isApproval
                ? "bg-green-600 text-white hover:bg-green-700"
                : "bg-orange-600 text-white hover:bg-orange-700"
            }
          >
            {approveReviewMutation.isPending && (
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            )}
            {isMultipleApproval
              ? "Approve Reviews"
              : isApproval
                ? "Approve Review"
                : "Unpublish Review"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
} 