"use client";

import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useDeleteReview, useDeleteReviews } from "@/feature/tool/review/hooks/use-tool-review-data";
import {
  useReviewFormState,
  useReviewFormActions,
  useReviewBulkState,
  useReviewBulkActions,
} from "@/feature/tool/review/store/use-tool-review-store";
import { toast } from "sonner";

export function ReviewDeleteDialog() {
  const { isDeleteDialogOpen, reviewToDelete } = useReviewFormState();
  const { isBulkDeleteDialogOpen } = useReviewBulkState();
  const { selectedReviewIds } = useReviewBulkState();
  const { setIsDeleteDialogOpen, closeAllDialogs } = useReviewFormActions();
  const { clearReviewSelection, setIsBulkDeleteDialogOpen } = useReviewBulkActions();

  const deleteReviewMutation = useDeleteReview();
  const deleteReviewsMutation = useDeleteReviews();

  const isMultipleDelete = isBulkDeleteDialogOpen;
  const reviewCount = selectedReviewIds.length;

  const handleDelete = async () => {
    try {
      if (isMultipleDelete) {
        await deleteReviewsMutation.mutateAsync(selectedReviewIds);
        toast.success(`Successfully deleted ${reviewCount} reviews`);
        clearReviewSelection();
        setIsBulkDeleteDialogOpen(false);
      } else if (reviewToDelete) {
        await deleteReviewMutation.mutateAsync(reviewToDelete.id);
        toast.success("Review deleted successfully");
        setIsDeleteDialogOpen(false);
      }
    } catch (error) {
      console.error("Error deleting review(s):", error);
      toast.error(
        isMultipleDelete
          ? "Failed to delete reviews. Please try again."
          : "Failed to delete review. Please try again."
      );
    }
  };

  const handleClose = () => {
    if (isMultipleDelete) {
      setIsBulkDeleteDialogOpen(false);
    } else {
      setIsDeleteDialogOpen(false);
    }
  };

  const isOpen = isDeleteDialogOpen || isBulkDeleteDialogOpen;

  return (
    <AlertDialog open={isOpen} onOpenChange={handleClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {isMultipleDelete ? "Delete Reviews" : "Delete Review"}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {isMultipleDelete
              ? `Are you sure you want to delete these ${reviewCount} reviews? This action cannot be undone.`
              : "Are you sure you want to delete this review? This action cannot be undone."}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteReviewMutation.isPending || deleteReviewsMutation.isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {(deleteReviewMutation.isPending || deleteReviewsMutation.isPending) && (
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            )}
            {isMultipleDelete ? "Delete Reviews" : "Delete Review"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
} 