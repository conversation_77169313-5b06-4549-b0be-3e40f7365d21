"use client";

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  MoreHorizontal,
  Search,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Star,
  Eye,
  EyeOff,
  ThumbsUp,
  ThumbsDown,
} from "lucide-react";
import { useGetReviews } from "@/feature/tool/review/hooks/use-tool-review-data";
import {
  useReviewListState,
  useReviewListActions,
  useReviewFormActions,
  useReviewBulkState,
  useReviewBulkActions,
} from "@/feature/tool/review/store/use-tool-review-store";
import { ReviewResponse } from "@/types/tool-api";
import { CustomPagination } from "@/components/custom/custom-pagination";

export function ReviewTable() {
  const {
    searchQuery,
    toolFilter,
    userFilter,
    ratingFilter,
    isPublishedFilter,
    currentPage,
  } = useReviewListState();

  const { setSearchQuery, setCurrentPage } = useReviewListActions();

  const { openEditDialog, openDeleteDialog, openApprovalDialog } =
    useReviewFormActions();

  const { selectedReviewIds } = useReviewBulkState();

  const {
    toggleReviewSelection,
    selectAllReviews,
    clearReviewSelection,
    openBulkDeleteDialog,
  } = useReviewBulkActions();

  // Fetch reviews with current filters
  const { data, isLoading, error } = useGetReviews({
    page: currentPage,
    limit: 10,
    search: searchQuery || undefined,
    tool_id: toolFilter || undefined,
    user_id: userFilter || undefined,
    rating: ratingFilter || undefined,
    is_published: isPublishedFilter ?? undefined,
  });

  const reviews = React.useMemo(() => data?.items || [], [data?.items]);
  const totalPages = data?.pagination?.total_pages || 1;
  const hasNext = data?.pagination?.has_next || false;
  const hasPrev = data?.pagination?.has_prev || false;

  // Calculate if all reviews are selected
  const isAllSelected =
    reviews.length > 0 &&
    selectedReviewIds.length === reviews.length &&
    reviews.every((review) => selectedReviewIds.includes(review.id));

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1); // Reset to first page on search
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getStatusBadge = (isPublished: boolean) => {
    return (
      <Badge variant={isPublished ? "default" : "secondary"}>
        {isPublished ? "Published" : "Pending"}
      </Badge>
    );
  };

  const getRatingStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {Array.from({ length: 5 }).map((_, index) => (
          <Star
            key={index}
            className={`w-4 h-4 ${
              index < rating
                ? "fill-yellow-400 text-yellow-400"
                : "text-gray-300"
            }`}
          />
        ))}
      </div>
    );
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString();
  };

  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  const handleToggleReview = (reviewId: string) => {
    toggleReviewSelection(reviewId);
  };

  const handleToggleAll = () => {
    if (isAllSelected) {
      clearReviewSelection();
    } else {
      selectAllReviews();
    }
  };

  const handleBulkApprove = () => {
    if (selectedReviewIds.length > 0) {
      // For bulk approval, we could create a bulk approval dialog or iterate through selected reviews
      // For now, let's use a simple approach by approving the first selected review as an example
      const firstSelectedReview = reviews.find((r) =>
        selectedReviewIds.includes(r.id)
      );
      if (firstSelectedReview) {
        openApprovalDialog(firstSelectedReview);
      }
    }
  };

  const handleBulkDelete = () => {
    if (selectedReviewIds.length > 0) {
      openBulkDeleteDialog();
    }
  };

  React.useEffect(() => {
    // Clear selection when data changes
    clearReviewSelection();
  }, [reviews]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-destructive">
          Error loading reviews: {error.message}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Reviews</h2>
          <p className="text-muted-foreground">
            Manage and moderate user reviews for tools
          </p>
        </div>
        {selectedReviewIds.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              {selectedReviewIds.length} selected
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleBulkApprove}
              className="flex items-center gap-2"
            >
              <CheckCircle className="h-4 w-4" />
              Approve Selected
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleBulkDelete}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Delete Selected
            </Button>
          </div>
        )}
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4 flex-wrap">
        <div className="relative flex-1 min-w-[200px]">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search reviews..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
        {/* TODO: Add ReviewFilters component */}
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]">
                <Checkbox
                  checked={isAllSelected}
                  onCheckedChange={handleToggleAll}
                />
              </TableHead>
              <TableHead>User</TableHead>
              <TableHead>Tool</TableHead>
              <TableHead>Rating</TableHead>
              <TableHead>Content</TableHead>
              <TableHead className="text-center">Helpful</TableHead>
              <TableHead className="text-center">Unhelpful</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="w-[70px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="h-4 w-4 bg-gray-200 rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </TableCell>
                </TableRow>
              ))
            ) : reviews.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="text-center py-8">
                  <div className="text-muted-foreground">
                    <p className="text-lg font-medium mb-2">No reviews found</p>
                    <p className="text-sm">
                      {searchQuery ||
                      toolFilter ||
                      userFilter ||
                      ratingFilter ||
                      isPublishedFilter !== null
                        ? "Try adjusting your filters to see more results."
                        : "No reviews have been submitted yet."}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              reviews.map((review: ReviewResponse) => (
                <TableRow key={review.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedReviewIds.includes(review.id)}
                      onCheckedChange={() => handleToggleReview(review.id)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={review.user?.image || undefined} />
                        <AvatarFallback>
                          {review.user?.name?.charAt(0) ||
                            review.user?.email?.charAt(0) ||
                            "U"}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">
                          {review.user?.name || "Anonymous"}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {review.user?.email}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {review.tool?.logo_url && (
                        <img
                          src={review.tool.logo_url}
                          alt={review.tool.name}
                          className="w-8 h-8 rounded object-cover"
                        />
                      )}
                      <div>
                        <p className="font-medium">
                          {review.tool?.name || "Unknown Tool"}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {review.tool?.slug}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getRatingStars(review.rating)}
                      <span className="text-sm text-muted-foreground">
                        {review.rating}/5
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="max-w-[200px]">
                    <p className="text-sm" title={review.content || undefined}>
                      {review.content
                        ? truncateText(review.content)
                        : "No content"}
                    </p>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      <ThumbsUp className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">
                        {review.helpful_votes}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      <ThumbsDown className="h-4 w-4 text-red-600" />
                      <span className="text-sm font-medium">
                        {review.unhelpful_votes}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(review.is_published)}
                      {review.is_published ? (
                        <Eye className="h-4 w-4 text-green-600" />
                      ) : (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p>{formatDate(review.created_at)}</p>
                      {review.updated_at !== review.created_at && (
                        <p className="text-muted-foreground text-xs">
                          Updated {formatDate(review.updated_at)}
                        </p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => openEditDialog(review)}
                          className="flex items-center gap-2"
                        >
                          <Edit className="h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => openApprovalDialog(review)}
                          className="flex items-center gap-2"
                        >
                          {review.is_published ? (
                            <>
                              <EyeOff className="h-4 w-4" />
                              Unpublish
                            </>
                          ) : (
                            <>
                              <CheckCircle className="h-4 w-4" />
                              Approve
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => openDeleteDialog(review)}
                          className="flex items-center gap-2 text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <CustomPagination
          currentPage={currentPage}
          totalPages={totalPages}
          hasNext={hasNext}
          hasPrev={hasPrev}
          onPageChange={handlePageChange}
        />
      )}
    </div>
  );
} 