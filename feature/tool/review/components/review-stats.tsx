"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Star, 
  MessageSquare, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  BarChart3
} from "lucide-react";
import { useGetReviewStats } from "@/feature/tool/review/hooks/use-tool-review-data";

export function ReviewStats() {
  const { data: statsData, isLoading, error } = useGetReviewStats();

  const renderStatCard = (
    title: string,
    value: string | number,
    description: string,
    icon: React.ReactNode,
    color: string = "text-blue-600"
  ) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className={color}>{icon}</div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );

  const renderRatingDistribution = () => {
    if (!statsData?.ratingDistribution) return null;

    const maxCount = Math.max(...Object.values(statsData.ratingDistribution));
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Rating Distribution
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {[5, 4, 3, 2, 1].map((rating) => {
            const count = statsData.ratingDistribution[rating] || 0;
            const percentage = maxCount > 0 ? (count / maxCount) * 100 : 0;
            
            return (
              <div key={rating} className="flex items-center space-x-2">
                <div className="flex items-center space-x-1 w-16">
                  <span className="text-sm font-medium">{rating}</span>
                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                </div>
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
                <div className="flex items-center space-x-2 w-16">
                  <span className="text-sm font-medium">{count}</span>
                  <span className="text-xs text-muted-foreground">
                    ({maxCount > 0 ? Math.round((count / maxCount) * 100) : 0}%)
                  </span>
                </div>
              </div>
            );
          })}
        </CardContent>
      </Card>
    );
  };

  const formatAverageRating = (rating: number) => {
    return rating.toFixed(1);
  };

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="text-center text-destructive">
            <p className="text-lg font-medium mb-2">Error Loading Stats</p>
            <p className="text-sm">{error.message}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {isLoading ? (
          Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded animate-pulse mb-2" />
                <div className="h-3 bg-gray-200 rounded animate-pulse" />
              </CardContent>
            </Card>
          ))
        ) : statsData ? (
          <>
            {renderStatCard(
              "Total Reviews",
              statsData.totalReviews,
              "All reviews submitted",
              <MessageSquare className="h-4 w-4" />,
              "text-blue-600"
            )}
            {renderStatCard(
              "Published",
              statsData.publishedReviews,
              "Approved and visible",
              <CheckCircle className="h-4 w-4" />,
              "text-green-600"
            )}
            {renderStatCard(
              "Pending",
              statsData.pendingReviews,
              "Awaiting approval",
              <Clock className="h-4 w-4" />,
              "text-orange-600"
            )}
            {renderStatCard(
              "Average Rating",
              formatAverageRating(statsData.averageRating),
              "Overall user satisfaction",
              <Star className="h-4 w-4" />,
              "text-yellow-600"
            )}
          </>
        ) : (
          Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <CardTitle className="text-sm font-medium">No Data</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">--</div>
                <p className="text-xs text-muted-foreground">Loading...</p>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Rating Distribution */}
      {!isLoading && statsData && renderRatingDistribution()}

      {/* Quick Stats */}
      {!isLoading && statsData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Quick Stats
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {statsData.totalReviews}
                </div>
                <div className="text-sm text-muted-foreground">Total Reviews</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {Math.round((statsData.publishedReviews / statsData.totalReviews) * 100) || 0}%
                </div>
                <div className="text-sm text-muted-foreground">Approval Rate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {formatAverageRating(statsData.averageRating)}
                </div>
                <div className="text-sm text-muted-foreground">Avg Rating</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {statsData.pendingReviews}
                </div>
                <div className="text-sm text-muted-foreground">Pending</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 