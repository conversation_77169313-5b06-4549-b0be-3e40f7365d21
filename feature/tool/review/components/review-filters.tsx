"use client";

import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import {
  useReviewListState,
  useReviewListActions,
} from "@/feature/tool/review/store/use-tool-review-store";
import { useGetTools } from "@/feature/tool/hooks/use-tool-data";

export function ReviewFilters() {
  const {
    toolFilter,
    userFilter,
    ratingFilter,
    isPublishedFilter,
  } = useReviewListState();

  const {
    setToolFilter,
    setUserFilter,
    setRatingFilter,
    setIsPublishedFilter,
    setCurrentPage,
  } = useReviewListActions();

  // Fetch tools for the tool filter
  const { data: toolsData } = useGetTools({ limit: 100 });
  const tools = toolsData?.items || [];

  const handleToolChange = (value: string) => {
    setToolFilter(value === "all" ? "" : value);
    setCurrentPage(1);
  };

  const handleRatingChange = (value: string) => {
    setRatingFilter(value === "all" ? null : parseInt(value));
    setCurrentPage(1);
  };

  const handleStatusChange = (value: string) => {
    if (value === "all") {
      setIsPublishedFilter(null);
    } else {
      setIsPublishedFilter(value === "published");
    }
    setCurrentPage(1);
  };

  const handleUserChange = (value: string) => {
    setUserFilter(value || "");
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setToolFilter("");
    setUserFilter("");
    setRatingFilter(null);
    setIsPublishedFilter(null);
    setCurrentPage(1);
  };

  const hasActiveFilters = toolFilter || userFilter || ratingFilter || isPublishedFilter !== null;

  return (
    <div className="flex items-center gap-4 flex-wrap">
      {/* Tool Filter */}
      <div className="min-w-[160px]">
        <Select
          value={toolFilter || "all"}
          onValueChange={handleToolChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="All Tools" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Tools</SelectItem>
            {tools.map((tool) => (
              <SelectItem key={tool.id} value={tool.id}>
                {tool.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Rating Filter */}
      <div className="min-w-[140px]">
        <Select
          value={ratingFilter?.toString() || "all"}
          onValueChange={handleRatingChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="All Ratings" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Ratings</SelectItem>
            <SelectItem value="5">5 Stars</SelectItem>
            <SelectItem value="4">4 Stars</SelectItem>
            <SelectItem value="3">3 Stars</SelectItem>
            <SelectItem value="2">2 Stars</SelectItem>
            <SelectItem value="1">1 Star</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Status Filter */}
      <div className="min-w-[140px]">
        <Select
          value={
            isPublishedFilter === null
              ? "all"
              : isPublishedFilter
                ? "published"
                : "pending"
          }
          onValueChange={handleStatusChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="All Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="published">Published</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* User Filter */}
      <div className="min-w-[160px]">
        <div className="space-y-1">
          <Label htmlFor="user-filter" className="text-sm">
            User Email
          </Label>
          <Input
            id="user-filter"
            placeholder="Filter by user..."
            value={userFilter || ""}
            onChange={(e) => handleUserChange(e.target.value)}
            className="h-9"
          />
        </div>
      </div>

      {/* Clear Filters */}
      {hasActiveFilters && (
        <Button
          variant="outline"
          size="sm"
          onClick={clearFilters}
          className="flex items-center gap-2"
        >
          <X className="h-4 w-4" />
          Clear
        </Button>
      )}
    </div>
  );
} 