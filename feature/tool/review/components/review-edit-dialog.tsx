"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Star } from "lucide-react";
import { useUpdateReview } from "@/feature/tool/review/hooks/use-tool-review-data";
import {
  useReviewFormState,
  useReviewFormActions,
} from "@/feature/tool/review/store/use-tool-review-store";
import { toast } from "sonner";

export function ReviewEditDialog() {
  const { isEditDialogOpen, reviewToEdit } = useReviewFormState();
  const { setIsEditDialogOpen } = useReviewFormActions();
  const updateReviewMutation = useUpdateReview();

  const [formData, setFormData] = React.useState({
    rating: 5,
    content: "",
    is_published: false,
  });

  // Initialize form data when dialog opens
  React.useEffect(() => {
    if (reviewToEdit) {
      setFormData({
        rating: reviewToEdit.rating,
        content: reviewToEdit.content || "",
        is_published: reviewToEdit.is_published,
      });
    }
  }, [reviewToEdit]);

  const handleClose = () => {
    setIsEditDialogOpen(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!reviewToEdit) return;

    try {
      await updateReviewMutation.mutateAsync({
        id: reviewToEdit.id,
        data: {
          rating: formData.rating,
          content: formData.content,
          is_published: formData.is_published,
        },
      });
      
      toast.success("Review updated successfully");
      handleClose();
    } catch (error) {
      console.error("Error updating review:", error);
      toast.error("Failed to update review. Please try again.");
    }
  };

  const handleRatingChange = (rating: number) => {
    setFormData(prev => ({ ...prev, rating }));
  };

  const handleContentChange = (content: string) => {
    setFormData(prev => ({ ...prev, content }));
  };

  const handlePublishedToggle = () => {
    setFormData(prev => ({ ...prev, is_published: !prev.is_published }));
  };

  const renderRatingStars = () => {
    return (
      <div className="flex items-center gap-1">
        {Array.from({ length: 5 }).map((_, index) => (
          <button
            key={index}
            type="button"
            onClick={() => handleRatingChange(index + 1)}
            className="focus:outline-none"
          >
            <Star
              className={`w-6 h-6 transition-colors ${
                index < formData.rating
                  ? "fill-yellow-400 text-yellow-400"
                  : "text-gray-300 hover:text-yellow-400"
              }`}
            />
          </button>
        ))}
        <span className="ml-2 text-sm text-muted-foreground">
          {formData.rating}/5
        </span>
      </div>
    );
  };

  if (!reviewToEdit) return null;

  return (
    <Dialog open={isEditDialogOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Review</DialogTitle>
          <DialogDescription>
            Edit the review details and publication status.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Review Info */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Tool</Label>
                <p className="text-sm text-muted-foreground">
                  {reviewToEdit.tool?.name || "Unknown Tool"}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">User</Label>
                <p className="text-sm text-muted-foreground">
                  {reviewToEdit.user?.name || reviewToEdit.user?.email || "Anonymous"}
                </p>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Created</Label>
              <p className="text-sm text-muted-foreground">
                {new Date(reviewToEdit.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>

          {/* Rating */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Rating *</Label>
            {renderRatingStars()}
          </div>

          {/* Content */}
          <div className="space-y-2">
            <Label htmlFor="content" className="text-sm font-medium">
              Review Content
            </Label>
            <Textarea
              id="content"
              placeholder="Review content..."
              value={formData.content}
              onChange={(e) => handleContentChange(e.target.value)}
              rows={4}
              className="resize-none"
            />
          </div>

          {/* Publication Status */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Publication Status</Label>
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={handlePublishedToggle}
                className="flex items-center gap-2"
              >
                <Badge variant={formData.is_published ? "default" : "secondary"}>
                  {formData.is_published ? "Published" : "Unpublished"}
                </Badge>
              </button>
              <span className="text-sm text-muted-foreground">
                Click to toggle publication status
              </span>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={updateReviewMutation.isPending}
            >
              {updateReviewMutation.isPending && (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              )}
              Update Review
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
} 