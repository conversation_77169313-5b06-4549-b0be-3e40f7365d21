"use client";

import React, { useEffect } from "react";
import { BlogTagTable } from "./blog-tag-table";
import { BlogTagFormDialog } from "./blog-tag-form-dialog";
import { BlogTagDeleteDialog } from "./blog-tag-delete-dialog";
import {
  useBlogTagListState,
  useBlogTagListActions,
} from "@/feature/blog/tag/store/use-blog-tag-store";
import { useGetBlogTags } from "@/feature/blog/tag/hooks/use-blog-tag-data";

export function BlogTagManagement() {
  const { currentPage, searchQuery } = useBlogTagListState();
  const { setTags, setTotalTags, setTotalPages, setIsLoading } =
    useBlogTagListActions();

  const { data: tagsData, isLoading } = useGetBlogTags({
    page: currentPage,
    limit: 20,
    search: searchQuery || undefined,
  });

  useEffect(() => {
    setIsLoading(isLoading);
  }, [isLoading, setIsLoading]);

  useEffect(() => {
    if (tagsData) {
      setTags(tagsData.items);
      setTotalTags(tagsData.pagination.total);
      setTotalPages(tagsData.pagination.total_pages);
    }
  }, [tagsData, setTags, setTotalTags, setTotalPages]);

  return (
    <div className="space-y-6">
      <BlogTagTable />
      <BlogTagFormDialog />
      <BlogTagDeleteDialog />
    </div>
  );
}
