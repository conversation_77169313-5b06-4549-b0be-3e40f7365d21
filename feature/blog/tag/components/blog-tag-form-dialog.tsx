"use client";

import React, { useEffect } from "react";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogDescription,
  CustomDialogFooter,
} from "@/components/custom/custom-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import {
  useBlogTagFormState,
  useBlogTagFormActions,
} from "@/feature/blog/tag/store/use-blog-tag-store";
import {
  useCreateBlogTag,
  useUpdateBlogTag,
} from "@/feature/blog/tag/hooks/use-blog-tag-data";
import { generateSlug } from "@/lib/slug-utils";

export function BlogTagFormDialog() {
  const { isCreateDialogOpen, isEditDialogOpen, tagFormData, tagToEdit } =
    useBlogTagFormState();

  const {
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    updateTagFormField,
    resetTagForm,
    closeAllDialogs,
  } = useBlogTagFormActions();

  const createTagMutation = useCreateBlogTag();
  const updateTagMutation = useUpdateBlogTag();

  const isOpen = isCreateDialogOpen || isEditDialogOpen;
  const isEditing = isEditDialogOpen && tagToEdit;
  const isLoading = createTagMutation.isPending || updateTagMutation.isPending;

  useEffect(() => {
    if (isEditing && tagToEdit) {
      updateTagFormField("name", tagToEdit.name);
      updateTagFormField("description", tagToEdit.description || "");
      updateTagFormField("slug", tagToEdit.slug);
    } else if (isCreateDialogOpen) {
      resetTagForm();
    }
  }, [
    isEditing,
    tagToEdit,
    isCreateDialogOpen,
    updateTagFormField,
    resetTagForm,
  ]);

  // Auto-generate slug from name
  const handleNameChange = (value: string) => {
    updateTagFormField("name", value);
    if (!isEditing && value) {
      const slug = generateSlug(value);
      updateTagFormField("slug", slug);
    }
  };

  const handleClose = () => {
    if (isLoading) return;

    closeAllDialogs();
    resetTagForm();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isLoading) return;

    // Basic validation
    if (!tagFormData.name.trim()) {
      toast.error("Please enter a tag name");
      return;
    }

    const tagData = {
      name: tagFormData.name.trim(),
      description: tagFormData.description.trim() || undefined,
      slug: tagFormData.slug.trim() || undefined,
    };

    try {
      if (isEditing && tagToEdit) {
        await updateTagMutation.mutateAsync({
          id: tagToEdit.id,
          data: tagData,
        });
        // Toast is handled by the hook
      } else {
        await createTagMutation.mutateAsync(tagData);
        // Toast is handled by the hook
      }
      handleClose();
    } catch (error: any) {
      // Error toast is handled by the hook
      console.error("Form submission error:", error);
    }
  };

  return (
    <CustomDialog open={isOpen} onOpenChange={handleClose}>
      <CustomDialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <CustomDialogHeader>
            <CustomDialogTitle>
              {isEditing ? "Edit Tag" : "Create New Tag"}
            </CustomDialogTitle>
            <CustomDialogDescription className="p-0">
              {isEditing
                ? "Update the tag information below."
                : "Create a new blog tag to categorize your content."}
            </CustomDialogDescription>
          </CustomDialogHeader>

          <div className="grid gap-6 p-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                value={tagFormData.name}
                onChange={(e) => handleNameChange(e.target.value)}
                placeholder="Enter tag name"
                disabled={isLoading}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                value={tagFormData.slug}
                onChange={(e) => updateTagFormField("slug", e.target.value)}
                placeholder="auto-generated"
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={tagFormData.description}
                onChange={(e) =>
                  updateTagFormField("description", e.target.value)
                }
                placeholder="Enter tag description (optional)"
                disabled={isLoading}
                rows={3}
              />
            </div>
          </div>

          <CustomDialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? "Update" : "Create"}
            </Button>
          </CustomDialogFooter>
        </form>
      </CustomDialogContent>
    </CustomDialog>
  );
}
