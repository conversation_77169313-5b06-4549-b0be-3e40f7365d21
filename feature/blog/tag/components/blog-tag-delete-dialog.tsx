"use client";

import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import {
  useBlogTagFormState,
  useBlogTagFormActions,
} from "@/feature/blog/tag/store/use-blog-tag-store";
import { useDeleteBlogTag } from "@/feature/blog/tag/hooks/use-blog-tag-data";

export function BlogTagDeleteDialog() {
  const { isDeleteDialogOpen, tagToDelete } = useBlogTagFormState();
  const { closeAllDialogs } = useBlogTagFormActions();
  const deleteTagMutation = useDeleteBlogTag();

  const isLoading = deleteTagMutation.isPending;

  const handleClose = () => {
    if (isLoading) return;
    closeAllDialogs();
  };

  const handleDelete = async () => {
    if (!tagToDelete || isLoading) return;

    try {
      await deleteTagMutation.mutateAsync(tagToDelete.id);
      // Toast is handled by the hook
      handleClose();
    } catch (error: any) {
      // Error toast is handled by the hook
      console.error("Delete error:", error);
    }
  };

  return (
    <AlertDialog open={isDeleteDialogOpen} onOpenChange={handleClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Tag</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the tag{" "}
            <strong>"{tagToDelete?.name}"</strong>?
            <br />
            <br />
            This action cannot be undone. The tag will be permanently removed
            from the system.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel asChild>
            <Button
              variant="outline"
              disabled={isLoading}
              onClick={handleClose}
            >
              Cancel
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isLoading}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete Tag
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
