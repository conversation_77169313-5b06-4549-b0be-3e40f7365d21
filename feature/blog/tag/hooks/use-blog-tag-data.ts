import {
  useApiMutation,
  useApiQuery,
  apiPost,
  apiGet,
  apiPut,
  apiDelete,
  ApiError,
} from "@/feature/core/api/api-utils";
import {
  CreateBlogTagRequest,
  UpdateBlogTagRequest,
  BlogTagResponse,
  PaginatedResponse,
  ListQueryParams,
} from "@/types/blog-api";
import { toast } from "sonner";

// Blog Tag API functions
const blogTagApi = {
  getTags: async (
    params?: ListQueryParams
  ): Promise<PaginatedResponse<BlogTagResponse>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set("page", params.page.toString());
    if (params?.limit) searchParams.set("limit", params.limit.toString());
    if (params?.search) searchParams.set("search", params.search);

    const queryString = searchParams.toString();
    const response = await apiGet<{ data: PaginatedResponse<BlogTagResponse> }>(
      `/api/blog/tags${queryString ? `?${queryString}` : ""}`
    );
    return response.data;
  },

  getTag: async (id: number): Promise<BlogTagResponse> => {
    const response = await apiGet<{ data: BlogTagResponse }>(
      `/api/blog/tags/${id}`
    );
    return response.data;
  },

  createTag: async (data: CreateBlogTagRequest): Promise<BlogTagResponse> => {
    const response = await apiPost<{ data: BlogTagResponse }>(
      "/api/blog/tags",
      data
    );
    return response.data;
  },

  updateTag: async (
    id: number,
    data: UpdateBlogTagRequest
  ): Promise<BlogTagResponse> => {
    const response = await apiPut<{ data: BlogTagResponse }>(
      `/api/blog/tags/${id}`,
      data
    );
    return response.data;
  },

  deleteTag: async (id: number): Promise<{ id: number }> => {
    const response = await apiDelete<{ data: { id: number } }>(
      `/api/blog/tags/${id}`
    );
    return response.data;
  },
};

// Hook to get tags list
export function useGetBlogTags(params?: ListQueryParams) {
  return useApiQuery(
    ["blog-tags", JSON.stringify(params)],
    () => blogTagApi.getTags(params),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

// Hook to get single tag
export function useGetBlogTag(id: number) {
  return useApiQuery(["blog-tag", id.toString()], () => blogTagApi.getTag(id), {
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to create tag
export function useCreateBlogTag() {
  return useApiMutation(blogTagApi.createTag, {
    showErrorToast: false,
    onSuccess: (data) => {
      toast.success("Blog tag created successfully");
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to create blog tag");
    },
    invalidateQueries: ["blog-tags"],
  });
}

// Hook to update tag
export function useUpdateBlogTag() {
  return useApiMutation(
    (variables: { id: number; data: UpdateBlogTagRequest }) =>
      blogTagApi.updateTag(variables.id, variables.data),
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success("Blog tag updated successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to update blog tag");
      },
      invalidateQueries: ["blog-tags", "blog-tag"],
    }
  );
}

// Hook to delete tag
export function useDeleteBlogTag() {
  return useApiMutation(blogTagApi.deleteTag, {
    showErrorToast: false,
    onSuccess: () => {
      toast.success("Blog tag deleted successfully");
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to delete blog tag");
    },
    invalidateQueries: ["blog-tags"],
  });
}

// Custom hook that aggregates all tag-related hooks
export function useBlogTagActions() {
  const createTag = useCreateBlogTag();
  const updateTag = useUpdateBlogTag();
  const deleteTag = useDeleteBlogTag();

  const isLoading =
    createTag.isPending || updateTag.isPending || deleteTag.isPending;

  return {
    createTag,
    updateTag,
    deleteTag,
    isLoading,
  };
}
