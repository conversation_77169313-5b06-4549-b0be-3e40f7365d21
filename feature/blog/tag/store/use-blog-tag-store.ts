import { create } from "zustand";
import {
  BlogTagResponse,
  CreateBlogTagRequest,
  UpdateBlogTagRequest,
} from "@/types/blog-api";

// Blog tag form data interfaces
export interface BlogTagFormData {
  name: string;
  description: string;
  slug: string;
}

// Initial form data
const initialBlogTagFormData: BlogTagFormData = {
  name: "",
  description: "",
  slug: "",
};

// Store interface
interface BlogTagStore {
  // List state
  tags: BlogTagResponse[];
  selectedTag: BlogTagResponse | null;
  totalTags: number;
  currentPage: number;
  totalPages: number;
  isLoading: boolean;
  searchQuery: string;

  // Form state
  tagFormData: BlogTagFormData;
  isCreateDialogOpen: boolean;
  isEditDialogOpen: boolean;
  isDeleteDialogOpen: boolean;
  tagToEdit: BlogTagResponse | null;
  tagToDelete: BlogTagResponse | null;

  // List actions
  setTags: (tags: BlogTagResponse[]) => void;
  setSelectedTag: (tag: BlogTagResponse | null) => void;
  setTotalTags: (total: number) => void;
  setCurrentPage: (page: number) => void;
  setTotalPages: (pages: number) => void;
  setIsLoading: (loading: boolean) => void;
  setSearchQuery: (query: string) => void;
  resetFilters: () => void;

  // Form actions
  setTagFormData: (data: BlogTagFormData) => void;
  updateTagFormField: (field: keyof BlogTagFormData, value: string) => void;
  resetTagForm: () => void;
  setIsCreateDialogOpen: (open: boolean) => void;
  setIsEditDialogOpen: (open: boolean) => void;
  setIsDeleteDialogOpen: (open: boolean) => void;
  setTagToEdit: (tag: BlogTagResponse | null) => void;
  setTagToDelete: (tag: BlogTagResponse | null) => void;

  // Helper actions
  openCreateDialog: () => void;
  openEditDialog: (tag: BlogTagResponse) => void;
  openDeleteDialog: (tag: BlogTagResponse) => void;
  closeAllDialogs: () => void;

  // Global actions
  resetAllState: () => void;
}

// Create the store
export const useBlogTagStore = create<BlogTagStore>((set) => ({
  // Initial state
  tags: [],
  selectedTag: null,
  totalTags: 0,
  currentPage: 1,
  totalPages: 1,
  isLoading: false,
  searchQuery: "",
  tagFormData: initialBlogTagFormData,
  isCreateDialogOpen: false,
  isEditDialogOpen: false,
  isDeleteDialogOpen: false,
  tagToEdit: null,
  tagToDelete: null,

  // List actions
  setTags: (tags) => set({ tags }),
  setSelectedTag: (tag) => set({ selectedTag: tag }),
  setTotalTags: (total) => set({ totalTags: total }),
  setCurrentPage: (page) => set({ currentPage: page }),
  setTotalPages: (pages) => set({ totalPages: pages }),
  setIsLoading: (loading) => set({ isLoading: loading }),
  setSearchQuery: (query) => set({ searchQuery: query }),
  resetFilters: () =>
    set({
      searchQuery: "",
      currentPage: 1,
    }),

  // Form actions
  setTagFormData: (data) => set({ tagFormData: data }),
  updateTagFormField: (field, value) =>
    set((state) => ({
      tagFormData: {
        ...state.tagFormData,
        [field]: value,
      },
    })),
  resetTagForm: () => set({ tagFormData: initialBlogTagFormData }),
  setIsCreateDialogOpen: (open) => set({ isCreateDialogOpen: open }),
  setIsEditDialogOpen: (open) => set({ isEditDialogOpen: open }),
  setIsDeleteDialogOpen: (open) => set({ isDeleteDialogOpen: open }),
  setTagToEdit: (tag) => set({ tagToEdit: tag }),
  setTagToDelete: (tag) => set({ tagToDelete: tag }),

  // Helper actions
  openCreateDialog: () => {
    set({
      isCreateDialogOpen: true,
      tagFormData: initialBlogTagFormData,
    });
  },

  openEditDialog: (tag) => {
    set({
      isEditDialogOpen: true,
      tagToEdit: tag,
      tagFormData: {
        name: tag.name,
        description: tag.description || "",
        slug: tag.slug,
      },
    });
  },

  openDeleteDialog: (tag) => {
    set({
      isDeleteDialogOpen: true,
      tagToDelete: tag,
    });
  },

  closeAllDialogs: () => {
    set({
      isCreateDialogOpen: false,
      isEditDialogOpen: false,
      isDeleteDialogOpen: false,
      tagToEdit: null,
      tagToDelete: null,
    });
  },

  // Global actions
  resetAllState: () =>
    set({
      tags: [],
      selectedTag: null,
      totalTags: 0,
      currentPage: 1,
      totalPages: 1,
      isLoading: false,
      searchQuery: "",
      tagFormData: initialBlogTagFormData,
      isCreateDialogOpen: false,
      isEditDialogOpen: false,
      isDeleteDialogOpen: false,
      tagToEdit: null,
      tagToDelete: null,
    }),
}));

// Selector hooks for specific parts of the state
export const useBlogTagListState = () => {
  const {
    tags,
    selectedTag,
    totalTags,
    currentPage,
    totalPages,
    isLoading,
    searchQuery,
  } = useBlogTagStore();

  return {
    tags,
    selectedTag,
    totalTags,
    currentPage,
    totalPages,
    isLoading,
    searchQuery,
  };
};

export const useBlogTagListActions = () => {
  const {
    setTags,
    setSelectedTag,
    setTotalTags,
    setCurrentPage,
    setTotalPages,
    setIsLoading,
    setSearchQuery,
    resetFilters,
  } = useBlogTagStore();

  return {
    setTags,
    setSelectedTag,
    setTotalTags,
    setCurrentPage,
    setTotalPages,
    setIsLoading,
    setSearchQuery,
    resetFilters,
  };
};

export const useBlogTagFormState = () => {
  const {
    tagFormData,
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    tagToEdit,
    tagToDelete,
  } = useBlogTagStore();

  return {
    tagFormData,
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    tagToEdit,
    tagToDelete,
  };
};

export const useBlogTagFormActions = () => {
  const {
    setTagFormData,
    updateTagFormField,
    resetTagForm,
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    setIsDeleteDialogOpen,
    setTagToEdit,
    setTagToDelete,
    openCreateDialog,
    openEditDialog,
    openDeleteDialog,
    closeAllDialogs,
  } = useBlogTagStore();

  return {
    setTagFormData,
    updateTagFormField,
    resetTagForm,
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    setIsDeleteDialogOpen,
    setTagToEdit,
    setTagToDelete,
    openCreateDialog,
    openEditDialog,
    openDeleteDialog,
    closeAllDialogs,
  };
};
