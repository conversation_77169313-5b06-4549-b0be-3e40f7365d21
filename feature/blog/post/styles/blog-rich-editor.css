/* Blog Rich Editor Styles */

/* Base Editor Styles */
.blog-rich-editor-wrapper {
  width: 100%;
  height: 100%;
  min-height: inherit;
  cursor: text;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.blog-rich-editor-wrapper .ProseMirror {
  outline: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100%;
  min-height: inherit;
  font-size: 16px;
  line-height: 1.7;
  color: var(--foreground);
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    <PERSON>l,
    "Noto Sans",
    sans-serif;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.blog-rich-editor-prosemirror {
  outline: none !important;
  border: none !important;
  font-size: 16px;
  line-height: 1.7;
  color: var(--foreground);
  min-height: inherit;
  padding: 1.5rem;
  margin: 0;
  width: 100%;
  height: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Placeholder styling */
.blog-rich-editor-prosemirror:empty:before {
  content: attr(data-placeholder);
  float: left;
  color: rgb(156 163 175);
  pointer-events: none;
  height: 0;
  font-style: italic;
  opacity: 0.6;
}

/* Fixed Toolbar Container */
.blog-rich-editor-toolbar {
  position: sticky;
  top: 0;
  z-index: 50;  
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  /* box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); */
}

.dark .blog-rich-editor-toolbar {
  background: rgb(17 24 39);
  border-color: rgb(55 65 81);
}

/* Toolbar Header */
.blog-rich-editor-toolbar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--border);
  background: rgb(249 250 251);
  border-radius: 0.5rem 0.5rem 0 0;
}

.dark .blog-rich-editor-toolbar-header {
  background: rgb(31 41 55);
  border-bottom-color: rgb(55 65 81);
}

/* Toolbar Buttons Container */
.blog-rich-editor-toolbar-buttons {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem;
  background: white;
  overflow-x: auto;
  border-radius: 0.5rem;
}

.dark .blog-rich-editor-toolbar-buttons {
  background: rgb(17 24 39);
}

/* HEADINGS - Maximum specificity targeting */
.blog-rich-editor-wrapper .ProseMirror h1,
.blog-rich-editor-prosemirror h1 {
  font-size: 2.5rem !important;
  font-weight: 800 !important;
  line-height: 1.2 !important;
  margin: 2rem 0 1.5rem 0 !important;
  color: var(--foreground) !important;
  letter-spacing: -0.03em !important;
  display: block !important;
}

.blog-rich-editor-wrapper .ProseMirror h2,
.blog-rich-editor-prosemirror h2 {
  font-size: 2rem !important;
  font-weight: 700 !important;
  line-height: 1.3 !important;
  margin: 1.75rem 0 1.25rem 0 !important;
  color: var(--foreground) !important;
  letter-spacing: -0.02em !important;
  display: block !important;
}

.blog-rich-editor-wrapper .ProseMirror h3,
.blog-rich-editor-prosemirror h3 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin: 1.5rem 0 1rem 0 !important;
  color: var(--foreground) !important;
  letter-spacing: -0.01em !important;
  display: block !important;
}

/* PARAGRAPHS */
.blog-rich-editor-wrapper .ProseMirror p,
.blog-rich-editor-prosemirror p {
  margin: 1rem 0 !important;
  line-height: 1.7 !important;
  color: var(--foreground) !important;
  font-size: 16px !important;
}

/* TEXT FORMATTING */
.blog-rich-editor-wrapper .ProseMirror strong,
.blog-rich-editor-prosemirror strong {
  font-weight: 700 !important;
}

.blog-rich-editor-wrapper .ProseMirror em,
.blog-rich-editor-prosemirror em {
  font-style: italic !important;
}

.blog-rich-editor-wrapper .ProseMirror u,
.blog-rich-editor-prosemirror u {
  text-decoration: underline !important;
  text-underline-offset: 2px !important;
}

.blog-rich-editor-wrapper .ProseMirror code,
.blog-rich-editor-prosemirror code {
  background-color: rgb(243 244 246) !important;
  color: var(--primary) !important;
  padding: 0.2rem 0.4rem !important;
  border-radius: 0.375rem !important;
  font-family:
    ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo,
    monospace !important;
  font-size: 0.875em !important;
  font-weight: 500 !important;
}

.dark .blog-rich-editor-wrapper .ProseMirror code,
.dark .blog-rich-editor-prosemirror code {
  background-color: rgb(55 65 81) !important;
}

/* LISTS - Maximum specificity */
.blog-rich-editor-wrapper .ProseMirror ul,
.blog-rich-editor-prosemirror ul {
  list-style-type: disc !important;
  margin: 1rem 0 !important;
  padding-left: 1.5rem !important;
  display: block !important;
}

.blog-rich-editor-wrapper .ProseMirror ol,
.blog-rich-editor-prosemirror ol {
  list-style-type: decimal !important;
  margin: 1rem 0 !important;
  padding-left: 1.5rem !important;
  display: block !important;
}

.blog-rich-editor-wrapper .ProseMirror li,
.blog-rich-editor-prosemirror li {
  margin: 0.5rem 0 !important;
  line-height: 1.6 !important;
  display: list-item !important;
}

/* BLOCKQUOTE - Maximum specificity */
.blog-rich-editor-wrapper .ProseMirror blockquote,
.blog-rich-editor-prosemirror blockquote {
  border-left: 4px solid var(--primary) !important;
  margin: 1.5rem 0 !important;
  padding: 0.5rem 1.5rem !important;
  background: linear-gradient(
    135deg,
    rgb(249 250 251) 0%,
    rgb(243 244 246) 100%
  ) !important;
  border-radius: 0.75rem !important;
  font-style: italic !important;
  color: rgb(75 85 99) !important;
  position: relative !important;
  display: block !important;
}

.dark .blog-rich-editor-wrapper .ProseMirror blockquote,
.dark .blog-rich-editor-prosemirror blockquote {
  background: linear-gradient(
    135deg,
    rgb(17 24 39) 0%,
    rgb(31 41 55) 100%
  ) !important;
  color: rgb(156 163 175) !important;
}

/* LINKS */
.blog-rich-editor-wrapper .ProseMirror a,
.blog-rich-editor-prosemirror a,
.editor-link {
  color: var(--primary) !important;
  text-decoration: underline !important;
  text-underline-offset: 2px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  border-radius: 2px !important;
  position: relative !important;
  font-weight: 500 !important;
  text-decoration-thickness: 1px !important;
}

.blog-rich-editor-wrapper .ProseMirror a:hover,
.blog-rich-editor-prosemirror a:hover,
.editor-link:hover {
  color: white !important;
  background-color: var(--primary) !important;
  padding: 2px 4px !important;
  border-radius: 4px !important;
  text-decoration: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  transform: none !important;
  transition: all 0.2s ease !important;
  font-size: inherit !important;
  line-height: inherit !important;
  display: inline !important;
  font-weight: 500 !important;
}

/* External links (target="_blank") indicator */
.blog-rich-editor-wrapper .ProseMirror a[target="_blank"],
.blog-rich-editor-prosemirror a[target="_blank"] {
  position: relative !important;
}

.blog-rich-editor-wrapper .ProseMirror a[target="_blank"]::after,
.blog-rich-editor-prosemirror a[target="_blank"]::after {
  content: "↗" !important;
  font-size: 0.75em !important;
  vertical-align: super !important;
  margin-left: 2px !important;
  opacity: 0.6 !important;
}

/* Sponsored/nofollow links visual indicator */
.blog-rich-editor-wrapper .ProseMirror a[rel*="sponsored"],
.blog-rich-editor-prosemirror a[rel*="sponsored"] {
  color: rgb(168 85 247) !important; /* Purple for sponsored */
}

.blog-rich-editor-wrapper .ProseMirror a[rel*="nofollow"],
.blog-rich-editor-prosemirror a[rel*="nofollow"] {
  color: rgb(239 68 68) !important; /* Red for nofollow */
}

/* Links that are both sponsored and nofollow */
.blog-rich-editor-wrapper .ProseMirror a[rel*="sponsored"][rel*="nofollow"],
.blog-rich-editor-prosemirror a[rel*="sponsored"][rel*="nofollow"] {
  color: rgb(251 146 60) !important; /* Orange for both */
}

/* All links show white text on hover, regardless of type */
.blog-rich-editor-wrapper .ProseMirror a[rel*="sponsored"]:hover,
.blog-rich-editor-prosemirror a[rel*="sponsored"]:hover,
.blog-rich-editor-wrapper .ProseMirror a[rel*="nofollow"]:hover,
.blog-rich-editor-prosemirror a[rel*="nofollow"]:hover,
.blog-rich-editor-wrapper .ProseMirror a[rel*="sponsored"][rel*="nofollow"]:hover,
.blog-rich-editor-prosemirror a[rel*="sponsored"][rel*="nofollow"]:hover {
  color: white !important;
  background-color: var(--primary) !important;
  transform: none !important;
  font-size: inherit !important;
}

/* Link click animation - removed scaling to prevent zoom effect */
.blog-rich-editor-wrapper .ProseMirror a:active,
.blog-rich-editor-prosemirror a:active {
  transform: none !important;
  opacity: 0.8 !important;
  transition: opacity 0.1s ease !important;
}

/* Link focus styles for accessibility */
.blog-rich-editor-wrapper .ProseMirror a:focus,
.blog-rich-editor-prosemirror a:focus {
  outline: 2px solid var(--primary) !important;
  outline-offset: 2px !important;
  border-radius: 4px !important;
}

/* IMAGES */
.blog-rich-editor-wrapper .ProseMirror img,
.blog-rich-editor-prosemirror img,
.editor-image {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 8px !important;
  margin: 1.5rem 0 !important;
  display: block !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
  border: 1px solid rgb(229 231 235) !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

.blog-rich-editor-wrapper .ProseMirror img:hover,
.blog-rich-editor-prosemirror img:hover,
.editor-image:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
  transform: translateY(-2px) !important;
  border-color: var(--primary) !important;
}

.dark .blog-rich-editor-wrapper .ProseMirror img,
.dark .blog-rich-editor-prosemirror img,
.dark .editor-image {
  border-color: rgb(55 65 81) !important;
}

.dark .blog-rich-editor-wrapper .ProseMirror img:hover,
.dark .blog-rich-editor-prosemirror img:hover,
.dark .editor-image:hover {
  border-color: var(--primary) !important;
}

/* Image selection styling - Reduced border width */
.blog-rich-editor-wrapper .ProseMirror img.ProseMirror-selectednode,
.blog-rich-editor-prosemirror img.ProseMirror-selectednode,
.blog-rich-editor-wrapper .ProseMirror figure.ProseMirror-selectednode,
.blog-rich-editor-prosemirror figure.ProseMirror-selectednode {
  border-color: var(--primary) !important;
  border-width: 1px !important;
  box-shadow: 0 0 0 1px var(--primary) !important;
}

/* Image Figure Container */
.blog-rich-editor-wrapper .ProseMirror figure.image-figure,
.blog-rich-editor-prosemirror figure.image-figure {
  margin: 1.5rem 0 !important;
  padding: 0 !important;
  display: block !important;
  text-align: center !important;
  border: none !important;
  background: transparent !important;
}

.blog-rich-editor-wrapper .ProseMirror figure.image-figure img,
.blog-rich-editor-prosemirror figure.image-figure img {
  margin: 0 0 0.75rem 0 !important;
  display: block !important;
  max-width: 100% !important;
  height: auto !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
  border: 1px solid rgb(229 231 235) !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

.blog-rich-editor-wrapper .ProseMirror figure.image-figure img:hover,
.blog-rich-editor-prosemirror figure.image-figure img:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
  transform: translateY(-2px) !important;
  border-color: var(--primary) !important;
}

.dark .blog-rich-editor-wrapper .ProseMirror figure.image-figure img,
.dark .blog-rich-editor-prosemirror figure.image-figure img {
  border-color: rgb(55 65 81) !important;
}

.dark .blog-rich-editor-wrapper .ProseMirror figure.image-figure img:hover,
.dark .blog-rich-editor-prosemirror figure.image-figure img:hover {
  border-color: var(--primary) !important;
}

/* Image Caption */
.blog-rich-editor-wrapper .ProseMirror figcaption.image-caption,
.blog-rich-editor-prosemirror figcaption.image-caption {
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  color: rgb(107 114 128) !important;
  text-align: center !important;
  font-style: italic !important;
  padding: 0.5rem 1rem !important;
  margin: 0 !important;
  border: none !important;
  background: transparent !important;
  outline: none !important;
  min-height: 1.5rem !important;
  cursor: text !important;
  white-space: normal !important;
  word-wrap: break-word !important;
}

.dark .blog-rich-editor-wrapper .ProseMirror figcaption.image-caption,
.dark .blog-rich-editor-prosemirror figcaption.image-caption {
  color: rgb(156 163 175) !important;
}

.blog-rich-editor-wrapper .ProseMirror figcaption.image-caption:empty:before,
.blog-rich-editor-prosemirror figcaption.image-caption:empty:before {
  content: attr(data-placeholder);
  color: rgb(156 163 175) !important;
  opacity: 0.6 !important;
  pointer-events: none !important;
}

.blog-rich-editor-wrapper .ProseMirror figcaption.image-caption:focus,
.blog-rich-editor-prosemirror figcaption.image-caption:focus {
  outline: none !important;
  color: var(--foreground) !important;
  background: rgb(249 250 251) !important;
  border-radius: 4px !important;
  padding: 0.5rem 1rem !important;
}

.dark .blog-rich-editor-wrapper .ProseMirror figcaption.image-caption:focus,
.dark .blog-rich-editor-prosemirror figcaption.image-caption:focus {
  background: rgb(31 41 55) !important;
}

/* Image Links */
.blog-rich-editor-wrapper .ProseMirror figure.image-figure a,
.blog-rich-editor-prosemirror figure.image-figure a {
  display: block !important;
  text-decoration: none !important;
  border: none !important;
  outline: none !important;
  position: relative !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

.blog-rich-editor-wrapper .ProseMirror figure.image-figure a:hover,
.blog-rich-editor-prosemirror figure.image-figure a:hover {
  text-decoration: none !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

/* Add a subtle link indicator for linked images */
.blog-rich-editor-wrapper .ProseMirror figure.image-figure a::after,
.blog-rich-editor-prosemirror figure.image-figure a::after {
  content: '🔗' !important;
  position: absolute !important;
  top: 10px !important;
  right: 10px !important;
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  padding: 6px 8px !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
  pointer-events: none !important;
  z-index: 10 !important;
}

.blog-rich-editor-wrapper .ProseMirror figure.image-figure a:hover::after,
.blog-rich-editor-prosemirror figure.image-figure a:hover::after {
  opacity: 1 !important;
}

/* Caption Links */
.blog-rich-editor-wrapper .ProseMirror figcaption.image-caption a,
.blog-rich-editor-prosemirror figcaption.image-caption a {
  color: var(--primary) !important;
  text-decoration: underline !important;
  text-underline-offset: 2px !important;
  display: inline !important;
}

.blog-rich-editor-wrapper .ProseMirror figcaption.image-caption a:hover,
.blog-rich-editor-prosemirror figcaption.image-caption a:hover {
  text-decoration: underline !important;
  opacity: 0.8 !important;
}



/* HORIZONTAL RULE */
.blog-rich-editor-wrapper .ProseMirror hr,
.blog-rich-editor-prosemirror hr {
  border: none !important;
  height: 3px !important;
  background: linear-gradient(
    90deg,
    transparent,
    var(--primary),
    transparent
  ) !important;
  margin: 2.5rem 0 !important;
  border-radius: 2px !important;
  display: block !important;
}

/* SELECTION - Improved visibility like original TipTap */
.blog-rich-editor-wrapper .ProseMirror ::selection,
.blog-rich-editor-prosemirror ::selection {
  background: var(--primary) !important;
  color: white !important;
  text-shadow: none !important;
}

/* Selection for different elements to ensure white text */
.blog-rich-editor-wrapper .ProseMirror h1::selection,
.blog-rich-editor-wrapper .ProseMirror h2::selection,
.blog-rich-editor-wrapper .ProseMirror h3::selection,
.blog-rich-editor-wrapper .ProseMirror p::selection,
.blog-rich-editor-wrapper .ProseMirror strong::selection,
.blog-rich-editor-wrapper .ProseMirror em::selection,
.blog-rich-editor-wrapper .ProseMirror u::selection,
.blog-rich-editor-wrapper .ProseMirror li::selection,
.blog-rich-editor-wrapper .ProseMirror blockquote::selection,
.blog-rich-editor-wrapper .ProseMirror a::selection,
.blog-rich-editor-prosemirror h1::selection,
.blog-rich-editor-prosemirror h2::selection,
.blog-rich-editor-prosemirror h3::selection,
.blog-rich-editor-prosemirror p::selection,
.blog-rich-editor-prosemirror strong::selection,
.blog-rich-editor-prosemirror em::selection,
.blog-rich-editor-prosemirror u::selection,
.blog-rich-editor-prosemirror li::selection,
.blog-rich-editor-prosemirror blockquote::selection,
.blog-rich-editor-prosemirror a::selection {
  background: var(--primary) !important;
  color: white !important;
  text-shadow: none !important;
}

/* Focus styles */
.blog-rich-editor-wrapper .ProseMirror:focus {
  outline: none !important;
}

/* CODE BLOCKS - Enhanced styling */
.blog-rich-editor-wrapper .code-block-wrapper,
.blog-rich-editor-prosemirror .code-block-wrapper {
  margin: 1.5rem 0 !important;
  border-radius: 0.5rem !important;
  overflow: hidden !important;
  background: #1f2937 !important;
  border: 1px solid #374151 !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  display: block !important;
  position: relative !important;
}

.blog-rich-editor-wrapper .code-block-header,
.blog-rich-editor-prosemirror .code-block-header {
  background: #1f2937 !important;
  border-bottom: 1px solid #374151 !important;
  padding: 0.5rem 1rem !important;
}

.blog-rich-editor-wrapper .code-block-content,
.blog-rich-editor-prosemirror .code-block-content {
  background: #111827 !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
  display: block !important;
}

.blog-rich-editor-wrapper .code-block-content pre,
.blog-rich-editor-prosemirror .code-block-content pre {
  background: #111827 !important;
  color: #f9fafb !important;
  margin: 0 !important;
  padding: 1rem !important;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  border-radius: 0 !important;
  overflow: visible !important;
  white-space: pre-wrap !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  display: block !important;
}

.blog-rich-editor-wrapper .code-block-content code,
.blog-rich-editor-prosemirror .code-block-content code {
  background: transparent !important;
  color: inherit !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-family: inherit !important;
  font-size: inherit !important;
  font-weight: inherit !important;
}

/* Ensure code block content is editable and visible */
.blog-rich-editor-wrapper .code-block-content .ProseMirror,
.blog-rich-editor-prosemirror .code-block-content .ProseMirror {
  outline: none !important;
  border: none !important;
  background: transparent !important;
  color: #f9fafb !important;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  padding: 0 !important;
  margin: 0 !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  overflow: visible !important;
  display: block !important;
}

.blog-rich-editor-wrapper .code-block-content .ProseMirror:empty:before,
.blog-rich-editor-prosemirror .code-block-content .ProseMirror:empty:before {
  content: "Enter your code here...";
  color: #6b7280 !important;
  font-style: italic !important;
  opacity: 0.7 !important;
}

/* Syntax highlighting colors */
.blog-rich-editor-wrapper .hljs-keyword,
.blog-rich-editor-prosemirror .hljs-keyword {
  color: #8b5cf6 !important;
}

.blog-rich-editor-wrapper .hljs-string,
.blog-rich-editor-prosemirror .hljs-string {
  color: #10b981 !important;
}

.blog-rich-editor-wrapper .hljs-comment,
.blog-rich-editor-prosemirror .hljs-comment {
  color: #6b7280 !important;
  font-style: italic !important;
}

.blog-rich-editor-wrapper .hljs-number,
.blog-rich-editor-prosemirror .hljs-number {
  color: #f59e0b !important;
}

.blog-rich-editor-wrapper .hljs-function,
.blog-rich-editor-prosemirror .hljs-function {
  color: #3b82f6 !important;
}

.blog-rich-editor-wrapper .hljs-variable,
.blog-rich-editor-prosemirror .hljs-variable {
  color: #ef4444 !important;
}

.blog-rich-editor-wrapper .hljs-title,
.blog-rich-editor-prosemirror .hljs-title {
  color: #06b6d4 !important;
}

.blog-rich-editor-wrapper .hljs-attr,
.blog-rich-editor-prosemirror .hljs-attr {
  color: #8b5cf6 !important;
}

.blog-rich-editor-wrapper .hljs-tag,
.blog-rich-editor-prosemirror .hljs-tag {
  color: #ef4444 !important;
}

.blog-rich-editor-wrapper .hljs-built_in,
.blog-rich-editor-prosemirror .hljs-built_in {
  color: #f59e0b !important;
}

.blog-rich-editor-wrapper .hljs-literal,
.blog-rich-editor-prosemirror .hljs-literal {
  color: #06b6d4 !important;
}

.blog-rich-editor-wrapper .hljs-meta,
.blog-rich-editor-prosemirror .hljs-meta {
  color: #6b7280 !important;
}

.blog-rich-editor-wrapper .hljs-selector-tag,
.blog-rich-editor-prosemirror .hljs-selector-tag {
  color: #ef4444 !important;
}

.blog-rich-editor-wrapper .hljs-selector-class,
.blog-rich-editor-prosemirror .hljs-selector-class {
  color: #f59e0b !important;
}

.blog-rich-editor-wrapper .hljs-selector-id,
.blog-rich-editor-prosemirror .hljs-selector-id {
  color: #3b82f6 !important;
}

.blog-rich-editor-wrapper .hljs-property,
.blog-rich-editor-prosemirror .hljs-property {
  color: #10b981 !important;
}

.blog-rich-editor-wrapper .hljs-operator,
.blog-rich-editor-prosemirror .hljs-operator {
  color: #8b5cf6 !important;
}

/* Custom scrollbar for code blocks */
.blog-rich-editor-wrapper .code-block-content::-webkit-scrollbar,
.blog-rich-editor-prosemirror .code-block-content::-webkit-scrollbar {
  height: 12px !important;
  width: 12px !important;
}

.blog-rich-editor-wrapper .code-block-content::-webkit-scrollbar-track,
.blog-rich-editor-prosemirror .code-block-content::-webkit-scrollbar-track {
  background: #374151 !important;
  border-radius: 6px !important;
}

.blog-rich-editor-wrapper .code-block-content::-webkit-scrollbar-thumb,
.blog-rich-editor-prosemirror .code-block-content::-webkit-scrollbar-thumb {
  background: #6b7280 !important;
  border-radius: 6px !important;
  border: 2px solid #374151 !important;
}

.blog-rich-editor-wrapper .code-block-content::-webkit-scrollbar-thumb:hover,
.blog-rich-editor-prosemirror .code-block-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af !important;
}

.blog-rich-editor-wrapper .code-block-content::-webkit-scrollbar-corner,
.blog-rich-editor-prosemirror .code-block-content::-webkit-scrollbar-corner {
  background: #374151 !important;
}

/* Preview mode styling */
.blog-rich-editor-preview h1 {
  font-size: 2.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin: 2rem 0 1.5rem 0;
}

.blog-rich-editor-preview h2 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.3;
  margin: 1.75rem 0 1.25rem 0;
}

.blog-rich-editor-preview h3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  margin: 1.5rem 0 1rem 0;
}

.blog-rich-editor-preview p {
  margin: 1rem 0;
  line-height: 1.7;
}

.blog-rich-editor-preview ul {
  list-style-type: disc;
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.blog-rich-editor-preview ol {
  list-style-type: decimal;
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.blog-rich-editor-preview li {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.blog-rich-editor-preview blockquote {
  border-left: 4px solid var(--primary);
  margin: 1rem 0;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(
    135deg,
    rgb(249 250 251) 0%,
    rgb(243 244 246) 100%
  );
  border-radius: 0.75rem;
  font-style: italic;
  color: rgb(75 85 99);
}

.blog-rich-editor-preview code {
  background-color: rgb(243 244 246);
  color: var(--primary);
  padding: 0.2rem 0.4rem;
  border-radius: 0.375rem;
  font-family: monospace;
  font-size: 0.875em;
}

.blog-rich-editor-preview .code-block-wrapper {
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
  background: #1f2937;
  border: 1px solid #374151;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  display: block;
  position: relative;
}

.blog-rich-editor-preview .code-block-header {
  background: #1f2937;
  border-bottom: 1px solid #374151;
  padding: 0.5rem 1rem;
  color: white;
}

.blog-rich-editor-preview .code-block-content {
  background: #111827;
  overflow-x: auto;
  overflow-y: hidden;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
  display: block;
}

.blog-rich-editor-preview .code-block-content pre {
  background: #111827;
  color: #f9fafb;
  margin: 0;
  padding: 1rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0;
  overflow: visible;
  white-space: pre-wrap;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  word-wrap: break-word;
  overflow-wrap: break-word;
  display: block;
}

.blog-rich-editor-preview .code-block-content code {
  background: transparent;
  color: inherit;
  padding: 0;
  border-radius: 0;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
}

.blog-rich-editor-preview a {
  color: var(--primary);
  text-decoration: underline;
  text-underline-offset: 2px;
}

.blog-rich-editor-preview img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1.5rem 0;
  display: block;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  border: 1px solid rgb(229 231 235);
}

.blog-rich-editor-preview figure.image-figure {
  margin: 1.5rem 0;
  padding: 0;
  text-align: center;
}

.blog-rich-editor-preview figure.image-figure img {
  margin: 0 0 0.75rem 0;
  display: block;
}

.blog-rich-editor-preview figcaption.image-caption {
  font-size: 0.875rem;
  line-height: 1.5;
  color: rgb(107 114 128);
  text-align: center;
  font-style: italic;
  padding: 0.5rem 1rem;
  margin: 0;
}

.blog-rich-editor-preview figure.image-figure a {
  display: block;
  text-decoration: none;
  position: relative;
  transition: all 0.2s ease;
  cursor: pointer;
}

.blog-rich-editor-preview figure.image-figure a:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Link indicator for preview mode */
.blog-rich-editor-preview figure.image-figure a::after {
  content: '🔗';
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 8px;
  border-radius: 6px;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10;
}

.blog-rich-editor-preview figure.image-figure a:hover::after {
  opacity: 1;
}

.blog-rich-editor-preview figcaption.image-caption a {
  color: var(--primary);
  text-decoration: underline;
  text-underline-offset: 2px;
}

.blog-rich-editor-preview hr {
  border: none;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--primary), transparent);
  margin: 2.5rem 0;
  border-radius: 2px;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .blog-rich-editor-wrapper .ProseMirror,
  .blog-rich-editor-prosemirror {
    font-size: 14px;
    padding: 1rem;
  }

  .blog-rich-editor-wrapper .ProseMirror h1,
  .blog-rich-editor-prosemirror h1 {
    font-size: 1.875rem !important;
  }

  .blog-rich-editor-wrapper .ProseMirror h2,
  .blog-rich-editor-prosemirror h2 {
    font-size: 1.5rem !important;
  }

  .blog-rich-editor-wrapper .ProseMirror h3,
  .blog-rich-editor-prosemirror h3 {
    font-size: 1.25rem !important;
  }

  .blog-rich-editor-toolbar-buttons {
    padding: 0.5rem;
    gap: 0.125rem;
  }

  /* Mobile image adjustments */
  .blog-rich-editor-wrapper .ProseMirror img,
  .blog-rich-editor-prosemirror img,
  .editor-image {
    margin: 1rem 0 !important;
    border-radius: 6px !important;
  }

  .blog-rich-editor-wrapper .ProseMirror figure.image-figure,
  .blog-rich-editor-prosemirror figure.image-figure {
    margin: 1rem 0 !important;
  }

  .blog-rich-editor-wrapper .ProseMirror figure.image-figure img,
  .blog-rich-editor-prosemirror figure.image-figure img {
    margin: 0 0 0.5rem 0 !important;
    border-radius: 6px !important;
  }

  .blog-rich-editor-wrapper .ProseMirror figcaption.image-caption,
  .blog-rich-editor-prosemirror figcaption.image-caption {
    font-size: 0.8rem !important;
    padding: 0.4rem 0.8rem !important;
  }

  .blog-rich-editor-preview img {
    margin: 1rem 0;
    border-radius: 6px;
  }

  .blog-rich-editor-preview figure.image-figure {
    margin: 1rem 0;
  }

  .blog-rich-editor-preview figure.image-figure img {
    margin: 0 0 0.5rem 0;
    border-radius: 6px;
  }

  .blog-rich-editor-preview figcaption.image-caption {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

/* Smooth transitions */
/* .blog-rich-editor-toolbar {
  transition: box-shadow 0.2s ease;
}

.blog-rich-editor-toolbar:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}  */

/* ================================ */
/* YOUTUBE EMBED STYLES */
/* ================================ */
.blog-rich-editor-wrapper .ProseMirror .youtube-embed-wrapper,
.blog-rich-editor-prosemirror .youtube-embed-wrapper {
  margin: 2rem 0 !important;
  border-radius: 0.75rem !important;
  overflow: hidden !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
  background: rgb(0 0 0) !important;
  position: relative !important;
  width: 100% !important;
  max-width: 100% !important;
}

.blog-rich-editor-wrapper .ProseMirror .youtube-embed-container,
.blog-rich-editor-prosemirror .youtube-embed-container {
  position: relative !important;
  padding-bottom: 56.25% !important;
  height: 0 !important;
  overflow: hidden !important;
  width: 100% !important;
}

.blog-rich-editor-wrapper .ProseMirror .youtube-embed-container iframe,
.blog-rich-editor-prosemirror .youtube-embed-container iframe {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  border-radius: 0 !important;
}

.blog-rich-editor-wrapper .ProseMirror .youtube-embed-wrapper.ProseMirror-selectednode,
.blog-rich-editor-prosemirror .youtube-embed-wrapper.ProseMirror-selectednode {
  outline: 2px solid var(--primary) !important;
  outline-offset: 2px !important;
}

/* ================================ */
/* TWITTER EMBED STYLES */
/* ================================ */
.blog-rich-editor-wrapper .ProseMirror .twitter-embed-wrapper,
.blog-rich-editor-prosemirror .twitter-embed-wrapper {
  margin: 2rem 0 !important;
  display: flex !important;
  justify-content: center !important;
  width: 100% !important;
}

.blog-rich-editor-wrapper .ProseMirror .twitter-embed-container,
.blog-rich-editor-prosemirror .twitter-embed-container {
  max-width: 550px !important;
  margin: 0 auto !important;
  border: none !important;
  border-radius: 0 !important;
  overflow: visible !important;
  background: transparent !important;
  box-shadow: none !important;
  width: 100% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.dark .blog-rich-editor-wrapper .ProseMirror .twitter-embed-container,
.dark .blog-rich-editor-prosemirror .twitter-embed-container {
  background: transparent !important;
  border: none !important;
}

.blog-rich-editor-wrapper .ProseMirror .twitter-embed-placeholder,
.blog-rich-editor-prosemirror .twitter-embed-placeholder {
  padding: 20px !important;
  text-align: center !important;
  color: #657786 !important;
}

.dark .blog-rich-editor-wrapper .ProseMirror .twitter-embed-placeholder,
.dark .blog-rich-editor-prosemirror .twitter-embed-placeholder {
  color: rgb(156 163 175) !important;
}

.blog-rich-editor-wrapper .ProseMirror .twitter-embed-placeholder a,
.blog-rich-editor-prosemirror .twitter-embed-placeholder a {
  color: #1da1f2 !important;
  text-decoration: none !important;
  font-size: 14px !important;
}

.blog-rich-editor-wrapper .ProseMirror .twitter-embed-placeholder a:hover,
.blog-rich-editor-prosemirror .twitter-embed-placeholder a:hover {
  text-decoration: underline !important;
}

.blog-rich-editor-wrapper .ProseMirror .twitter-embed-wrapper.ProseMirror-selectednode,
.blog-rich-editor-prosemirror .twitter-embed-wrapper.ProseMirror-selectednode {
  outline: 2px solid var(--primary) !important;
  outline-offset: 2px !important;
}

/* ================================ */
/* PREVIEW MODE STYLES FOR EMBEDS */
/* ================================ */
.blog-rich-editor-preview .youtube-embed-wrapper {
  margin: 2rem 0;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  background: rgb(0 0 0);
}

.blog-rich-editor-preview .youtube-embed-container {
  position: relative;
  padding-bottom: 56.25%;
  height: 0;
  overflow: hidden;
}

.blog-rich-editor-preview .youtube-embed-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.blog-rich-editor-preview .twitter-embed-wrapper {
  margin: 2rem 0;
  display: flex;
  justify-content: center;
}

.blog-rich-editor-preview .twitter-embed-container {
  max-width: 550px;
  margin: 0 auto;
  border: 1px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.dark .blog-rich-editor-preview .twitter-embed-container {
  background: rgb(21 32 43);
  border-color: rgb(56 68 77);
}

.blog-rich-editor-preview .twitter-embed-placeholder {
  padding: 20px;
  text-align: center;
  color: #657786;
}

.dark .blog-rich-editor-preview .twitter-embed-placeholder {
  color: rgb(156 163 175);
}

.blog-rich-editor-preview .twitter-embed-placeholder a {
  color: #1da1f2;
  text-decoration: none;
  font-size: 14px;
}

.blog-rich-editor-preview .twitter-embed-placeholder a:hover {
  text-decoration: underline;
}

/* ================================ */
/* RESPONSIVE EMBEDS */
/* ================================ */
@media (max-width: 640px) {
  .blog-rich-editor-wrapper .ProseMirror .youtube-embed-wrapper,
  .blog-rich-editor-prosemirror .youtube-embed-wrapper,
  .blog-rich-editor-preview .youtube-embed-wrapper {
    margin: 1.5rem 0 !important;
    border-radius: 0.5rem !important;
  }

  .blog-rich-editor-wrapper .ProseMirror .twitter-embed-container,
  .blog-rich-editor-prosemirror .twitter-embed-container,
  .blog-rich-editor-preview .twitter-embed-container {
    max-width: 100% !important;
    margin: 0 !important;
    border-radius: 8px !important;
  }

  .blog-rich-editor-wrapper .ProseMirror .twitter-embed-wrapper,
  .blog-rich-editor-prosemirror .twitter-embed-wrapper,
  .blog-rich-editor-preview .twitter-embed-wrapper {
    margin: 1.5rem 0 !important;
  }

  .blog-rich-editor-wrapper .ProseMirror .twitter-embed-placeholder,
  .blog-rich-editor-prosemirror .twitter-embed-placeholder,
  .blog-rich-editor-preview .twitter-embed-placeholder {
    padding: 16px !important;
  }
}

/* ================================ */
/* TWITTER EMBED ENHANCED STYLES */
/* ================================ */

/* Twitter Embed Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Twitter Embed Content Styles */
.blog-rich-editor-wrapper .ProseMirror .twitter-embed-content,
.blog-rich-editor-prosemirror .twitter-embed-content,
.blog-rich-editor-preview .twitter-embed-content {
  max-width: 100% !important;
  overflow: visible !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.blog-rich-editor-wrapper .ProseMirror .twitter-embed-content blockquote,
.blog-rich-editor-prosemirror .twitter-embed-content blockquote,
.blog-rich-editor-preview .twitter-embed-content blockquote {
  margin: 0 auto !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
  max-width: 550px !important;
  width: 100% !important;
}

.blog-rich-editor-wrapper .ProseMirror .twitter-embed-content .twitter-tweet,
.blog-rich-editor-prosemirror .twitter-embed-content .twitter-tweet,
.blog-rich-editor-preview .twitter-embed-content .twitter-tweet {
  margin: 0 auto !important;
  max-width: 550px !important;
  width: 100% !important;
}

.blog-rich-editor-wrapper .ProseMirror .twitter-embed-content iframe,
.blog-rich-editor-prosemirror .twitter-embed-content iframe,
.blog-rich-editor-preview .twitter-embed-content iframe {
  max-width: 100% !important;
  border: none !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

/* Twitter Embed Loading State */
.blog-rich-editor-wrapper .ProseMirror .twitter-embed-loading,
.blog-rich-editor-prosemirror .twitter-embed-loading,
.blog-rich-editor-preview .twitter-embed-loading {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 100px !important;
  color: #657786 !important;
  font-size: 14px !important;
  padding: 40px 20px !important;
  text-align: center !important;
}

.dark .blog-rich-editor-wrapper .ProseMirror .twitter-embed-loading,
.dark .blog-rich-editor-prosemirror .twitter-embed-loading,
.dark .blog-rich-editor-preview .twitter-embed-loading {
  color: #9ca3af !important;
}

/* Twitter Embed Error State */
.blog-rich-editor-wrapper .ProseMirror .twitter-embed-error,
.blog-rich-editor-prosemirror .twitter-embed-error,
.blog-rich-editor-preview .twitter-embed-error {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 100px !important;
  color: #657786 !important;
  font-size: 14px !important;
  padding: 20px !important;
  text-align: center !important;
}

.dark .blog-rich-editor-wrapper .ProseMirror .twitter-embed-error,
.dark .blog-rich-editor-prosemirror .twitter-embed-error,
.dark .blog-rich-editor-preview .twitter-embed-error {
  color: #9ca3af !important;
}

.blog-rich-editor-wrapper .ProseMirror .twitter-embed-error a,
.blog-rich-editor-prosemirror .twitter-embed-error a,
.blog-rich-editor-preview .twitter-embed-error a {
  color: #1da1f2 !important;
  text-decoration: none !important;
  font-weight: 500 !important;
}

.blog-rich-editor-wrapper .ProseMirror .twitter-embed-error a:hover,
.blog-rich-editor-prosemirror .twitter-embed-error a:hover,
.blog-rich-editor-preview .twitter-embed-error a:hover {
  text-decoration: underline !important;
}

/* Mobile responsiveness for enhanced Twitter embeds */
@media (max-width: 640px) {
  .blog-rich-editor-wrapper .ProseMirror .twitter-embed-content,
  .blog-rich-editor-prosemirror .twitter-embed-content,
  .blog-rich-editor-preview .twitter-embed-content {
    padding: 0 !important;
  }
  
  .blog-rich-editor-wrapper .ProseMirror .twitter-embed-loading,
  .blog-rich-editor-prosemirror .twitter-embed-loading,
  .blog-rich-editor-preview .twitter-embed-loading,
  .blog-rich-editor-wrapper .ProseMirror .twitter-embed-error,
  .blog-rich-editor-prosemirror .twitter-embed-error,
  .blog-rich-editor-preview .twitter-embed-error {
    padding: 15px !important;
    font-size: 13px !important;
  }
}
