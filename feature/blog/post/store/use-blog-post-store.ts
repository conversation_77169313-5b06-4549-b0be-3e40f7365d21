import { create } from "zustand";
import { BlogPostResponse } from "@/types/blog-api";

interface BlogPostStore {
  // Form dialog state
  isFormDialogOpen: boolean;
  formMode: "create" | "edit";
  editingPost: BlogPostResponse | null;

  // Delete dialog state
  isDeleteDialogOpen: boolean;
  deletingPost: BlogPostResponse | null;

  // Filters and search state
  searchQuery: string;
  statusFilter: string;
  authorFilter: string;
  categoryFilter: string;
  tagFilter: string;
  publishedFilter: string;
  currentPage: number;
  pageSize: number;

  // Actions for form dialog
  openCreateDialog: () => void;
  openEditDialog: (post: BlogPostResponse) => void;
  closeFormDialog: () => void;

  // Actions for delete dialog
  openDeleteDialog: (post: BlogPostResponse) => void;
  closeDeleteDialog: () => void;

  // Actions for filters and search
  setSearchQuery: (query: string) => void;
  setStatusFilter: (status: string) => void;
  setAuthorFilter: (author: string) => void;
  setCategoryFilter: (category: string) => void;
  setTagFilter: (tag: string) => void;
  setPublishedFilter: (published: string) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  resetFilters: () => void;

  // Reset all state
  resetState: () => void;
}

const initialState = {
  // Form dialog state
  isFormDialogOpen: false,
  formMode: "create" as const,
  editingPost: null,

  // Delete dialog state
  isDeleteDialogOpen: false,
  deletingPost: null,

  // Filters and search state
  searchQuery: "",
  statusFilter: "all",
  authorFilter: "all",
  categoryFilter: "all",
  tagFilter: "all",
  publishedFilter: "all",
  currentPage: 1,
  pageSize: 10,
};

export const useBlogPostStore = create<BlogPostStore>((set) => ({
  ...initialState,

  // Actions for form dialog
  openCreateDialog: () =>
    set({
      isFormDialogOpen: true,
      formMode: "create",
      editingPost: null,
    }),

  openEditDialog: (post: BlogPostResponse) =>
    set({
      isFormDialogOpen: true,
      formMode: "edit",
      editingPost: post,
    }),

  closeFormDialog: () =>
    set({
      isFormDialogOpen: false,
      formMode: "create",
      editingPost: null,
    }),

  // Actions for delete dialog
  openDeleteDialog: (post: BlogPostResponse) =>
    set({
      isDeleteDialogOpen: true,
      deletingPost: post,
    }),

  closeDeleteDialog: () =>
    set({
      isDeleteDialogOpen: false,
      deletingPost: null,
    }),

  // Actions for filters and search
  setSearchQuery: (query: string) =>
    set({ searchQuery: query, currentPage: 1 }),

  setStatusFilter: (status: string) =>
    set({ statusFilter: status, currentPage: 1 }),

  setAuthorFilter: (author: string) =>
    set({ authorFilter: author, currentPage: 1 }),

  setCategoryFilter: (category: string) =>
    set({ categoryFilter: category, currentPage: 1 }),

  setTagFilter: (tag: string) => set({ tagFilter: tag, currentPage: 1 }),

  setPublishedFilter: (published: string) =>
    set({ publishedFilter: published, currentPage: 1 }),

  setCurrentPage: (page: number) => set({ currentPage: page }),

  setPageSize: (size: number) => set({ pageSize: size, currentPage: 1 }),

  resetFilters: () =>
    set({
      searchQuery: "",
      statusFilter: "all",
      authorFilter: "all",
      categoryFilter: "all",
      tagFilter: "all",
      publishedFilter: "all",
      currentPage: 1,
    }),

  // Reset all state
  resetState: () => set(initialState),
}));

// Selector hooks for better performance
export const useBlogPostFormState = () => {
  const { isFormDialogOpen, formMode, editingPost } = useBlogPostStore();
  return { isFormDialogOpen, formMode, editingPost };
};

export const useBlogPostFormActions = () => {
  const { openCreateDialog, openEditDialog, closeFormDialog } =
    useBlogPostStore();
  return { openCreateDialog, openEditDialog, closeFormDialog };
};

export const useBlogPostDeleteState = () => {
  const { isDeleteDialogOpen, deletingPost } = useBlogPostStore();
  return { isDeleteDialogOpen, deletingPost };
};

export const useBlogPostDeleteActions = () => {
  const { openDeleteDialog, closeDeleteDialog } = useBlogPostStore();
  return { openDeleteDialog, closeDeleteDialog };
};

export const useBlogPostFiltersState = () => {
  const {
    searchQuery,
    statusFilter,
    authorFilter,
    categoryFilter,
    tagFilter,
    publishedFilter,
    currentPage,
    pageSize,
  } = useBlogPostStore();
  return {
    searchQuery,
    statusFilter,
    authorFilter,
    categoryFilter,
    tagFilter,
    publishedFilter,
    currentPage,
    pageSize,
  };
};

export const useBlogPostFiltersActions = () => {
  const {
    setSearchQuery,
    setStatusFilter,
    setAuthorFilter,
    setCategoryFilter,
    setTagFilter,
    setPublishedFilter,
    setCurrentPage,
    setPageSize,
    resetFilters,
  } = useBlogPostStore();
  return {
    setSearchQuery,
    setStatusFilter,
    setAuthorFilter,
    setCategoryFilter,
    setTagFilter,
    setPublishedFilter,
    setCurrentPage,
    setPageSize,
    resetFilters,
  };
};

export const useBlogPostResetActions = () => {
  const { resetState } = useBlogPostStore();
  return { resetState };
};
