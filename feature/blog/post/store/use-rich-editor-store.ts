"use client";

import { create } from "zustand";
import { Editor } from "@tiptap/react";
import { MediaItem, MediaMetadata } from "@/types/media-api";
import { toast } from "sonner";

// =============================================
// INTERFACE DEFINITIONS
// =============================================

export interface LinkData {
  url: string;
  text: string;
  openInNewTab: boolean;
  noFollow: boolean;
  sponsored: boolean;
}

export interface ImageData {
  src: string;
  alt?: string;
  title?: string;
  caption?: string;
  link?: string;
  linkTarget?: "_blank" | "_self";
  width?: number;
  height?: number;
  node?: any;
  pos?: number;
}

export interface MetadataForm {
  title: string;
  alt_text: string;
  caption: string;
  description: string;
}

// Add selection mode type
export type MediaSelectionMode = "editor" | "featured_image";

// =============================================
// STORE STATE INTERFACE
// =============================================

interface RichEditorState {
  // Editor instance
  editor: Editor | null;
  
  // Dialog visibility states
  isPreviewMode: boolean;
  isLinkDialogOpen: boolean;
  isMediaDialogOpen: boolean;
  isImageOptionsOpen: boolean;
  
  // Link dialog state
  linkData: LinkData;
  isEditingLink: boolean;
  hasTextSelection: boolean;
  urlError: string;
  
  // Media dialog state
  activeTab: "upload" | "library";
  currentPath: string;
  viewMode: "grid" | "list";
  searchQuery: string;
  typeFilter: string;
  tempSelectedUrl: string;
  selectedItem: MediaItem | null;
  isDragOver: boolean;
  uploadingFiles: File[];
  metadataForm: MetadataForm;
  // Add selection mode and callback
  selectionMode: MediaSelectionMode;
  onImageSelected?: (url: string) => void;
  
  // Image options state
  selectedImageData: ImageData | null;
  imageOptionsActiveTab: "general" | "link";
  isLinkEnabled: boolean;
  isLinkPopoverOpen: boolean;
  captionLinkText: string;
  captionLinkUrl: string;
  captionSelectedText: string;
  
  // Actions - Dialog Management
  setEditor: (editor: Editor | null) => void;
  togglePreviewMode: () => void;
  openLinkDialog: () => void;
  closeLinkDialog: () => void;
  openMediaDialog: (mode?: MediaSelectionMode, onSelected?: (url: string) => void) => void;
  closeMediaDialog: () => void;
  openImageOptionsDialog: (imageData: ImageData) => void;
  closeImageOptionsDialog: () => void;
  
  // Actions - Link Dialog
  setLinkData: (data: Partial<LinkData>) => void;
  setIsEditingLink: (isEditing: boolean) => void;
  setHasTextSelection: (hasSelection: boolean) => void;
  setUrlError: (error: string) => void;
  initializeLinkDialog: () => void;
  validateUrl: (url: string) => boolean;
  buildRelAttribute: () => string;
  handleSaveLink: () => void;
  handleRemoveLink: () => void;
  resetLinkDialog: () => void;
  
  // Actions - Media Dialog
  setActiveTab: (tab: "upload" | "library") => void;
  setCurrentPath: (path: string) => void;
  setViewMode: (mode: "grid" | "list") => void;
  setSearchQuery: (query: string) => void;
  setTypeFilter: (filter: string) => void;
  setTempSelectedUrl: (url: string) => void;
  setSelectedItem: (item: MediaItem | null) => void;
  setIsDragOver: (isDragOver: boolean) => void;
  setUploadingFiles: (files: File[]) => void;
  setMetadataForm: (data: Partial<MetadataForm>) => void;
  handleItemClick: (item: MediaItem) => void;
  handleImageSelect: (url: string, metadata?: Partial<MediaMetadata>) => void;
  resetMediaDialog: () => void;
  
  // Actions - Image Options
  setSelectedImageData: (data: ImageData | null) => void;
  setImageOptionsActiveTab: (tab: "general" | "link") => void;
  setIsLinkEnabled: (enabled: boolean) => void;
  setIsLinkPopoverOpen: (open: boolean) => void;
  setCaptionLinkText: (text: string) => void;
  setCaptionLinkUrl: (url: string) => void;
  setCaptionSelectedText: (text: string) => void;
  handleImageOptionsInputChange: (field: keyof ImageData, value: string | number) => void;
  handleImageOptionsLinkToggle: (enabled: boolean) => void;
  handleImageOptionsSave: () => void;
  handleImageOptionsUpdate: (updatedImageData: ImageData) => void;
  handleImageDelete: () => void;
  resetImageOptionsDialog: () => void;
  
  // Utility Actions
  resetAllDialogs: () => void;
  isValidUrl: (url: string) => boolean;
}

// =============================================
// INITIAL STATE VALUES
// =============================================

const initialLinkData: LinkData = {
  url: "",
  text: "",
  openInNewTab: false,
  noFollow: false,
  sponsored: false,
};

const initialMetadataForm: MetadataForm = {
  title: "",
  alt_text: "",
  caption: "",
  description: "",
};

// =============================================
// ZUSTAND STORE IMPLEMENTATION
// =============================================

export const useRichEditorStore = create<RichEditorState>((set, get) => ({
  // Initial state
  editor: null,
  
  // Dialog visibility states
  isPreviewMode: false,
  isLinkDialogOpen: false,
  isMediaDialogOpen: false,
  isImageOptionsOpen: false,
  
  // Link dialog state
  linkData: { ...initialLinkData },
  isEditingLink: false,
  hasTextSelection: false,
  urlError: "",
  
  // Media dialog state
  activeTab: "library",
  currentPath: "",
  viewMode: "grid",
  searchQuery: "",
  typeFilter: "images",
  tempSelectedUrl: "",
  selectedItem: null,
  isDragOver: false,
  uploadingFiles: [],
  metadataForm: { ...initialMetadataForm },
  selectionMode: "editor",
  onImageSelected: undefined,
  
  // Image options state
  selectedImageData: null,
  imageOptionsActiveTab: "general",
  isLinkEnabled: false,
  isLinkPopoverOpen: false,
  captionLinkText: "",
  captionLinkUrl: "",
  captionSelectedText: "",
  
  // =============================================
  // DIALOG MANAGEMENT ACTIONS
  // =============================================
  
  setEditor: (editor) => set({ editor }),
  
  togglePreviewMode: () => set((state) => ({ isPreviewMode: !state.isPreviewMode })),
  
  openLinkDialog: () => {
    get().initializeLinkDialog();
    set({ isLinkDialogOpen: true });
  },
  
  closeLinkDialog: () => {
    get().resetLinkDialog();
    set({ isLinkDialogOpen: false });
  },
  
  openMediaDialog: (mode?: MediaSelectionMode, onSelected?: (url: string) => void) => {
    get().resetMediaDialog();
    set({
      isMediaDialogOpen: true,
      selectionMode: mode || "editor",
      onImageSelected: onSelected,
    });
  },
  
  closeMediaDialog: () => {
    get().resetMediaDialog();
    set({ isMediaDialogOpen: false });
  },
  
  openImageOptionsDialog: (imageData) => {
    set({
      selectedImageData: imageData,
      isImageOptionsOpen: true,
      isLinkEnabled: !!imageData.link,
      imageOptionsActiveTab: "general",
    });
  },
  
  closeImageOptionsDialog: () => {
    get().resetImageOptionsDialog();
    set({ isImageOptionsOpen: false });
  },
  
  // =============================================
  // LINK DIALOG ACTIONS
  // =============================================
  
  setLinkData: (data) => set((state) => ({ 
    linkData: { ...state.linkData, ...data } 
  })),
  
  setIsEditingLink: (isEditing) => set({ isEditingLink: isEditing }),
  
  setHasTextSelection: (hasSelection) => set({ hasTextSelection: hasSelection }),
  
  setUrlError: (error) => set({ urlError: error }),
  
  initializeLinkDialog: () => {
    const { editor } = get();
    if (!editor) return;

    const { from, to } = editor.state.selection;
    const selectedText = editor.state.doc.textBetween(from, to);
    const hasTextSelection = selectedText.length > 0;

    // Check if current selection is already a link
    const linkAttributes = editor.getAttributes("link");
    const isCurrentlyLinked = linkAttributes.href;

    // Try to find link at cursor position if no direct link
    let actualLinkAttributes = linkAttributes;
    if (!isCurrentlyLinked && !hasTextSelection) {
      const pos = editor.state.selection.anchor;
      const resolvedPos = editor.state.doc.resolve(pos);
      const linkMark = resolvedPos.marks().find((mark) => mark.type.name === "link");
      if (linkMark) {
        actualLinkAttributes = linkMark.attrs;
      }
    }

    const finalIsLinked = isCurrentlyLinked || actualLinkAttributes.href;

    if (finalIsLinked) {
      // Editing existing link
      const rel = actualLinkAttributes.rel || "";
      // Split rel attribute into individual values for accurate checking
      const relValues = rel.split(/\s+/).filter(Boolean);
      set({
        isEditingLink: true,
        hasTextSelection,
        linkData: {
          url: actualLinkAttributes.href || "",
          text: selectedText || "",
          openInNewTab: actualLinkAttributes.target === "_blank",
          noFollow: relValues.includes("nofollow"),
          sponsored: relValues.includes("sponsored"),
        },
        urlError: "",
      });
    } else {
      // Creating new link
      set({
        isEditingLink: false,
        hasTextSelection,
        linkData: {
          url: "",
          text: selectedText || "",
          openInNewTab: false,
          noFollow: false,
          sponsored: false,
        },
        urlError: "",
      });
    }
  },
  
  validateUrl: (url) => {
    if (!url.trim()) {
      set({ urlError: "URL is required" });
      return false;
    }

    try {
      const urlToValidate = url.includes("://") ? url : `https://${url}`;
      new URL(urlToValidate);
      set({ urlError: "" });
      return true;
    } catch {
      set({ urlError: "Please enter a valid URL" });
      return false;
    }
  },
  
  buildRelAttribute: () => {
    const { linkData } = get();
    const relParts: string[] = [];
    
    // Add security-related attributes for new tab links
    if (linkData.openInNewTab) {
      relParts.push("noopener", "noreferrer");
    }
    
    // Add user-selected attributes
    if (linkData.noFollow) relParts.push("nofollow");
    if (linkData.sponsored) relParts.push("sponsored");
    
    return relParts.join(" ");
  },
  
  handleSaveLink: () => {
    const { editor, linkData, isEditingLink, hasTextSelection, validateUrl, buildRelAttribute, closeLinkDialog } = get();
    
    if (!editor || !validateUrl(linkData.url)) return;

    // Normalize URL
    const normalizedUrl = linkData.url.includes("://") ? linkData.url : `https://${linkData.url}`;

    // Build link attributes
    const linkAttributes: any = { href: normalizedUrl };
    if (linkData.openInNewTab) {
      linkAttributes.target = "_blank";
    }

    const relAttribute = buildRelAttribute();
    if (relAttribute) {
      linkAttributes.rel = relAttribute;
    }

    if (isEditingLink || hasTextSelection) {
      // Update existing link or apply to selection
      editor.chain().focus().setLink(linkAttributes).run();
    } else {
      // Insert new link with text
      if (linkData.text.trim()) {
        editor
          .chain()
          .focus()
          .insertContent(
            `<a href="${normalizedUrl}"${linkAttributes.target ? ` target="${linkAttributes.target}"` : ""}${linkAttributes.rel ? ` rel="${linkAttributes.rel}"` : ""}>${linkData.text}</a>`
          )
          .run();
      } else {
        editor.chain().focus().setLink(linkAttributes).run();
      }
    }

    closeLinkDialog();
  },
  
  handleRemoveLink: () => {
    const { editor, closeLinkDialog } = get();
    if (!editor) return;
    editor.chain().focus().unsetLink().run();
    closeLinkDialog();
  },
  
  resetLinkDialog: () => {
    set({
      linkData: { ...initialLinkData },
      isEditingLink: false,
      hasTextSelection: false,
      urlError: "",
    });
  },
  
  // =============================================
  // MEDIA DIALOG ACTIONS
  // =============================================
  
  setActiveTab: (tab) => set({ activeTab: tab }),
  setCurrentPath: (path) => set({ currentPath: path }),
  setViewMode: (mode) => set({ viewMode: mode }),
  setSearchQuery: (query) => set({ searchQuery: query }),
  setTypeFilter: (filter) => set({ typeFilter: filter }),
  setTempSelectedUrl: (url) => set({ tempSelectedUrl: url }),
  setSelectedItem: (item) => set({ selectedItem: item }),
  setIsDragOver: (isDragOver) => set({ isDragOver }),
  setUploadingFiles: (files) => set({ uploadingFiles: files }),
  setMetadataForm: (data) => set((state) => ({ 
    metadataForm: { ...state.metadataForm, ...data } 
  })),
  
  handleItemClick: (item) => {
    if (item.type === "folder") {
      set({ currentPath: item.key });
    } else if (item.url) {
      set({ 
        tempSelectedUrl: item.url,
        selectedItem: item 
      });
    }
  },
  
  handleImageSelect: (imageUrl, metadata) => {
    const { editor, selectionMode, onImageSelected, closeMediaDialog } = get();
    if (!imageUrl) return;

    if (selectionMode === "featured_image") {
      // Handle featured image selection using callback
      if (onImageSelected) {
        onImageSelected(imageUrl);
      }
    } else {
      // Handle editor image insertion (existing behavior)
      if (!editor) return;
      
      editor
        .chain()
        .focus()
        .setImageWithCaption({
          src: imageUrl,
          alt: metadata?.alt_text || "Blog image",
          title: metadata?.title || "Blog image",
          caption: metadata?.caption || "",
        })
        .enter() // Add a new line after the image
        .run();
    }

    closeMediaDialog();
  },
  
  resetMediaDialog: () => {
    set({
      currentPath: "",
      searchQuery: "",
      tempSelectedUrl: "",
      selectedItem: null,
      metadataForm: { ...initialMetadataForm },
      uploadingFiles: [],
      isDragOver: false,
      selectionMode: "editor",
      onImageSelected: undefined,
    });
  },
  
  // =============================================
  // IMAGE OPTIONS ACTIONS
  // =============================================
  
  setSelectedImageData: (data) => set({ selectedImageData: data }),
  setImageOptionsActiveTab: (tab) => set({ imageOptionsActiveTab: tab }),
  setIsLinkEnabled: (enabled) => set({ isLinkEnabled: enabled }),
  setIsLinkPopoverOpen: (open) => set({ isLinkPopoverOpen: open }),
  setCaptionLinkText: (text) => set({ captionLinkText: text }),
  setCaptionLinkUrl: (url) => set({ captionLinkUrl: url }),
  setCaptionSelectedText: (text) => set({ captionSelectedText: text }),
  
  handleImageOptionsInputChange: (field, value) => {
    set((state) => ({
      selectedImageData: state.selectedImageData 
        ? { ...state.selectedImageData, [field]: value }
        : null
    }));
  },
  
  handleImageOptionsLinkToggle: (enabled) => {
    set((state) => ({
      isLinkEnabled: enabled,
      selectedImageData: state.selectedImageData 
        ? {
            ...state.selectedImageData,
            link: enabled ? state.selectedImageData.link : "",
            linkTarget: enabled ? state.selectedImageData.linkTarget : "_blank",
          }
        : null
    }));
  },
  
  handleImageOptionsSave: () => {
    const { selectedImageData, isLinkEnabled } = get();
    if (!selectedImageData) return;

    // Validation
    if (!selectedImageData.src) {
      toast.error("Image source is required");
      return;
    }

    if (isLinkEnabled && selectedImageData.link && !get().isValidUrl(selectedImageData.link)) {
      toast.error("Please enter a valid URL for the image link");
      return;
    }

    // Prepare final data
    const finalData: ImageData = {
      ...selectedImageData,
      link: isLinkEnabled && selectedImageData.link?.trim() ? selectedImageData.link.trim() : "",
      linkTarget: isLinkEnabled && selectedImageData.link?.trim() ? selectedImageData.linkTarget : "_blank",
    };

    get().handleImageOptionsUpdate(finalData);
    toast.success("Image settings updated successfully");
    get().closeImageOptionsDialog();
  },
  
  handleImageOptionsUpdate: (updatedImageData) => {
    const { editor } = get();
    if (!editor || !updatedImageData.node || updatedImageData.pos === undefined) return;

    const { pos, node } = updatedImageData;

    // Find the actual position of the node
    let actualPos = pos;
    const nodeAtPos = editor.state.doc.nodeAt(actualPos);
    
    if (!nodeAtPos || (nodeAtPos.type.name !== "image" && nodeAtPos.type.name !== "imageWithCaption")) {
      // Try to find the node more reliably
      editor.state.doc.descendants((node, pos) => {
        if ((node.type.name === "image" || node.type.name === "imageWithCaption") && 
            node.attrs.src === updatedImageData.src) {
          actualPos = pos;
          return false; // Stop traversal
        }
      });
    }

    // Update the node with new attributes
    const tr = editor.state.tr;
    tr.setNodeMarkup(actualPos, undefined, {
      src: updatedImageData.src,
      alt: updatedImageData.alt || "",
      title: updatedImageData.title || "",
      caption: updatedImageData.caption || "",
      link: updatedImageData.link || "",
      linkTarget: updatedImageData.linkTarget || "_blank",
    });

    editor.view.dispatch(tr);
  },
  
  handleImageDelete: () => {
    const { editor, selectedImageData, closeImageOptionsDialog } = get();
    if (!editor || !selectedImageData || selectedImageData.pos === undefined) return;

    const { pos } = selectedImageData;
    
    // Find and delete the image node
    let actualPos = pos;
    const nodeAtPos = editor.state.doc.nodeAt(actualPos);
    
    if (!nodeAtPos || (nodeAtPos.type.name !== "image" && nodeAtPos.type.name !== "imageWithCaption")) {
      // Try to find the node more reliably
      editor.state.doc.descendants((node, nodePos) => {
        if ((node.type.name === "image" || node.type.name === "imageWithCaption") && 
            node.attrs.src === selectedImageData.src) {
          actualPos = nodePos;
          return false; // Stop traversal
        }
      });
    }

    // Delete the node
    const tr = editor.state.tr;
    tr.delete(actualPos, actualPos + 1);
    editor.view.dispatch(tr);

    closeImageOptionsDialog();
  },
  
  resetImageOptionsDialog: () => {
    set({
      selectedImageData: null,
      imageOptionsActiveTab: "general",
      isLinkEnabled: false,
      isLinkPopoverOpen: false,
      captionLinkText: "",
      captionLinkUrl: "",
      captionSelectedText: "",
    });
  },
  
  // =============================================
  // UTILITY ACTIONS
  // =============================================
  
  resetAllDialogs: () => {
    set({
      isPreviewMode: false,
      isLinkDialogOpen: false,
      isMediaDialogOpen: false,
      isImageOptionsOpen: false,
    });
    get().resetLinkDialog();
    get().resetMediaDialog();
    get().resetImageOptionsDialog();
  },
  
  // Helper method for URL validation (used in image options)
  isValidUrl: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return url.startsWith("/") || url.startsWith("./") || url.startsWith("../");
    }
  },
}));

// =============================================
// SELECTOR HOOKS FOR BETTER PERFORMANCE
// =============================================

export const useRichEditorState = () => {
  const { editor, isPreviewMode } = useRichEditorStore();
  return { editor, isPreviewMode };
};

export const useRichEditorActions = () => {
  const { 
    setEditor, 
    togglePreviewMode, 
    openLinkDialog, 
    openMediaDialog,
    handleImageSelect,
    handleImageOptionsUpdate,
    handleImageDelete 
  } = useRichEditorStore();
  return { 
    setEditor, 
    togglePreviewMode, 
    openLinkDialog, 
    openMediaDialog,
    handleImageSelect,
    handleImageOptionsUpdate,
    handleImageDelete 
  };
};

export const useLinkDialogState = () => {
  const { 
    isLinkDialogOpen, 
    linkData, 
    isEditingLink, 
    hasTextSelection, 
    urlError 
  } = useRichEditorStore();
  return { 
    isLinkDialogOpen, 
    linkData, 
    isEditingLink, 
    hasTextSelection, 
    urlError 
  };
};

export const useLinkDialogActions = () => {
  const { 
    closeLinkDialog, 
    setLinkData, 
    setUrlError, 
    handleSaveLink, 
    handleRemoveLink,
    validateUrl 
  } = useRichEditorStore();
  return { 
    closeLinkDialog, 
    setLinkData, 
    setUrlError, 
    handleSaveLink, 
    handleRemoveLink,
    validateUrl 
  };
};

export const useMediaDialogState = () => {
  const { 
    isMediaDialogOpen,
    activeTab,
    currentPath,
    viewMode,
    searchQuery,
    typeFilter,
    tempSelectedUrl,
    selectedItem,
    isDragOver,
    uploadingFiles,
    metadataForm,
    selectionMode,
    onImageSelected 
  } = useRichEditorStore();
  return { 
    isMediaDialogOpen,
    activeTab,
    currentPath,
    viewMode,
    searchQuery,
    typeFilter,
    tempSelectedUrl,
    selectedItem,
    isDragOver,
    uploadingFiles,
    metadataForm,
    selectionMode,
    onImageSelected 
  };
};

export const useMediaDialogActions = () => {
  const { 
    closeMediaDialog,
    setActiveTab,
    setCurrentPath,
    setViewMode,
    setSearchQuery,
    setTempSelectedUrl,
    setSelectedItem,
    setIsDragOver,
    setUploadingFiles,
    setMetadataForm,
    handleItemClick,
    handleImageSelect 
  } = useRichEditorStore();
  return { 
    closeMediaDialog,
    setActiveTab,
    setCurrentPath,
    setViewMode,
    setSearchQuery,
    setTempSelectedUrl,
    setSelectedItem,
    setIsDragOver,
    setUploadingFiles,
    setMetadataForm,
    handleItemClick,
    handleImageSelect 
  };
};

export const useImageOptionsState = () => {
  const { 
    isImageOptionsOpen,
    selectedImageData,
    imageOptionsActiveTab,
    isLinkEnabled,
    isLinkPopoverOpen,
    captionLinkText,
    captionLinkUrl,
    captionSelectedText 
  } = useRichEditorStore();
  return { 
    isImageOptionsOpen,
    selectedImageData,
    imageOptionsActiveTab,
    isLinkEnabled,
    isLinkPopoverOpen,
    captionLinkText,
    captionLinkUrl,
    captionSelectedText 
  };
};

export const useImageOptionsActions = () => {
  const { 
    closeImageOptionsDialog,
    setImageOptionsActiveTab,
    setIsLinkEnabled,
    setIsLinkPopoverOpen,
    setCaptionLinkText,
    setCaptionLinkUrl,
    setCaptionSelectedText,
    handleImageOptionsInputChange,
    handleImageOptionsLinkToggle,
    handleImageOptionsSave,
    handleImageDelete 
  } = useRichEditorStore();
  return { 
    closeImageOptionsDialog,
    setImageOptionsActiveTab,
    setIsLinkEnabled,
    setIsLinkPopoverOpen,
    setCaptionLinkText,
    setCaptionLinkUrl,
    setCaptionSelectedText,
    handleImageOptionsInputChange,
    handleImageOptionsLinkToggle,
    handleImageOptionsSave,
    handleImageDelete 
  };
};
