import { useApiQuery } from "@/feature/core/api/api-utils";
import { BlogConfigResponse } from "@/types/blog-api";

// Blog Config API function
const blogConfigApi = {
  getConfig: async (): Promise<BlogConfigResponse> => {
    const response = await fetch("/api/blog/config");

    if (!response.ok) {
      throw new Error("Failed to fetch blog configuration");
    }

    const data = await response.json();
    return data.data;
  },
};

// React Query hook for blog config
export function useBlogConfig() {
  return useApiQuery(["blog-config"], () => blogConfigApi.getConfig(), {
    staleTime: 1000 * 60 * 10, // 10 minutes
    cacheTime: 1000 * 60 * 30, // 30 minutes
  });
}
