import {
  useApiMutation,
  useApiQuery,
  apiPost,
  apiGet,
  apiPut,
  apiDelete,
  ApiError,
} from "@/feature/core/api/api-utils";
import {
  CreateBlogPostRequest,
  UpdateBlogPostRequest,
  BlogPostResponse,
  PaginatedResponse,
  BlogPostQueryParams,
  BlogStatus,
} from "@/types/blog-api";
import { toast } from "sonner";

// Blog Post API functions
const blogPostApi = {
  getPosts: async (
    params?: BlogPostQueryParams
  ): Promise<PaginatedResponse<BlogPostResponse>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set("page", params.page.toString());
    if (params?.limit) searchParams.set("limit", params.limit.toString());
    if (params?.search) searchParams.set("search", params.search);
    if (params?.status) searchParams.set("status", params.status);
    if (params?.author_id) searchParams.set("author_id", params.author_id);
    if (params?.category_id !== undefined)
      searchParams.set("category_id", params.category_id.toString());
    if (params?.tag_id !== undefined)
      searchParams.set("tag_id", params.tag_id.toString());
    if (params?.published !== undefined)
      searchParams.set("published", params.published.toString());

    const queryString = searchParams.toString();
    const response = await apiGet<{
      data: PaginatedResponse<BlogPostResponse>;
    }>(`/api/blog/post${queryString ? `?${queryString}` : ""}`);
    return response.data;
  },

  getPost: async (id: string): Promise<BlogPostResponse> => {
    const response = await apiGet<{ data: BlogPostResponse }>(
      `/api/blog/post/${id}`
    );
    return response.data;
  },

  createPost: async (
    data: CreateBlogPostRequest
  ): Promise<BlogPostResponse> => {
    const response = await apiPost<{ data: BlogPostResponse }>(
      "/api/blog/post",
      data
    );
    return response.data;
  },

  updatePost: async (
    id: string,
    data: UpdateBlogPostRequest
  ): Promise<BlogPostResponse> => {
    const response = await apiPut<{ data: BlogPostResponse }>(
      `/api/blog/post/${id}`,
      data
    );
    return response.data;
  },

  deletePost: async (id: string): Promise<void> => {
    await apiDelete(`/api/blog/post/${id}`);
  },
};

// React Query hooks
export function useGetBlogPosts(params?: BlogPostQueryParams) {
  return useApiQuery(
    ["blog-posts", JSON.stringify(params)],
    () => blogPostApi.getPosts(params),
    {
      enabled: true,
      staleTime: 1000 * 60 * 5, // 5 minutes
    }
  );
}

export function useGetBlogPost(id: string) {
  return useApiQuery(["blog-post", id], () => blogPostApi.getPost(id), {
    enabled: !!id,
  });
}

export function useCreateBlogPost() {
  return useApiMutation(
    async (data: CreateBlogPostRequest): Promise<BlogPostResponse> => {
      return blogPostApi.createPost(data);
    },
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success("Blog post created successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to create blog post");
      },
    }
  );
}

export function useUpdateBlogPost() {
  return useApiMutation(
    async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateBlogPostRequest;
    }): Promise<BlogPostResponse> => {
      return blogPostApi.updatePost(id, data);
    },
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success("Blog post updated successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to update blog post");
      },
    }
  );
}

export function useDeleteBlogPost() {
  return useApiMutation(
    async (id: string): Promise<void> => {
      return blogPostApi.deletePost(id);
    },
    {
      showErrorToast: false,
      onSuccess: () => {
        toast.success("Blog post deleted successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to delete blog post");
      },
    }
  );
}

// Publish/Unpublish shortcuts
export function usePublishBlogPost() {
  return useApiMutation(
    async (id: string): Promise<BlogPostResponse> => {
      return blogPostApi.updatePost(id, {
        status: BlogStatus.PUBLISHED,
        published_at: new Date(),
      });
    },
    {
      showErrorToast: false,
      onSuccess: () => {
        toast.success("Blog post published successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to publish blog post");
      },
    }
  );
}

export function useUnpublishBlogPost() {
  return useApiMutation(
    async (id: string): Promise<BlogPostResponse> => {
      return blogPostApi.updatePost(id, {
        status: BlogStatus.DRAFT,
        published_at: undefined,
      });
    },
    {
      showErrorToast: false,
      onSuccess: () => {
        toast.success("Blog post unpublished successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to unpublish blog post");
      },
    }
  );
}

// Archive/Unarchive shortcuts
export function useArchiveBlogPost() {
  return useApiMutation(
    async (id: string): Promise<BlogPostResponse> => {
      return blogPostApi.updatePost(id, { status: BlogStatus.ARCHIVED });
    },
    {
      showErrorToast: false,
      onSuccess: () => {
        toast.success("Blog post archived successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to archive blog post");
      },
    }
  );
}

export function useUnarchiveBlogPost() {
  return useApiMutation(
    async (id: string): Promise<BlogPostResponse> => {
      return blogPostApi.updatePost(id, { status: BlogStatus.DRAFT });
    },
    {
      showErrorToast: false,
      onSuccess: () => {
        toast.success("Blog post unarchived successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to unarchive blog post");
      },
    }
  );
}
