"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useSession } from "next-auth/react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  SectionCard,
} from "@/components/ui/section-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import {
  Save,
  Eye,
  FileText,
  Image,
  Settings,
  Loader2,
  Calendar,
  X,
  User,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { BlogRichEditor } from "./blog-rich-editor";
import {
  useCreateBlogPost,
  useUpdateBlogPost,
  useGetBlogPost,
} from "../hooks/use-blog-post-data";
import { useBlogConfig } from "../hooks/use-blog-config";
import {
  CreateBlogPostRequest,
  UpdateBlogPostRequest,
  BlogStatus,
} from "@/types/blog-api";
import { Separator } from "@/components/ui/separator";
import { DateTimePicker } from "./date-time-picker";
import { BlogCategoryTagDialog } from "./blog-category-tag-dialog";
import { BlogAuthorDialog } from "./blog-author-dialog";
import { BlogMediaDialog } from "./blog-media-dialog";
import {
  useMediaDialogActions,
  useMediaDialogState,
  useRichEditorActions,
} from "../store/use-rich-editor-store";
import { cn } from "@/lib/utils";
import { FeaturedImageUpload } from "./featured-image-upload";

const createBlogPostSchema = z.object({
  title: z.string().min(1, "Title is required"),
  excerpt: z.string().optional(),
  content: z.string().min(1, "Content is required"),
  status: z.nativeEnum(BlogStatus),
  meta_title: z.string().optional(),
  meta_description: z.string().optional(),
  featured_image_url: z
    .string()
    .url("Must be a valid URL")
    .optional()
    .or(z.literal("")),
  category_ids: z.array(z.number()).optional(),
  tag_ids: z.array(z.number()).optional(),
  published_at: z.date().optional(),
  author_id: z.string().optional(),
});

type CreateBlogPostFormData = z.infer<typeof createBlogPostSchema>;

interface BlogPostFormProps {
  postId?: string;
  mode?: "add" | "edit";
  dict?: any;
  lang?: string;
}

export function BlogPostForm({
  postId,
  mode = "add",
  dict,
  lang,
}: BlogPostFormProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [selectedCategories, setSelectedCategories] = useState<number[]>([]);
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [selectedAuthorId, setSelectedAuthorId] = useState<string>("");
  const [categoryTagDialogOpen, setCategoryTagDialogOpen] = useState(false);
  const [authorDialogOpen, setAuthorDialogOpen] = useState(false);
  const isEditMode = mode === "edit" && postId;

  // Get current user info
  const currentUser = session?.user;
  const currentUserRole =
    (currentUser?.role as "ADMIN" | "AUTHOR" | "USER") || "USER";
  const currentUserId = currentUser?.id || "";

  // Media dialog store for featured image selection
  const { isMediaDialogOpen } = useMediaDialogState();
  const { openMediaDialog } = useRichEditorActions();
  const { closeMediaDialog, handleImageSelect } = useMediaDialogActions();

  const form = useForm<CreateBlogPostFormData>({
    resolver: zodResolver(createBlogPostSchema),
    defaultValues: {
      title: "",
      excerpt: "",
      content: "",
      status: BlogStatus.DRAFT,
      meta_title: "",
      meta_description: "",
      featured_image_url: "",
      category_ids: [],
      tag_ids: [],
      author_id: currentUserId,
    },
  });

  const createBlogPost = useCreateBlogPost();
  const updateBlogPost = useUpdateBlogPost();
  const { data: existingPost, isLoading: isLoadingPost } = useGetBlogPost(
    postId || ""
  );
  const { data: blogConfig, isLoading: isLoadingConfig } = useBlogConfig();

  // Load existing post data for edit mode
  useEffect(() => {
    if (isEditMode && existingPost && !isLoadingPost) {
      form.reset({
        title: existingPost.title,
        excerpt: existingPost.excerpt || "",
        content: existingPost.content,
        status: existingPost.status,
        meta_title: existingPost.meta_title || "",
        meta_description: existingPost.meta_description || "",
        featured_image_url: existingPost.featured_image_url || "",
        category_ids: existingPost.category_ids,
        tag_ids: existingPost.tag_ids,
        published_at: existingPost.published_at
          ? new Date(existingPost.published_at)
          : undefined,
        author_id: existingPost.author_id || currentUserId,
      });
      setSelectedCategories(existingPost.category_ids);
      setSelectedTags(existingPost.tag_ids);
      setSelectedAuthorId(existingPost.author_id || currentUserId);
    }
  }, [existingPost, isLoadingPost, isEditMode, form, currentUserId]);

  // Set default author when current user is available
  useEffect(() => {
    if (currentUserId && !isEditMode) {
      setSelectedAuthorId(currentUserId);
      form.setValue("author_id", currentUserId);
    }
  }, [currentUserId, isEditMode, form]);

  // Handle image selection from media dialog for featured image
  const handleFeaturedImageSelect = (url: string) => {
    form.setValue("featured_image_url", url);
    toast.success("Featured image selected successfully");
  };

  // Handle author selection change
  const handleAuthorChange = (authorId: string) => {
    setSelectedAuthorId(authorId);
    form.setValue("author_id", authorId);
  };

  const onSubmit = async (data: CreateBlogPostFormData) => {
    try {
      const postData = {
        ...data,
        category_ids: selectedCategories,
        tag_ids: selectedTags,
        featured_image_url: data.featured_image_url || undefined,
        meta_title: data.meta_title || undefined,
        meta_description: data.meta_description || undefined,
        excerpt: data.excerpt || undefined,
        author_id: selectedAuthorId || currentUserId,
      };

      if (isEditMode) {
        await updateBlogPost.mutateAsync({
          id: postId!,
          data: postData as UpdateBlogPostRequest,
        });
      } else {
        await createBlogPost.mutateAsync(postData as CreateBlogPostRequest);
      }

      router.push("/admin/blog");
    } catch (error) {
      // Error is handled by the mutation hooks
    }
  };

  const handleSaveAsDraft = () => {
    form.setValue("status", BlogStatus.DRAFT);
    form.handleSubmit(onSubmit)();
  };

  const handlePublish = () => {
    form.setValue("status", BlogStatus.PUBLISHED);
    form.handleSubmit(onSubmit)();
  };

  const handleCategoryTagSelectionChange = (
    categories: number[],
    tags: number[]
  ) => {
    setSelectedCategories(categories);
    setSelectedTags(tags);
    form.setValue("category_ids", categories);
    form.setValue("tag_ids", tags);
  };

  if (isEditMode && isLoadingPost) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading blog post...</span>
      </div>
    );
  }

  return (
    <div className="w-full max-w-none space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          {/* Layout with Independent Scrolling */}
          <div className="flex gap-6 h-[calc(100vh-100px)]">
            {/* Left Side - Main Content (75%) - Scrollable */}
            <div className="flex-1 w-3/4 overflow-y-auto  hide-scrollbar ">
              <div className="space-y-6 pb-6">
                {/* Title */}
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Textarea
                          placeholder="Add title"
                          {...field}
                          rows={1}
                          className="text-base font-bold px-0 resize-none overflow-hidden focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-muted-foreground/60 min-h-[8px] shadow-none leading-tight border-0 border-b border-b-gray-200 rounded-none"
                          onInput={(e) => {
                            const target = e.target as HTMLTextAreaElement;
                            target.style.height = "auto";
                            target.style.height = target.scrollHeight + "px";
                          }}
                          style={{ fontSize: "2rem", lineHeight: "1" }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Content Editor */}
                <FormField
                  control={form.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <BlogRichEditor
                          content={field.value}
                          onChange={field.onChange}
                          placeholder="Start writing your blog post..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Right Side - Settings Sidebar (25%) - Scrollable */}
            <div className="w-1/4 min-w-[300px] max-w-[350px] overflow-y-auto hide-scrollbar">
              <div className="space-y-4 pb-6">
                {/* Action Buttons */}
                <div className="flex flex-col gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleSaveAsDraft}
                    disabled={
                      createBlogPost.isPending || updateBlogPost.isPending
                    }
                    className="gap-2 w-full"
                  >
                    <Save className="h-4 w-4" />
                    Save as Draft
                  </Button>
                  <Button
                    type="button"
                    onClick={handlePublish}
                    disabled={
                      createBlogPost.isPending || updateBlogPost.isPending
                    }
                    className="gap-2 w-full"
                  >
                    {createBlogPost.isPending || updateBlogPost.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                    {isEditMode ? "Update & Publish" : "Publish"}
                  </Button>
                </div>

                {/* Publishing Settings */}
                <SectionCard
                  title="Publish"
                  titleIcon={<Settings className="h-4 w-4" />}
                >
                  <div className="space-y-3">
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-xs font-medium text-muted-foreground">
                            Status
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className="h-8 text-sm w-full">
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {blogConfig?.enums.blogStatuses.map(
                                (status: any) => (
                                  <SelectItem key={status} value={status}>
                                    {status.charAt(0) +
                                      status.slice(1).toLowerCase()}
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="published_at"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-xs font-medium text-muted-foreground">
                            Publish Date
                          </FormLabel>
                          <FormControl>
                            <DateTimePicker
                              value={field.value}
                              onChange={field.onChange}
                              placeholder="Immediately"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </SectionCard>

                {/* Author Selection */}
                <SectionCard title="Author">
                  <div className="space-y-3">
                    {currentUserRole === "ADMIN" ? (
                      <>
                        <BlogAuthorDialog
                          trigger={
                            <Button
                              variant="outline"
                              className="w-full justify-start text-left h-auto py-3"
                              type="button"
                            >
                              <div className="flex items-center gap-3 w-full">
                                <User className="h-4 w-4 text-muted-foreground" />
                                <div className="flex flex-col items-start gap-1 flex-1">
                                  <span className="text-sm font-medium">
                                    {selectedAuthorId
                                      ? "Change Author"
                                      : "Select Author"}
                                  </span>
                                  <span className="text-xs text-muted-foreground">
                                    {selectedAuthorId
                                      ? blogConfig?.authors?.find(
                                          (a) => a.id === selectedAuthorId
                                        )?.name ||
                                        blogConfig?.authors?.find(
                                          (a) => a.id === selectedAuthorId
                                        )?.email ||
                                        "Selected author"
                                      : "No author selected"}
                                  </span>
                                </div>
                              </div>
                            </Button>
                          }
                          authors={blogConfig?.authors || []}
                          selectedAuthorId={selectedAuthorId}
                          onAuthorChange={handleAuthorChange}
                          open={authorDialogOpen}
                          onOpenChange={setAuthorDialogOpen}
                          currentUserRole={currentUserRole}
                          currentUserId={currentUserId}
                        />

                        {/* Selected Author Display */}
                        {selectedAuthorId && (
                          <div className="p-3 border rounded-lg bg-muted/20">
                            {(() => {
                              const selectedAuthor = blogConfig?.authors?.find(
                                (a) => a.id === selectedAuthorId
                              );
                              return selectedAuthor ? (
                                <div className="flex items-center gap-3">
                                  <Avatar className="h-8 w-8">
                                    {selectedAuthor.image && (
                                      <AvatarImage src={selectedAuthor.image} />
                                    )}
                                    <AvatarFallback className="text-xs">
                                      {selectedAuthor.name?.charAt(0) ||
                                        selectedAuthor.email.charAt(0)}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium truncate">
                                      {selectedAuthor.name || "No name"}
                                    </p>
                                    <p className="text-xs text-muted-foreground truncate">
                                      {selectedAuthor.email}
                                    </p>
                                  </div>
                                  <Badge
                                    variant={
                                      selectedAuthor.role === "ADMIN"
                                        ? "default"
                                        : "secondary"
                                    }
                                    className="text-xs h-5"
                                  >
                                    {selectedAuthor.role}
                                  </Badge>
                                  {selectedAuthor.id === currentUserId && (
                                    <Badge
                                      variant="outline"
                                      className="text-xs h-5"
                                    >
                                      You
                                    </Badge>
                                  )}
                                </div>
                              ) : (
                                <p className="text-sm text-muted-foreground">
                                  Author not found
                                </p>
                              );
                            })()}
                          </div>
                        )}
                      </>
                    ) : (
                      /* Author Display for non-ADMIN users */
                      <div className="p-3 border rounded-lg bg-muted/20">
                        {(() => {
                          const currentAuthor = blogConfig?.authors?.find(
                            (a) => a.id === currentUserId
                          );
                          return currentAuthor ? (
                            <div className="flex items-center gap-3">
                              <Avatar className="h-8 w-8">
                                {currentAuthor.image && (
                                  <AvatarImage src={currentAuthor.image} />
                                )}
                                <AvatarFallback className="text-xs">
                                  {currentAuthor.name?.charAt(0) ||
                                    currentAuthor.email.charAt(0)}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">
                                  {currentAuthor.name || "No name"}
                                </p>
                                <p className="text-xs text-muted-foreground truncate">
                                  {currentAuthor.email}
                                </p>
                              </div>
                              <Badge
                                variant={
                                  currentAuthor.role === "ADMIN"
                                    ? "default"
                                    : "secondary"
                                }
                                className="text-xs h-5"
                              >
                                {currentAuthor.role}
                              </Badge>
                              <Badge variant="outline" className="text-xs h-5">
                                You
                              </Badge>
                            </div>
                          ) : (
                            <div className="flex items-center gap-3">
                              <User className="h-8 w-8 text-muted-foreground" />
                              <div className="flex-1">
                                <p className="text-sm font-medium">You</p>
                                <p className="text-xs text-muted-foreground">
                                  {currentUser?.email || "Current user"}
                                </p>
                              </div>
                            </div>
                          );
                        })()}
                      </div>
                    )}
                  </div>
                </SectionCard>

                {/* Categories & Tags */}
                <SectionCard title="Categories & Tags">
                  <div className="space-y-3">
                    <BlogCategoryTagDialog
                      trigger={
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left h-auto py-3"
                          type="button"
                        >
                          <div className="flex flex-col items-start gap-1 w-full">
                            <span className="text-sm font-medium">
                              Select Categories & Tags
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {selectedCategories.length > 0 ||
                              selectedTags.length > 0
                                ? `${selectedCategories.length} categories, ${selectedTags.length} tags selected`
                                : "No categories or tags selected"}
                            </span>
                          </div>
                        </Button>
                      }
                      categories={blogConfig?.categories || []}
                      tags={blogConfig?.tags || []}
                      selectedCategories={selectedCategories}
                      selectedTags={selectedTags}
                      onSelectionChange={handleCategoryTagSelectionChange}
                      open={categoryTagDialogOpen}
                      onOpenChange={setCategoryTagDialogOpen}
                    />

                    {/* Selected items summary */}
                    {(selectedCategories.length > 0 ||
                      selectedTags.length > 0) && (
                      <div className="flex flex-wrap gap-1 pt-2 border-t">
                        {selectedCategories.length > 0 && (
                          <Badge variant="secondary" className="text-xs h-5">
                            {selectedCategories.length} categories
                          </Badge>
                        )}
                        {selectedTags.length > 0 && (
                          <Badge variant="secondary" className="text-xs h-5">
                            {selectedTags.length} tags
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                </SectionCard>

                {/* Excerpt */}
                <SectionCard title="Excerpt">
                  <FormField
                    control={form.control}
                    name="excerpt"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Textarea
                            placeholder="Write an excerpt..."
                            rows={5}
                            {...field}
                            className="text-sm resize-none"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </SectionCard>

                {/* Featured Image */}
                <SectionCard
                  title="Featured Image"
                  titleIcon={<Image className="h-4 w-4" />}
                >
                  <FormField
                    control={form.control}
                    name="featured_image_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div className="space-y-3">
                            <Button
                              variant="outline"
                              className="w-full justify-start text-left h-auto py-3"
                              type="button"
                              onClick={() =>
                                openMediaDialog(
                                  "featured_image",
                                  handleFeaturedImageSelect
                                )
                              }
                            >
                              <div className="flex flex-col items-start gap-1 w-full">
                                <span className="text-sm font-medium">
                                  {field.value
                                    ? "Change Featured Image"
                                    : "Set Featured Image"}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  {field.value
                                    ? "Featured image selected"
                                    : "No featured image selected"}
                                </span>
                              </div>
                            </Button>

                            {/* BlogMediaDialog without props - it uses Zustand store */}
                            {isMediaDialogOpen && <BlogMediaDialog />}

                            {/* Image Preview */}
                            {field.value && (
                              <div className="relative">
                                <img
                                  src={field.value}
                                  alt="Featured Image"
                                  className="w-full h-32 object-cover rounded-lg border"
                                />
                                <Button
                                  type="button"
                                  variant="destructive"
                                  size="sm"
                                  className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                                  onClick={() => field.onChange("")}
                                >
                                  <X className="h-3 w-3 text-white" />
                                </Button>
                              </div>
                            )}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </SectionCard>

                {/* SEO Meta Data */}
                <SectionCard title="SEO Settings">
                  <div className="space-y-3">
                    <FormField
                      control={form.control}
                      name="meta_title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-xs font-medium text-muted-foreground">
                            Meta Title
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="SEO title"
                              {...field}
                              className="text-sm h-8"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="meta_description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-xs font-medium text-muted-foreground">
                            Meta Description
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="SEO description"
                              rows={3}
                              {...field}
                              className="text-sm resize-none"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </SectionCard>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
