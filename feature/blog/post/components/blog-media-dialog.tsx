"use client";

import React, { use<PERSON><PERSON>back, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogFooter,
  CustomDialogTrigger,
} from "@/components/custom/custom-dialog";
import {
  Upload,
  Image,
  Grid3X3,
  List,
  Search,
  Folder,
  ArrowLeft,
  Check,
  X,
  FileImage,
  Loader2,
  Copy,
  Info,
  Edit3,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { MediaItem, MediaFileType, MediaMetadata } from "@/types/media-api";
import {
  useGetMedia,
  useUploadMedia,
  useUploadMultipleFiles,
  useGetMediaMetadata,
  useUpdateMediaMetadata,
  useCreateMediaMetadata,
} from "@/feature/media/hooks/use-media-data";
import { toast } from "sonner";
// Import Zustand store hooks
import {
  useMediaDialogState,
  useMediaDialogActions,
} from "@/feature/blog/post/store/use-rich-editor-store";

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

export function BlogMediaDialog() {
  // Use Zustand store instead of props
  const {
    isMediaDialogOpen,
    activeTab,
    currentPath,
    viewMode,
    searchQuery,
    typeFilter,
    tempSelectedUrl,
    selectedItem,
    isDragOver,
    uploadingFiles,
    metadataForm,
    selectionMode,
    onImageSelected,
  } = useMediaDialogState();

  const {
    closeMediaDialog,
    setActiveTab,
    setCurrentPath,
    setViewMode,
    setSearchQuery,
    setTempSelectedUrl,
    setSelectedItem,
    setIsDragOver,
    setUploadingFiles,
    setMetadataForm,
    handleItemClick,
    handleImageSelect,
  } = useMediaDialogActions();

  // Fetch media data
  const {
    data: mediaData,
    isLoading,
    refetch,
  } = useGetMedia({
    path: currentPath,
    limit: 100,
  });

  // Hooks for metadata
  const updateMetadata = useUpdateMediaMetadata();
  const createMetadata = useCreateMediaMetadata();
  const { data: metadataData } = useGetMediaMetadata(selectedItem?.key || "");

  // Load metadata when item is selected
  useEffect(() => {
    if (selectedItem && metadataData?.success) {
      const metadata = metadataData.data;
      setMetadataForm({
        title: metadata.title || "",
        alt_text: metadata.alt_text || "",
        caption: metadata.caption || "",
        description: metadata.description || "",
      });
    } else if (selectedItem) {
      // Reset form if no metadata
      setMetadataForm({
        title: selectedItem.name?.replace(/\.[^/.]+$/, "") || "",
        alt_text: "",
        caption: "",
        description: "",
      });
    }
  }, [selectedItem, metadataData, setMetadataForm]);

  const uploadMutation = useUploadMedia();
  const uploadMultipleMutation = useUploadMultipleFiles();

  // Filter items based on current filter and search
  const filteredItems = React.useMemo(() => {
    if (!mediaData?.data?.items) return [];

    let items = mediaData.data.items.filter((item) => {
      // Only show images for featured image selection
      if (item.type === "file" && item.file_type !== "image") return false;
      return true;
    });

    // Apply search filter
    if (searchQuery) {
      const searchTerm = searchQuery.toLowerCase();
      items = items.filter((item) =>
        item.name.toLowerCase().includes(searchTerm)
      );
    }

    // Sort: folders first, then files by date
    items.sort((a, b) => {
      if (a.type === "folder" && b.type === "file") return -1;
      if (a.type === "file" && b.type === "folder") return 1;

      if (a.last_modified && b.last_modified) {
        return (
          new Date(b.last_modified).getTime() -
          new Date(a.last_modified).getTime()
        );
      }

      return a.name.localeCompare(b.name);
    });

    return items;
  }, [mediaData?.data?.items, searchQuery]);

  const handleFileSelect = useCallback(
    async (files: FileList | null) => {
      if (!files || files.length === 0) return;

      const imageFiles = Array.from(files).filter((file) =>
        file.type.startsWith("image/")
      );

      if (imageFiles.length === 0) {
        toast.error("Please select only image files");
        return;
      }

      // Validate file sizes (10MB limit for featured images)
      const oversizedFiles = imageFiles.filter(
        (file) => file.size > 10 * 1024 * 1024
      );
      if (oversizedFiles.length > 0) {
        toast.error("Some files exceed the 10MB limit for featured images");
        return;
      }

      setUploadingFiles(imageFiles);

      try {
        if (imageFiles.length === 1) {
          const result = await uploadMutation.mutateAsync({
            file: imageFiles[0],
            folder_path: "blog/content-images",
          });

          if (result.success && result.data.url) {
            // Create metadata for uploaded file
            await createMetadata.mutateAsync({
              file_key: result.data.key,
              file_name: result.data.file_name,
              original_name: imageFiles[0].name,
              file_url: result.data.url,
              file_size: result.data.file_size,
              mime_type: result.data.mime_type,
              file_type: "image",
              folder_path: "blog/content-images",
              title: imageFiles[0].name.replace(/\.[^/.]+$/, ""), // Remove extension
              alt_text: "",
            });

            setTempSelectedUrl(result.data.url);
            setActiveTab("library");
            refetch();
          }
        } else {
          const results = await uploadMultipleMutation.mutateAsync({
            files: imageFiles,
            folder_path: "blog/content-images",
          });

          // Create metadata for each uploaded file
          for (let i = 0; i < results.length; i++) {
            const result = results[i];
            const file = imageFiles[i];
            if (result.success) {
              await createMetadata.mutateAsync({
                file_key: result.data.key,
                file_name: result.data.file_name,
                original_name: file.name,
                file_url: result.data.url,
                file_size: result.data.file_size,
                mime_type: result.data.mime_type,
                file_type: "image",
                folder_path: "blog/content-images",
                title: file.name.replace(/\.[^/.]+$/, ""),
                alt_text: "",
              });
            }
          }

          if (results.length > 0) {
            setActiveTab("library");
            refetch();
          }
        }
      } catch (error) {
        console.error("Upload failed:", error);
      } finally {
        setUploadingFiles([]);
      }
    },
    [
      uploadMutation,
      uploadMultipleMutation,
      refetch,
      setUploadingFiles,
      setTempSelectedUrl,
      setActiveTab,
    ]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
      handleFileSelect(e.dataTransfer.files);
    },
    [handleFileSelect, setIsDragOver]
  );

  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(true);
    },
    [setIsDragOver]
  );

  const handleDragLeave = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
    },
    [setIsDragOver]
  );

  const handleMetadataUpdate = async () => {
    if (!selectedItem) return;

    try {
      await updateMetadata.mutateAsync({
        file_key: selectedItem.key,
        ...metadataForm,
      });
      // toast.success("Image details updated successfully");
    } catch (error) {
      console.error("Failed to update metadata:", error);
      // Error is handled by the hook
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard");
  };

  const handleApply = () => {
    if (tempSelectedUrl) {
      handleImageSelect(tempSelectedUrl, {
        title: metadataForm.title,
        alt_text: metadataForm.alt_text,
        caption: metadataForm.caption,
        description: metadataForm.description,
      });
      closeMediaDialog();
    }
  };

  const handleCancel = () => {
    closeMediaDialog();
  };

  const pathParts = currentPath.split("/").filter(Boolean);

  return (
    <CustomDialog open={isMediaDialogOpen} onOpenChange={closeMediaDialog}>
      <CustomDialogContent className="max-w-5xl w-full max-h-[90vh] p-0">
        <CustomDialogHeader>
          <CustomDialogTitle className="flex items-center gap-2 py-2">
            <FileImage className="h-5 w-5" />
            {selectionMode === "featured_image"
              ? "Select Featured Image"
              : "Media Library"}
          </CustomDialogTitle>
        </CustomDialogHeader>

        <div className="flex h-[70vh]">
          {/* Left Panel - Media Browser */}
          <div className="flex-1 flex flex-col border-r">
            {/* Tabs */}
            <Tabs
              value={activeTab}
              onValueChange={(value) =>
                setActiveTab(value as "upload" | "library")
              }
              className="w-full"
            >
              <div className="border-b px-4 py-2">
                <TabsList className="grid w-48 grid-cols-2">
                  <TabsTrigger value="upload">Upload</TabsTrigger>
                  <TabsTrigger value="library">Library </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="upload" className="flex-1 p-4">
                <div className="space-y-4">
                  {/* Upload Zone */}
                  <div
                    className={cn(
                      "border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer",
                      isDragOver
                        ? "border-primary bg-primary/5"
                        : "border-muted-foreground/25 hover:border-primary/50"
                    )}
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onClick={() => {
                      const input = document.createElement("input");
                      input.type = "file";
                      input.accept = "image/*";
                      input.multiple = true;
                      input.onchange = (e) => {
                        const target = e.target as HTMLInputElement;
                        handleFileSelect(target.files);
                      };
                      input.click();
                    }}
                  >
                    <div className="flex flex-col items-center gap-4">
                      <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center">
                        <Upload className="w-8 h-8 text-muted-foreground" />
                      </div>
                      <div>
                        <p className="text-lg font-medium">
                          {selectionMode === "featured_image"
                            ? "Upload Featured Image"
                            : "Drop files to upload"}
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">
                          {selectionMode === "featured_image"
                            ? "Click to select featured image (max 10MB)"
                            : "or click to select files (Images only, max 10MB each)"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Upload Progress */}
                  {uploadingFiles.length > 0 && (
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Uploading files...</p>
                      {uploadingFiles.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 p-2 bg-muted rounded"
                        >
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="text-sm">{file.name}</span>
                          <span className="text-xs text-muted-foreground">
                            ({formatFileSize(file.size)})
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="library" className="flex-1 flex flex-col">
                {/* Toolbar */}
                <div className="border-b p-4 space-y-3">
                  {/* Navigation */}
                  {currentPath && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setCurrentPath("")}
                        className="h-6 px-1"
                      >
                        <ArrowLeft className="h-3 w-3" />
                      </Button>
                      <span>Media Library</span>
                      {pathParts.map((part, index) => (
                        <React.Fragment key={index}>
                          <span>/</span>
                          <span>{part}</span>
                        </React.Fragment>
                      ))}
                    </div>
                  )}

                  {/* Search and Filters */}
                  <div className="flex items-center gap-2">
                    <div className="relative flex-1 max-w-sm">
                      <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
                      <Input
                        placeholder={
                          selectionMode === "featured_image"
                            ? "Search for featured image..."
                            : "Search images..."
                        }
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-7 h-8 text-sm"
                      />
                    </div>

                    <div className="flex items-center gap-1">
                      <Button
                        variant={viewMode === "grid" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setViewMode("grid")}
                        className="h-8 w-8 p-0"
                      >
                        <Grid3X3 className="h-3 w-3" />
                      </Button>
                      <Button
                        variant={viewMode === "list" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setViewMode("list")}
                        className="h-8 w-8 p-0"
                      >
                        <List className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Media Grid/List */}
                <div className="flex-1 overflow-y-auto p-4 custom-scrollbar">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-32">
                      <Loader2 className="h-8 w-8 animate-spin" />
                      <span className="ml-2">Loading media...</span>
                    </div>
                  ) : filteredItems.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Image className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>
                        {selectionMode === "featured_image"
                          ? "No featured images found"
                          : "No images found"}
                      </p>
                      {searchQuery && (
                        <p className="text-sm mt-1">
                          Try a different search term
                        </p>
                      )}
                    </div>
                  ) : viewMode === "grid" ? (
                    <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2">
                      {filteredItems.map((item) => (
                        <div
                          key={item.key}
                          className={cn(
                            "relative aspect-square rounded-lg overflow-hidden cursor-pointer border-2 transition-all hover:shadow-md",
                            item.type === "folder"
                              ? "bg-muted border-muted-foreground/20"
                              : tempSelectedUrl === item.url
                                ? "border-primary bg-primary/5"
                                : "border-transparent hover:border-muted-foreground/30"
                          )}
                          onClick={() => handleItemClick(item)}
                        >
                          {item.type === "folder" ? (
                            <div className="flex items-center justify-center h-full">
                              <Folder className="h-8 w-8 text-muted-foreground" />
                            </div>
                          ) : (
                            <>
                              <img
                                src={item.url}
                                alt={item.name}
                                className="w-full h-full object-cover"
                              />
                              {tempSelectedUrl === item.url && (
                                <div className="absolute inset-0 bg-primary/20 flex items-center justify-center">
                                  <div className="bg-primary text-primary-foreground rounded-full p-1">
                                    <Check className="h-3 w-3" />
                                  </div>
                                </div>
                              )}
                            </>
                          )}
                          <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-1">
                            <p className="text-xs truncate">{item.name}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {filteredItems.map((item) => (
                        <div
                          key={item.key}
                          className={cn(
                            "flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-colors",
                            tempSelectedUrl === item.url
                              ? "bg-primary/10 border border-primary/20"
                              : "hover:bg-muted/50"
                          )}
                          onClick={() => handleItemClick(item)}
                        >
                          {item.type === "folder" ? (
                            <Folder className="h-8 w-8 text-muted-foreground flex-shrink-0" />
                          ) : (
                            <img
                              src={item.url}
                              alt={item.name}
                              className="w-8 h-8 object-cover rounded flex-shrink-0"
                            />
                          )}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">
                              {item.name}
                            </p>
                            {item.type === "file" && (
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <span>{formatFileSize(item.size || 0)}</span>
                                {item.last_modified && (
                                  <span>{formatDate(item.last_modified)}</span>
                                )}
                              </div>
                            )}
                          </div>
                          {tempSelectedUrl === item.url && (
                            <Check className="h-4 w-4 text-primary flex-shrink-0" />
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Right Panel - Metadata Editor */}
          <div className="w-80 flex flex-col">
            {selectedItem ? (
              <div className="flex-1 overflow-y-auto custom-scrollbar">
                {/* Image Preview */}
                <div className="p-4 border-b">
                  <div className="aspect-square rounded-lg overflow-hidden bg-muted mb-3">
                    <img
                      src={selectedItem.url}
                      alt={selectedItem.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h3 className="font-medium text-sm truncate">
                    {selectedItem.name}
                  </h3>
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(selectedItem.size || 0)}
                  </p>
                </div>

                {/* Metadata Form */}
                <div className="p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium flex items-center gap-2">
                      <Edit3 className="h-4 w-4" />
                      Image Details
                    </h4>
                    <Button
                      size="sm"
                      onClick={handleMetadataUpdate}
                      disabled={updateMetadata.isPending}
                    >
                      {updateMetadata.isPending ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        "Save"
                      )}
                    </Button>
                  </div>

                  <div className="space-y-3">
                    {/* Title */}
                    <div>
                      <Label htmlFor="title" className="text-xs font-medium">
                        Title
                      </Label>
                      <Input
                        id="title"
                        value={metadataForm.title}
                        onChange={(e) =>
                          setMetadataForm({
                            title: e.target.value,
                          })
                        }
                        placeholder="Enter image title"
                        className="mt-1"
                      />
                    </div>

                    {/* Alt Text */}
                    <div>
                      <Label htmlFor="alt-text" className="text-xs font-medium">
                        Alt Text
                      </Label>
                      <Input
                        id="alt-text"
                        value={metadataForm.alt_text}
                        onChange={(e) =>
                          setMetadataForm({
                            alt_text: e.target.value,
                          })
                        }
                        placeholder="Describe the image"
                        className="mt-1"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Used for accessibility and SEO
                      </p>
                    </div>

                    {/* Caption */}
                    <div>
                      <Label htmlFor="caption" className="text-xs font-medium">
                        Caption
                      </Label>
                      <Textarea
                        id="caption"
                        value={metadataForm.caption}
                        onChange={(e) =>
                          setMetadataForm({
                            caption: e.target.value,
                          })
                        }
                        placeholder="Image caption"
                        rows={2}
                        className="mt-1"
                      />
                    </div>

                    {/* Description */}
                    <div>
                      <Label
                        htmlFor="description"
                        className="text-xs font-medium"
                      >
                        Description
                      </Label>
                      <Textarea
                        id="description"
                        value={metadataForm.description}
                        onChange={(e) =>
                          setMetadataForm({
                            description: e.target.value,
                          })
                        }
                        placeholder="Detailed description"
                        rows={3}
                        className="mt-1"
                      />
                    </div>

                    {/* File URL */}
                    <div>
                      <Label className="text-xs font-medium">File URL</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Input
                          value={selectedItem.url || ""}
                          readOnly
                          className="flex-1 text-xs"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() =>
                            copyToClipboard(selectedItem.url || "")
                          }
                          className="px-2"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex-1 flex items-center justify-center p-4">
                <div className="text-center text-muted-foreground">
                  <Info className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">
                    {selectionMode === "featured_image"
                      ? "Select an image to set as featured image"
                      : "Select an image to edit details"}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        <CustomDialogFooter className="border-t p-4 flex-row justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {tempSelectedUrl && (
              <Badge variant="secondary">1 image selected</Badge>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleApply} disabled={!tempSelectedUrl}>
              {selectionMode === "featured_image"
                ? "Set as Featured Image"
                : "Select Image"}
            </Button>
          </div>
        </CustomDialogFooter>
      </CustomDialogContent>
    </CustomDialog>
  );
}
