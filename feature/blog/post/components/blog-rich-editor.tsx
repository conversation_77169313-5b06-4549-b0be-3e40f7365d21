"use client";

import React, { useRef, useEffect } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Underline from "@tiptap/extension-underline";
import Link from "@tiptap/extension-link";
import Image from "@tiptap/extension-image";
import CharacterCount from "@tiptap/extension-character-count";
import { Node, mergeAttributes } from "@tiptap/core";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { BlogLinkDialog } from "./blog-link-dialog";
import { BlogMediaDialog } from "./blog-media-dialog";
import { BlogMediaImageOptionsDialog } from "./blog-media-image-options-dialog";
import { EnhancedCodeBlock } from "./code-block-extension";
// Import Zustand store hooks
import {
  useRichEditorState,
  useRichEditorActions,
  useRichEditorStore,
} from "@/feature/blog/post/store/use-rich-editor-store";
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Code,
  Code2,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Link as LinkIcon,
  Image as ImageIcon,
  Quote,
  Minus,
  RotateCcw,
  Eye,
  EyeOff,
  Type,
  Sparkles,
  Youtube,
  Twitter,
} from "lucide-react";

// YouTube URL extraction utility
const extractYouTubeId = (url: string): string | null => {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/,
    /youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/,
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) return match[1];
  }
  return null;
};

// Twitter URL extraction utility
const extractTwitterData = (
  url: string
): { tweetId: string; username: string } | null => {
  const patterns = [/(?:twitter\.com|x\.com)\/(\w+)\/status\/(\d+)/];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return {
        username: match[1],
        tweetId: match[2],
      };
    }
  }
  return null;
};

// YouTube Embed Extension
declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    imageWithCaption: {
      setImageWithCaption: (options: {
        src: string;
        alt?: string;
        title?: string;
        caption?: string;
        link?: string;
        linkTarget?: string;
      }) => ReturnType;
    };
    youtubeEmbed: {
      setYouTubeEmbed: (options: {
        videoId: string;
        startTime?: number;
      }) => ReturnType;
    };
    twitterEmbed: {
      setTwitterEmbed: (options: {
        tweetId: string;
        username: string;
        tweetUrl?: string;
        embedHtml?: string;
        isLoading?: boolean;
        hasError?: boolean;
      }) => ReturnType;
    };
  }
}

const YouTubeEmbed = Node.create({
  name: "youtubeEmbed",
  group: "block",
  content: "",
  marks: "",
  atom: true,

  addAttributes() {
    return {
      videoId: {
        default: null,
      },
      startTime: {
        default: 0,
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "div[data-youtube-video]",
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    const { videoId, startTime } = HTMLAttributes;

    return [
      "div",
      mergeAttributes(HTMLAttributes, {
        "data-youtube-video": videoId,
        class: "youtube-embed-wrapper",
      }),
      [
        "div",
        {
          class: "youtube-embed-container",
          style:
            "position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden;",
        },
        [
          "iframe",
          {
            src: `https://www.youtube.com/embed/${videoId}${startTime ? `?start=${startTime}` : ""}`,
            frameborder: "0",
            allow:
              "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",
            allowfullscreen: true,
            style:
              "position: absolute; top: 0; left: 0; width: 100%; height: 100%;",
          },
        ],
      ],
    ];
  },

  addCommands() {
    return {
      setYouTubeEmbed:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          });
        },
    };
  },
});

// Twitter Embed Extension
const TwitterEmbed = Node.create({
  name: "twitterEmbed",
  group: "block",
  content: "",
  marks: "",
  atom: true,

  addAttributes() {
    return {
      tweetId: {
        default: null,
      },
      username: {
        default: null,
      },
      tweetUrl: {
        default: null,
      },
      embedHtml: {
        default: null,
      },
      isLoading: {
        default: true,
      },
      hasError: {
        default: false,
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "div[data-twitter-tweet]",
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    const { tweetId, username, tweetUrl, embedHtml, isLoading, hasError } =
      HTMLAttributes;

    // If we have embed HTML, use it
    if (embedHtml && !isLoading && !hasError) {
      return [
        "div",
        mergeAttributes(HTMLAttributes, {
          "data-twitter-tweet": tweetId,
          class: "twitter-embed-wrapper",
        }),
        [
          "div",
          {
            class: "twitter-embed-container",
            style: "max-width: 550px; margin: 20px auto;",
          },
          [
            "div",
            {
              class: "twitter-embed-content",
              "data-embed-html": embedHtml,
              "data-embed-id": tweetId,
            },
          ],
        ],
      ];
    }

    // Loading state
    if (isLoading) {
      return [
        "div",
        mergeAttributes(HTMLAttributes, {
          "data-twitter-tweet": tweetId,
          class: "twitter-embed-wrapper",
        }),
        [
          "div",
          {
            class: "twitter-embed-container",
            style:
              "max-width: 550px; margin: 20px auto; border: 1px solid #e1e8ed; border-radius: 12px; overflow: hidden; background: white;",
          },
          [
            "div",
            {
              class: "twitter-embed-loading",
              style: "padding: 40px 20px; text-align: center; color: #657786;",
            },
            [
              "div",
              {
                style: "margin-bottom: 12px;",
              },
              [
                "svg",
                {
                  width: "24",
                  height: "24",
                  viewBox: "0 0 24 24",
                  fill: "#1da1f2",
                  style:
                    "display: inline-block; animation: spin 1s linear infinite;",
                },
                [
                  "path",
                  {
                    d: "M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z",
                  },
                ],
              ],
            ],
            [
              "div",
              {
                style: "font-size: 14px;",
              },
              "Loading tweet...",
            ],
          ],
        ],
      ];
    }

    // Error state
    if (hasError) {
      return [
        "div",
        mergeAttributes(HTMLAttributes, {
          "data-twitter-tweet": tweetId,
          class: "twitter-embed-wrapper",
        }),
        [
          "div",
          {
            class: "twitter-embed-container",
            style:
              "max-width: 550px; margin: 20px auto; border: 1px solid #e1e8ed; border-radius: 12px; overflow: hidden; background: white;",
          },
          [
            "div",
            {
              class: "twitter-embed-error",
              style: "padding: 20px; text-align: center; color: #657786;",
            },
            [
              "div",
              {
                style: "margin-bottom: 12px;",
              },
              [
                "svg",
                {
                  width: "24",
                  height: "24",
                  viewBox: "0 0 24 24",
                  fill: "#e74c3c",
                  style: "display: inline-block;",
                },
                [
                  "path",
                  {
                    d: "M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z",
                  },
                ],
              ],
            ],
            [
              "div",
              {
                style: "font-weight: bold; margin-bottom: 8px;",
              },
              "Failed to load tweet",
            ],
            [
              "div",
              {
                style: "font-size: 14px; margin-bottom: 12px;",
              },
              "This tweet may be private or deleted.",
            ],
            [
              "a",
              {
                href:
                  tweetUrl ||
                  `https://twitter.com/${username}/status/${tweetId}`,
                target: "_blank",
                rel: "noopener noreferrer",
                style:
                  "color: #1da1f2; text-decoration: none; font-size: 14px;",
              },
              "View on Twitter",
            ],
          ],
        ],
      ];
    }

    // Fallback placeholder
    return [
      "div",
      mergeAttributes(HTMLAttributes, {
        "data-twitter-tweet": tweetId,
        class: "twitter-embed-wrapper",
      }),
      [
        "div",
        {
          class: "twitter-embed-container",
          style:
            "max-width: 550px; margin: 20px auto; border: 1px solid #e1e8ed; border-radius: 12px; overflow: hidden; background: white;",
        },
        [
          "div",
          {
            class: "twitter-embed-placeholder",
            style: "padding: 20px; text-align: center; color: #657786;",
          },
          [
            "div",
            {
              style: "margin-bottom: 12px;",
            },
            [
              "svg",
              {
                width: "24",
                height: "24",
                viewBox: "0 0 24 24",
                fill: "#1da1f2",
                style: "display: inline-block;",
              },
              [
                "path",
                {
                  d: "M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z",
                },
              ],
            ],
          ],
          [
            "div",
            {
              style: "font-weight: bold; margin-bottom: 8px;",
            },
            `Tweet from @${username}`,
          ],
          [
            "div",
            {
              style: "font-size: 14px; margin-bottom: 12px;",
            },
            "This tweet will be displayed here when published.",
          ],
          [
            "a",
            {
              href:
                tweetUrl || `https://twitter.com/${username}/status/${tweetId}`,
              target: "_blank",
              rel: "noopener noreferrer",
              style: "color: #1da1f2; text-decoration: none; font-size: 14px;",
            },
            "View on Twitter",
          ],
        ],
      ],
    ];
  },

  addCommands() {
    return {
      setTwitterEmbed:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          });
        },
    };
  },
});

// Custom Image Extension with Caption and Link support
const ImageWithCaption = Node.create({
  name: "imageWithCaption",

  group: "block",

  content: "",

  marks: "",

  atom: true,

  addAttributes() {
    return {
      src: {
        default: null,
      },
      alt: {
        default: null,
      },
      title: {
        default: null,
      },
      caption: {
        default: null,
      },
      link: {
        default: null,
      },
      linkTarget: {
        default: "_blank",
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "div[data-type='image-with-caption']",
      },
      {
        tag: "figure",
        getAttrs: (node) => {
          const figure = node as HTMLElement;

          // Find the img element
          const img = figure.querySelector("img");
          if (!img) return false;

          // Find the link element if it exists
          const link = figure.querySelector("a");

          // Find the caption element if it exists
          const caption = figure.querySelector("figcaption");

          // Use data attributes as primary source, fall back to DOM inspection
          return {
            src: figure.getAttribute("data-src") || img.getAttribute("src"),
            alt:
              figure.getAttribute("data-alt") || img.getAttribute("alt") || "",
            title:
              figure.getAttribute("data-title") ||
              img.getAttribute("title") ||
              "",
            caption:
              figure.getAttribute("data-caption") ||
              caption?.innerHTML ||
              caption?.textContent ||
              "",
            link:
              figure.getAttribute("data-link") ||
              link?.getAttribute("href") ||
              "",
            linkTarget:
              figure.getAttribute("data-link-target") ||
              link?.getAttribute("target") ||
              "_blank",
          };
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    const { src, alt, title, caption, link, linkTarget } = HTMLAttributes;

    const imageElement = [
      "img",
      {
        src,
        alt: alt || "",
        title: title || "",
        class: "editor-image",
        draggable: false,
      },
    ];

    const imageWrapper =
      link && link.trim()
        ? [
            "a",
            {
              href: link,
              target: linkTarget || "_blank",
              rel: "noopener noreferrer",
            },
            imageElement,
          ]
        : imageElement;

    const captionElement =
      caption && caption.trim()
        ? [
            "figcaption",
            {
              class: "image-caption",
              contenteditable: "false",
              "data-html-content": caption, // Store HTML content as data attribute
            },
            caption, // For now, keep as text (will be handled by CSS/JS)
          ]
        : null;

    const elements = [imageWrapper];
    if (captionElement) {
      elements.push(captionElement);
    }

    return [
      "figure",
      mergeAttributes(HTMLAttributes, {
        "data-type": "image-with-caption",
        class: "image-figure",
        "data-src": src,
        "data-alt": alt || "",
        "data-title": title || "",
        "data-caption": caption || "",
        "data-link": link || "",
        "data-link-target": linkTarget || "_blank",
      }),
      ...elements,
    ];
  },

  addCommands() {
    return {
      setImageWithCaption:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          });
        },
    };
  },
});

// Type declaration for Twitter widgets
declare global {
  interface Window {
    twttr?: {
      widgets?: {
        load: () => void;
        createTweet?: (
          tweetId: string,
          element: HTMLElement,
          options?: any
        ) => Promise<HTMLElement>;
      };
      ready?: (callback: () => void) => void;
    };
  }
}

// Function to load Twitter widgets script
let twitterWidgetsLoading = false;
let twitterWidgetsLoaded = false;

function loadTwitterWidgets() {
  if (typeof window === "undefined") return;

  if (!window.twttr && !twitterWidgetsLoading) {
    // Load Twitter widgets script if not already loaded
    twitterWidgetsLoading = true;
    const script = document.createElement("script");
    script.src = "https://platform.twitter.com/widgets.js";
    script.async = true;
    script.charset = "utf-8";
    script.onload = () => {
      twitterWidgetsLoaded = true;
      twitterWidgetsLoading = false;
      // Process widgets after script loads
      setTimeout(() => {
        if (window.twttr && window.twttr.widgets) {
          window.twttr.widgets.load();
        }
      }, 100);
    };
    script.onerror = () => {
      twitterWidgetsLoading = false;
    };
    document.head.appendChild(script);
  } else if (window.twttr && window.twttr.widgets && !twitterWidgetsLoading) {
    // If Twitter widgets are already loaded, process new widgets with throttling
    window.twttr.widgets.load();
  }
}

// Track fetching tweets to prevent duplicates
const fetchingTweets = new Set<string>();

// Function to fetch Twitter embed data
async function fetchTwitterEmbedData(
  tweetUrl: string,
  tweetId: string,
  view: any,
  position: number
) {
  // Prevent duplicate fetches
  if (fetchingTweets.has(tweetId)) {
    return;
  }

  fetchingTweets.add(tweetId);

  try {
    const response = await fetch(
      `/api/twitter/embed?url=${encodeURIComponent(tweetUrl)}`
    );
    const result = await response.json();

    if (result.success && result.data) {
      // Find the Twitter embed node by searching the document
      const { tr } = view.state;
      let foundNode: any = null;
      let foundPos = -1;

      // Search through the document to find the Twitter embed node with matching tweetId
      view.state.doc.descendants((node: any, pos: number) => {
        if (
          node.type.name === "twitterEmbed" &&
          node.attrs.tweetId === tweetId &&
          node.attrs.isLoading === true
        ) {
          foundNode = node;
          foundPos = pos;
          return false; // Stop searching
        }
      });

      if (foundNode && foundPos !== -1) {
        // Update the node with the fetched data
        const updatedNode = view.state.schema.nodes.twitterEmbed.create({
          tweetId: tweetId,
          username: foundNode.attrs.username,
          tweetUrl: tweetUrl,
          embedHtml: result.data.html,
          isLoading: false,
          hasError: false,
        });

        const transaction = tr.replaceWith(
          foundPos,
          foundPos + foundNode.nodeSize,
          updatedNode
        );
        view.dispatch(transaction);

        // Load Twitter widgets script - simplified
        setTimeout(() => {
          loadTwitterWidgets();
        }, 200);
      }
    } else {
      // Handle error case
      updateTwitterEmbedError(view, position, tweetId, tweetUrl);
    }
  } catch (error) {
    console.error("Error fetching Twitter embed:", error);
    updateTwitterEmbedError(view, position, tweetId, tweetUrl);
  } finally {
    // Remove from fetching set after completion
    fetchingTweets.delete(tweetId);
  }
}

// Function to update Twitter embed with error state
function updateTwitterEmbedError(
  view: any,
  position: number,
  tweetId: string,
  tweetUrl: string
) {
  const { tr } = view.state;
  let foundNode: any = null;
  let foundPos = -1;

  // Search through the document to find the Twitter embed node with matching tweetId
  view.state.doc.descendants((node: any, pos: number) => {
    if (
      node.type.name === "twitterEmbed" &&
      node.attrs.tweetId === tweetId &&
      node.attrs.isLoading === true
    ) {
      foundNode = node;
      foundPos = pos;
      return false; // Stop searching
    }
  });

  if (foundNode && foundPos !== -1) {
    const updatedNode = view.state.schema.nodes.twitterEmbed.create({
      tweetId: tweetId,
      username: foundNode.attrs.username,
      tweetUrl: tweetUrl,
      embedHtml: null,
      isLoading: false,
      hasError: true,
    });

    const transaction = tr.replaceWith(
      foundPos,
      foundPos + foundNode.nodeSize,
      updatedNode
    );
    view.dispatch(transaction);
  }
}

interface BlogRichEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: string;
  showWordCount?: boolean;
  characterLimit?: number;
  showPreview?: boolean;
}

export function BlogRichEditor({
  content = "",
  onChange,
  placeholder = "Start typing your blog post...",
  className,
  minHeight = "400px",
  showWordCount = true,
  characterLimit,
  showPreview = false,
}: BlogRichEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);

  // Use Zustand store instead of local state
  const { editor, isPreviewMode } = useRichEditorState();
  const { setEditor, togglePreviewMode, openLinkDialog, openMediaDialog } =
    useRichEditorActions();

  // Get dialog states and actions directly from store
  const {
    isLinkDialogOpen,
    isMediaDialogOpen,
    isImageOptionsOpen,
    selectedImageData,
    openImageOptionsDialog,
  } = useRichEditorStore();

  const editorInstance = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        bulletList: {},
        orderedList: {},
        listItem: {},
        blockquote: {},
        bold: {},
        italic: {},
        code: {},
        paragraph: {},
        horizontalRule: {},
        history: {},
        codeBlock: false, // Disable default code block
      }),
      Underline,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: "editor-link",
        },
        protocols: ["http", "https", "mailto"],
        linkOnPaste: false,
        autolink: true,
        validate: (url) => /^https?:\/\//.test(url),
      }),
      Image.configure({
        HTMLAttributes: {
          class: "editor-image",
        },
        allowBase64: false,
        inline: false,
      }),
      ImageWithCaption,
      YouTubeEmbed,
      TwitterEmbed,
      EnhancedCodeBlock, // Add our enhanced code block
      ...(characterLimit || showWordCount
        ? [
            CharacterCount.configure({
              limit: characterLimit,
            }),
          ]
        : []),
    ],
    content,
    editorProps: {
      attributes: {
        class: "blog-rich-editor-prosemirror",
        "data-placeholder": placeholder,
      },
      handlePaste: (view, event, slice) => {
        // Get the clipboard text
        const text = event.clipboardData?.getData("text/plain") || "";

        // Check if it's a URL
        if (
          text &&
          (text.startsWith("http://") || text.startsWith("https://"))
        ) {
          // Check for YouTube URL
          const youtubeId = extractYouTubeId(text);
          if (youtubeId) {
            event.preventDefault();
            const { tr } = view.state;
            const { from } = view.state.selection;

            // Insert YouTube embed
            const youtubeNode = view.state.schema.nodes.youtubeEmbed.create({
              videoId: youtubeId,
            });

            const transaction = tr.replaceWith(from, from, youtubeNode);
            view.dispatch(transaction);
            return true;
          }

          // Check for Twitter URL
          const twitterData = extractTwitterData(text);
          if (twitterData) {
            event.preventDefault();
            const { tr } = view.state;
            const { from } = view.state.selection;

            // Insert Twitter embed with loading state
            const twitterNode = view.state.schema.nodes.twitterEmbed.create({
              tweetId: twitterData.tweetId,
              username: twitterData.username,
              tweetUrl: text,
              isLoading: true,
              hasError: false,
            });

            const transaction = tr.replaceWith(from, from, twitterNode);
            view.dispatch(transaction);

            // Fetch Twitter embed data
            fetchTwitterEmbedData(text, twitterData.tweetId, view, from);
            return true;
          }
        }

        // Let TipTap handle normal paste behavior
        return false;
      },
      handleClick: (view, pos, event) => {
        const target = event.target as HTMLElement;

        // PRIORITY 1: Check if clicked element is a link inside a caption
        const captionLink = target.closest("figcaption.image-caption a");
        if (captionLink) {
          // Allow normal link behavior for caption links
          return false;
        }

        // PRIORITY 1.5: Check if clicked element is a regular link (not in image caption)
        const regularLink =
          target.closest("a.editor-link") || target.closest("a[href]");
        if (
          regularLink &&
          !target.closest("figcaption.image-caption") &&
          !target.closest("figure[data-type='image-with-caption']")
        ) {
          // Prevent default link navigation in editor
          event.preventDefault();
          event.stopPropagation();

          // Open link dialog for editing
          openLinkDialog();
          return true;
        }

        // PRIORITY 2: Check if clicked element is an image or inside a figure with image
        // But exclude clicks inside captions
        const isImageClick =
          (target.tagName === "IMG" ||
            target.classList.contains("editor-image") ||
            target.closest("figure[data-type='image-with-caption']")) &&
          !target.closest("figcaption.image-caption");

        if (isImageClick) {
          // Always prevent default to stop link navigation in editor
          event.preventDefault();
          event.stopPropagation();

          // Find the actual image element
          const imgElement =
            target.tagName === "IMG"
              ? target
              : target.querySelector("img") ||
                target.closest("figure")?.querySelector("img");

          if (!imgElement) return true;

          // Find the closest figure element
          const figure = imgElement.closest(
            "figure[data-type='image-with-caption']"
          );

          // Find the image node in the ProseMirror document
          let imageNode = null;
          let imagePos = pos;

          // Try to find the node more reliably
          const nodeAtPos = view.state.doc.nodeAt(pos);
          const resolvedPos = view.state.doc.resolve(pos);

          // First check if we clicked directly on an image node
          if (
            nodeAtPos &&
            (nodeAtPos.type.name === "image" ||
              nodeAtPos.type.name === "imageWithCaption")
          ) {
            imageNode = nodeAtPos;
          } else {
            // Search through all depth levels to find the image node
            for (let depth = resolvedPos.depth; depth >= 0; depth--) {
              const node = resolvedPos.node(depth);
              if (
                node.type.name === "image" ||
                node.type.name === "imageWithCaption"
              ) {
                imageNode = node;
                imagePos = resolvedPos.start(depth);
                break;
              }
            }

            // If still not found, search around the click position
            if (!imageNode) {
              for (
                let i = Math.max(0, pos - 10);
                i <= Math.min(view.state.doc.content.size, pos + 10);
                i++
              ) {
                const nodeAt = view.state.doc.nodeAt(i);
                if (
                  nodeAt &&
                  (nodeAt.type.name === "image" ||
                    nodeAt.type.name === "imageWithCaption")
                ) {
                  imageNode = nodeAt;
                  imagePos = i;
                  break;
                }
              }
            }
          }

          if (imageNode) {
            const imageData = {
              src: imageNode.attrs.src || "",
              alt: imageNode.attrs.alt || "",
              title: imageNode.attrs.title || "",
              caption: imageNode.attrs.caption || "",
              link: imageNode.attrs.link || "",
              linkTarget: imageNode.attrs.linkTarget || "_blank",
              node: imageNode,
              pos: imagePos,
            };

            openImageOptionsDialog(imageData);
            return true;
          }
        }

        return false;
      },
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange?.(html);
    },
  });

  useEffect(() => {
    if (editorInstance) {
      setEditor(editorInstance);
    }
    return () => setEditor(null);
  }, [editorInstance, setEditor]);

  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      const currentPosition = editor.state.selection.anchor;
      editor.commands.setContent(content);

      // Try to restore cursor position if possible
      const newDoc = editor.state.doc;
      if (currentPosition <= newDoc.content.size) {
        editor.commands.setTextSelection(currentPosition);
      }
    }
  }, [content, editor]);

  // Handle HTML rendering in captions and Twitter embeds
  useEffect(() => {
    if (!editorInstance) return;

    let isProcessing = false;
    let processedEmbeds = new Set<string>();

    const renderHTMLInCaptions = () => {
      const captions = editorRef.current?.querySelectorAll(
        "figcaption.image-caption[data-html-content]"
      );

      captions?.forEach((caption) => {
        const htmlContent = caption.getAttribute("data-html-content");
        if (
          htmlContent &&
          htmlContent.includes("<") &&
          htmlContent.includes(">")
        ) {
          // Only update if content has changed
          if (caption.innerHTML !== htmlContent) {
            caption.innerHTML = htmlContent;
          }
        }
      });
    };

    const renderTwitterEmbeds = () => {
      if (isProcessing) return;

      const twitterEmbeds = editorRef.current?.querySelectorAll(
        ".twitter-embed-content[data-embed-html]"
      );

      twitterEmbeds?.forEach((embed) => {
        const embedHtml = embed.getAttribute("data-embed-html");
        const embedId =
          embed.getAttribute("data-embed-id") || embedHtml?.substring(0, 50);

        if (
          embedHtml &&
          embed.innerHTML !== embedHtml &&
          !processedEmbeds.has(embedId || "")
        ) {
          isProcessing = true;
          embed.innerHTML = embedHtml;

          if (embedId) {
            processedEmbeds.add(embedId);
          }

          // Load Twitter widgets after inserting HTML - only once
          setTimeout(() => {
            loadTwitterWidgets();
            isProcessing = false;
          }, 300);
        }
      });
    };

    // Run initially
    renderHTMLInCaptions();
    renderTwitterEmbeds();

    // Set up mutation observer with debouncing
    let observerTimeout: NodeJS.Timeout;
    const observer = new MutationObserver((mutations) => {
      // Ignore mutations caused by Twitter widgets
      const isTwitterWidgetMutation = mutations.some((mutation) => {
        const target = mutation.target as Element;
        return (
          target.closest?.(".twitter-tweet") ||
          target.closest?.(".twitter-embed-content") ||
          target.tagName === "IFRAME" ||
          target.classList?.contains("twitter-tweet")
        );
      });

      if (isTwitterWidgetMutation || isProcessing) {
        return;
      }

      // Clear previous timeout
      if (observerTimeout) {
        clearTimeout(observerTimeout);
      }

      // Debounce the rendering
      observerTimeout = setTimeout(() => {
        renderHTMLInCaptions();
        renderTwitterEmbeds();
      }, 200);
    });

    if (editorRef.current) {
      observer.observe(editorRef.current, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ["data-html-content", "data-embed-html"],
      });
    }

    return () => {
      if (observerTimeout) {
        clearTimeout(observerTimeout);
      }
      observer.disconnect();
    };
  }, [editorInstance]);

  // Fixed Toolbar configuration
  const toolbarButtons: Array<{
    key: string;
    icon?: React.ComponentType<{ className?: string }>;
    label?: string;
    isActive?: () => boolean;
    onClick?: () => void;
    isDivider?: boolean;
  }> = [
    {
      key: "bold",
      icon: Bold,
      label: "Bold",
      isActive: () => editorInstance?.isActive("bold") || false,
      onClick: () => editorInstance?.chain().focus().toggleBold().run(),
    },
    {
      key: "italic",
      icon: Italic,
      label: "Italic",
      isActive: () => editorInstance?.isActive("italic") || false,
      onClick: () => editorInstance?.chain().focus().toggleItalic().run(),
    },
    {
      key: "underline",
      icon: UnderlineIcon,
      label: "Underline",
      isActive: () => editorInstance?.isActive("underline") || false,
      onClick: () => editorInstance?.chain().focus().toggleUnderline().run(),
    },
    {
      key: "code",
      icon: Code,
      label: "Inline Code",
      isActive: () => editorInstance?.isActive("code") || false,
      onClick: () => editorInstance?.chain().focus().toggleCode().run(),
    },
    {
      key: "codeBlock",
      icon: Code2,
      label: "Code Block",
      isActive: () => editorInstance?.isActive("codeBlock") || false,
      onClick: () => {
        if (editorInstance?.isActive("codeBlock")) {
          editorInstance?.chain().focus().setParagraph().run();
        } else {
          editorInstance?.chain().focus().setCodeBlock().run();
        }
      },
    },
    {
      key: "divider-1",
      isDivider: true,
    },
    {
      key: "h1",
      icon: Heading1,
      label: "Heading 1",
      isActive: () =>
        editorInstance?.isActive("heading", { level: 1 }) || false,
      onClick: () => {
        if (editorInstance?.isActive("heading", { level: 1 })) {
          editorInstance?.chain().focus().setParagraph().run();
        } else {
          editorInstance?.chain().focus().toggleHeading({ level: 1 }).run();
        }
      },
    },
    {
      key: "h2",
      icon: Heading2,
      label: "Heading 2",
      isActive: () =>
        editorInstance?.isActive("heading", { level: 2 }) || false,
      onClick: () => {
        if (editorInstance?.isActive("heading", { level: 2 })) {
          editorInstance?.chain().focus().setParagraph().run();
        } else {
          editorInstance?.chain().focus().toggleHeading({ level: 2 }).run();
        }
      },
    },
    {
      key: "h3",
      icon: Heading3,
      label: "Heading 3",
      isActive: () =>
        editorInstance?.isActive("heading", { level: 3 }) || false,
      onClick: () => {
        if (editorInstance?.isActive("heading", { level: 3 })) {
          editorInstance?.chain().focus().setParagraph().run();
        } else {
          editorInstance?.chain().focus().toggleHeading({ level: 3 }).run();
        }
      },
    },
    {
      key: "divider-2",
      isDivider: true,
    },
    {
      key: "bulletList",
      icon: List,
      label: "Bullet List",
      isActive: () => editorInstance?.isActive("bulletList") || false,
      onClick: () => editorInstance?.chain().focus().toggleBulletList().run(),
    },
    {
      key: "orderedList",
      icon: ListOrdered,
      label: "Numbered List",
      isActive: () => editorInstance?.isActive("orderedList") || false,
      onClick: () => editorInstance?.chain().focus().toggleOrderedList().run(),
    },
    {
      key: "blockquote",
      icon: Quote,
      label: "Blockquote",
      isActive: () => editorInstance?.isActive("blockquote") || false,
      onClick: () => editorInstance?.chain().focus().toggleBlockquote().run(),
    },
    {
      key: "divider-3",
      isDivider: true,
    },
    {
      key: "link",
      icon: LinkIcon,
      label: "Link",
      isActive: () => editorInstance?.isActive("link") || false,
      onClick: () => {
        openLinkDialog();
      },
    },
    {
      key: "image",
      icon: ImageIcon,
      label: "Insert Image",
      isActive: () => false,
      onClick: () => {
        openMediaDialog();
      },
    },
    {
      key: "youtube",
      icon: Youtube,
      label: "Insert YouTube",
      isActive: () => false,
      onClick: () => {
        const url = prompt("Enter YouTube URL:");
        if (url) {
          const videoId = extractYouTubeId(url);
          if (videoId) {
            editorInstance?.chain().focus().setYouTubeEmbed({ videoId }).run();
          } else {
            alert(
              "Invalid YouTube URL. Please enter a valid YouTube video URL."
            );
          }
        }
      },
    },
    {
      key: "twitter",
      icon: Twitter,
      label: "Insert Twitter",
      isActive: () => false,
      onClick: () => {
        const url = prompt("Enter Twitter/X URL:");
        if (url) {
          const twitterData = extractTwitterData(url);
          if (twitterData) {
            const { from } = editorInstance?.state.selection || { from: 0 };

            // Insert Twitter embed with loading state
            editorInstance
              ?.chain()
              .focus()
              .setTwitterEmbed({
                tweetId: twitterData.tweetId,
                username: twitterData.username,
                tweetUrl: url,
                isLoading: true,
                hasError: false,
              })
              .run();

            // Fetch Twitter embed data
            if (editorInstance?.view) {
              fetchTwitterEmbedData(
                url,
                twitterData.tweetId,
                editorInstance.view,
                from
              );
            }
          } else {
            alert("Invalid Twitter/X URL. Please enter a valid tweet URL.");
          }
        }
      },
    },
    {
      key: "divider-4",
      isDivider: true,
    },
    {
      key: "horizontalRule",
      icon: Minus,
      label: "Horizontal Line",
      isActive: () => false,
      onClick: () => editorInstance?.chain().focus().setHorizontalRule().run(),
    },
    {
      key: "clear",
      icon: RotateCcw,
      label: "Clear Formatting",
      isActive: () => false,
      onClick: () =>
        editorInstance?.chain().focus().clearNodes().unsetAllMarks().run(),
    },
  ];

  // Statistics
  const getWordCount = () => {
    if (!editorInstance) return 0;
    return editorInstance.storage.characterCount?.words() || 0;
  };

  const getCharacterCount = () => {
    if (!editorInstance) return 0;
    return editorInstance.storage.characterCount?.characters() || 0;
  };

  const isCharacterLimitExceeded = () => {
    return characterLimit ? getCharacterCount() > characterLimit : false;
  };

  const getCharacterLimitProgress = () => {
    if (!characterLimit) return 0;
    return Math.min((getCharacterCount() / characterLimit) * 100, 100);
  };

  if (!editorInstance) {
    return (
      <div className="flex items-center justify-center h-48 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3 text-gray-500 dark:text-gray-400">
          <Sparkles className="w-5 h-5 animate-pulse" />
          <span className="font-medium">Initializing editor...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Sticky Fixed Toolbar Container */}
      <div className="blog-rich-editor-toolbar">
        {/* Toolbar Header */}
        <div className="">
          {showPreview && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={togglePreviewMode}
              className="h-8 px-3 text-xs font-medium"
            >
              {isPreviewMode ? (
                <>
                  <EyeOff className="w-3.5 h-3.5 mr-2" />
                  Edit
                </>
              ) : (
                <>
                  <Eye className="w-3.5 h-3.5 mr-2" />
                  Preview
                </>
              )}
            </Button>
          )}
        </div>

        {/* Fixed Toolbar Buttons */}
        <div className="blog-rich-editor-toolbar-buttons ">
          {toolbarButtons.map((button, index) => {
            if (button.isDivider) {
              return (
                <Separator
                  key={button.key}
                  orientation="vertical"
                  className="h-6 mx-1"
                />
              );
            }

            const Icon = button.icon!;
            const isActive = button.isActive?.() || false;

            return (
              <Button
                key={button.key}
                type="button"
                variant="ghost"
                size="sm"
                className={cn(
                  "h-8 w-8 p-0 transition-all duration-200 rounded-md flex-shrink-0 ",
                  isActive
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                )}
                onClick={(e) => {
                  e.preventDefault();
                  button.onClick?.();
                }}
                title={button.label}
              >
                <Icon className="h-4 w-4" />
              </Button>
            );
          })}
        </div>
      </div>

      {/* Editor Container */}
      <div
        ref={editorRef}
        className={cn(
          "relative overflow-hidden rounded-lg bg-white dark:bg-gray-900 transition-all duration-300",
          "border border-gray-200 dark:border-gray-700",
          "focus-within:ring-1 focus-within:ring-primary/20 focus-within:border-primary/50",
          "hover:border-gray-300 dark:hover:border-gray-600",
          isCharacterLimitExceeded() &&
            "border-red-300 focus-within:border-red-500 focus-within:ring-red-500/30",
          className
        )}
        style={{ minHeight }}
      >
        {/* Editor Content Area */}
        <div className="relative">
          {isPreviewMode ? (
            <div
              className="blog-rich-editor-preview prose prose-gray dark:prose-invert max-w-none p-6"
              style={{ minHeight }}
              dangerouslySetInnerHTML={{ __html: editorInstance.getHTML() }}
            />
          ) : (
            <div
              className="relative w-full cursor-text px-3"
              style={{ minHeight }}
              onClick={() => {
                editorInstance.commands.focus();
              }}
            >
              <EditorContent
                editor={editorInstance}
                className="blog-rich-editor-wrapper w-full h-full"
              />
            </div>
          )}
        </div>
      </div>

      {/* Statistics Bar */}
      {showWordCount && (
        <div className="flex items-center justify-between px-2 mt-4">
          <div className="flex items-center space-x-4">
            <Badge
              variant="secondary"
              className="text-xs font-medium bg-primary/5 text-primary border-primary/20"
            >
              <Type className="w-3 h-3 mr-1.5" />
              {getWordCount()} words
            </Badge>
            <Badge
              variant="secondary"
              className={cn(
                "text-xs font-medium border",
                isCharacterLimitExceeded()
                  ? "bg-red-50 text-red-600 border-red-200"
                  : "bg-primary/5 text-primary border-primary/20"
              )}
            >
              {getCharacterCount()}
              {characterLimit && `/${characterLimit}`} characters
            </Badge>

            {characterLimit && (
              <div className="flex items-center space-x-3">
                <div className="w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className={cn(
                      "h-full rounded-full transition-all duration-500",
                      isCharacterLimitExceeded()
                        ? "bg-red-500"
                        : getCharacterLimitProgress() > 80
                          ? "bg-amber-500"
                          : "bg-primary"
                    )}
                    style={{ width: `${getCharacterLimitProgress()}%` }}
                  />
                </div>
                {isCharacterLimitExceeded() && (
                  <span className="text-xs font-medium text-red-600">
                    Limit exceeded
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Advanced Link Dialog */}
      <BlogLinkDialog />

      {/* Media Dialog for Image Upload */}
      <BlogMediaDialog />

      {/* Image Options Dialog */}
      {selectedImageData && <BlogMediaImageOptionsDialog />}
    </div>
  );
}
