"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogFooter,
  CustomDialogTrigger,
} from "@/components/custom/custom-dialog";
import { cn } from "@/lib/utils";
import { User, Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface Author {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
  role: "ADMIN" | "AUTHOR" | "USER";
}

interface BlogAuthorDialogProps {
  trigger: React.ReactNode;
  authors: Author[];
  selectedAuthorId?: string;
  onAuthorChange: (authorId: string) => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  currentUserRole: "ADMIN" | "AUTHOR" | "USER";
  currentUserId: string;
}

export function BlogAuthorDialog({
  trigger,
  authors,
  selectedAuthorId,
  onAuthorChange,
  open,
  onOpenChange,
  currentUserRole,
  currentUserId,
}: BlogAuthorDialogProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [tempSelectedAuthorId, setTempSelectedAuthorId] = useState<string>("");

  // Filter authors to only show ADMIN and AUTHOR roles
  const availableAuthors = authors.filter(author => 
    author.role === "ADMIN" || author.role === "AUTHOR"
  );

  // Filter authors based on search query
  const filteredAuthors = availableAuthors.filter(author =>
    author.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    author.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Initialize temp selection when dialog opens
  useEffect(() => {
    if (open) {
      setTempSelectedAuthorId(selectedAuthorId || currentUserId);
      setSearchQuery("");
    }
  }, [open, selectedAuthorId, currentUserId]);

  const handleApply = () => {
    onAuthorChange(tempSelectedAuthorId);
    onOpenChange?.(false);
  };

  const handleCancel = () => {
    setTempSelectedAuthorId(selectedAuthorId || currentUserId);
    setSearchQuery("");
    onOpenChange?.(false);
  };

  const selectedAuthor = availableAuthors.find(author => author.id === tempSelectedAuthorId);

  // If user is not ADMIN, don't render the dialog trigger
  if (currentUserRole !== "ADMIN") {
    return null;
  }

  return (
    <CustomDialog open={open} onOpenChange={onOpenChange}>
      <CustomDialogTrigger asChild>{trigger}</CustomDialogTrigger>
      <CustomDialogContent className="max-w-md w-full max-h-[80vh] p-0">
        <CustomDialogHeader>
          <CustomDialogTitle className="flex items-center gap-2 py-2">
            <User className="h-4 w-4" />
            Select Author
          </CustomDialogTitle>
        </CustomDialogHeader>

        <div className="p-4 space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search authors..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 h-9"
            />
          </div>

          {/* Selected Author Preview */}
          {selectedAuthor && (
            <div className="p-3 border rounded-lg bg-muted/50">
              <Label className="text-xs font-medium text-muted-foreground mb-2 block">
                Selected Author
              </Label>
              <div className="flex items-center gap-3">
                <Avatar className="h-8 w-8">
                  {selectedAuthor.image && <AvatarImage src={selectedAuthor.image} />}
                  <AvatarFallback className="text-xs">
                    {selectedAuthor.name?.charAt(0) || selectedAuthor.email.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {selectedAuthor.name || "No name"}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">
                    {selectedAuthor.email}
                  </p>
                </div>
                <Badge 
                  variant={selectedAuthor.role === "ADMIN" ? "default" : "secondary"}
                  className="text-xs h-5"
                >
                  {selectedAuthor.role}
                </Badge>
              </div>
            </div>
          )}

          {/* Authors List */}
          <div className="space-y-2 max-h-64 overflow-y-auto custom-scrollbar">
            <Label className="text-xs font-medium text-muted-foreground">
              Available Authors ({filteredAuthors.length})
            </Label>
            {filteredAuthors.length > 0 ? (
              filteredAuthors.map((author) => (
                <div
                  key={author.id}
                  className={cn(
                    "flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-colors hover:bg-muted/50",
                    tempSelectedAuthorId === author.id && "bg-primary/10 border-primary"
                  )}
                  onClick={() => setTempSelectedAuthorId(author.id)}
                >
                  <Avatar className="h-8 w-8">
                    {author.image && <AvatarImage src={author.image} />}
                    <AvatarFallback className="text-xs">
                      {author.name?.charAt(0) || author.email.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {author.name || "No name"}
                    </p>
                    <p className="text-xs text-muted-foreground truncate">
                      {author.email}
                    </p>
                  </div>
                  <Badge 
                    variant={author.role === "ADMIN" ? "default" : "secondary"}
                    className="text-xs h-5"
                  >
                    {author.role}
                  </Badge>
                  {author.id === currentUserId && (
                    <Badge variant="outline" className="text-xs h-5">
                      You
                    </Badge>
                  )}
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <User className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">
                  {searchQuery ? "No authors found" : "No authors available"}
                </p>
              </div>
            )}
          </div>
        </div>

        <CustomDialogFooter className="border-t p-4 flex-row justify-between">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleApply} disabled={!tempSelectedAuthorId}>
            Apply Selection
          </Button>
        </CustomDialogFooter>
      </CustomDialogContent>
    </CustomDialog>
  );
}
