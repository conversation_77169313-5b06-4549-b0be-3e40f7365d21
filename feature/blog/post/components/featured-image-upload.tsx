"use client";

import React, { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, X, Image, ExternalLink } from "lucide-react";
import { useUploadMedia } from "@/feature/media/hooks/use-media-data";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface FeaturedImageUploadProps {
  value?: string;
  onChange: (url: string) => void;
  dict?: any;
}

export function FeaturedImageUpload({
  value,
  onChange,
  dict,
}: FeaturedImageUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [manualUrl, setManualUrl] = useState("");
  const [showManualInput, setShowManualInput] = useState(false);

  const uploadMutation = useUploadMedia();

  const handleFileSelect = useCallback(
    async (files: FileList | null) => {
      if (!files || files.length === 0) return;

      const file = files[0];

      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast.error("Please select an image file");
        return;
      }

      // Validate file size (10MB limit for featured images)
      if (file.size > 10 * 1024 * 1024) {
        toast.error("Featured image file size must be less than 10MB");
        return;
      }

      try {
        const result = await uploadMutation.mutateAsync({
          file,
          folder_path: "blog/featured-images",
        });

        if (result.success && result.data.url) {
          onChange(result.data.url);
        }
      } catch (error) {
        console.error("Upload failed:", error);
      }
    },
    [uploadMutation, onChange]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
      handleFileSelect(e.dataTransfer.files);
    },
    [handleFileSelect]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleManualUrlSubmit = useCallback(() => {
    if (manualUrl.trim()) {
      onChange(manualUrl.trim());
      setManualUrl("");
      setShowManualInput(false);
      toast.success("Featured image URL added successfully");
    }
  }, [manualUrl, onChange]);

  const removeImage = useCallback(() => {
    onChange("");
    toast.success("Featured image removed");
  }, [onChange]);

  return (
    <div className="space-y-3">
      {value ? (
        <div className="relative">
          <img
            src={value}
            alt="Featured Image"
            className="w-full h-32 object-cover rounded-lg border"
          />
          <Button
            type="button"
            variant="destructive"
            size="sm"
            className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
            onClick={removeImage}
          >
            <X className="h-3 w-3 text-white" />
          </Button>
          <div className="mt-2">
            <p className="text-xs text-muted-foreground">
              Featured image selected
            </p>
            <a
              href={value}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1 mt-1"
            >
              View original <ExternalLink className="h-3 w-3" />
            </a>
          </div>
        </div>
      ) : (
        <div className="space-y-3">
          {/* File Upload Zone */}
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer",
              isDragOver
                ? "border-primary bg-primary/5"
                : "border-muted-foreground/25 hover:border-primary/50"
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => {
              const input = document.createElement("input");
              input.type = "file";
              input.accept = "image/*";
              input.onchange = (e) => {
                const target = e.target as HTMLInputElement;
                handleFileSelect(target.files);
              };
              input.click();
            }}
          >
            <div className="flex flex-col items-center gap-2">
              <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                <Image className="w-4 h-4 text-muted-foreground" />
              </div>
              <div>
                <p className="text-xs font-medium">Upload Image</p>
                <p className="text-xs text-muted-foreground">
                  Click or drag (Max 10MB)
                </p>
              </div>
            </div>
          </div>

          {/* Manual URL Input Toggle */}
          <div className="text-center">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowManualInput(!showManualInput)}
              className="text-xs h-6"
            >
              Add URL manually
            </Button>
          </div>

          {/* Manual URL Input */}
          {showManualInput && (
            <div className="space-y-2">
              <div className="space-y-2">
                <Input
                  type="url"
                  placeholder="https://example.com/image.jpg"
                  value={manualUrl}
                  onChange={(e) => setManualUrl(e.target.value)}
                  className="text-xs h-8"
                />
                <div className="flex gap-1">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleManualUrlSubmit}
                    disabled={!manualUrl.trim()}
                    className="flex-1 h-6 text-xs"
                  >
                    Add
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowManualInput(false);
                      setManualUrl("");
                    }}
                    className="flex-1 h-6 text-xs"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {uploadMutation.isPending && (
        <div className="text-center">
          <p className="text-xs text-muted-foreground">Uploading...</p>
        </div>
      )}
    </div>
  );
}
