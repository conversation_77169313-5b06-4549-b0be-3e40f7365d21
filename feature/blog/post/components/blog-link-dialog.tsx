"use client";

import React, { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogFooter,
} from "@/components/custom/custom-dialog";
import {
  Link as LinkIcon,
  ExternalLink,
  Unlink,
  Check,
  X,
  AlertCircle,
} from "lucide-react";
import { cn } from "@/lib/utils";
// Import Zustand store hooks
import {
  useLinkDialogState,
  useLinkDialogActions,
} from "@/feature/blog/post/store/use-rich-editor-store";

export function BlogLinkDialog() {
  // Use Zustand store instead of props
  const {
    isLinkDialogOpen,
    linkData,
    isEditingLink,
    hasTextSelection,
    urlError,
  } = useLinkDialogState();

  const {
    closeLinkDialog,
    setLinkData,
    setUrlError,
    handleSaveLink,
    handleRemoveLink,
    validateUrl,
  } = useLinkDialogActions();

  // Update link data
  const updateLinkData = (field: keyof typeof linkData, value: any) => {
    setLinkData({ [field]: value });
    if (field === "url" && urlError) {
      setUrlError("");
    }
  };

  // Handle dialog close
  const handleClose = () => {
    setUrlError("");
    closeLinkDialog();
  };

  return (
    <CustomDialog open={isLinkDialogOpen} onOpenChange={handleClose}>
      <CustomDialogContent className="max-w-md">
        <CustomDialogHeader>
          <CustomDialogTitle className="flex items-center gap-2">
            <LinkIcon className="w-5 h-5 text-primary" />
            {isEditingLink ? "Edit Link" : "Add Link"}
          </CustomDialogTitle>
        </CustomDialogHeader>

        <div className="space-y-4 p-4">
          {/* URL Input */}
          <div className="space-y-2">
            <Label htmlFor="link-url" className="text-sm font-medium">
              URL
            </Label>
            <div className="relative">
              <Input
                id="link-url"
                type="url"
                placeholder="https://example.com"
                value={linkData.url}
                onChange={(e) => updateLinkData("url", e.target.value)}
                className={cn(
                  "pr-10",
                  urlError && "border-red-300 focus:border-red-500"
                )}
                autoFocus
              />
              {urlError && (
                <AlertCircle className="absolute right-3 top-3 w-4 h-4 text-red-500" />
              )}
            </div>
            {urlError && (
              <p className="text-xs text-red-600 flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                {urlError}
              </p>
            )}
          </div>

          {/* Link Text Input (only show if no selection or editing) */}
          {(!hasTextSelection || isEditingLink) && (
            <div className="space-y-2">
              <Label htmlFor="link-text" className="text-sm font-medium">
                Link Text
              </Label>
              <Input
                id="link-text"
                type="text"
                placeholder="Enter link text"
                value={linkData.text}
                onChange={(e) => updateLinkData("text", e.target.value)}
              />
            </div>
          )}

          <Separator />

          {/* Link Options */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Link Options
            </h4>

            {/* Open in New Tab */}
            <div className="flex items-center space-x-3">
              <Checkbox
                id="new-tab"
                checked={linkData.openInNewTab}
                onCheckedChange={(checked) =>
                  updateLinkData("openInNewTab", checked)
                }
              />
              <Label
                htmlFor="new-tab"
                className="text-sm cursor-pointer flex items-center gap-2"
              >
                <ExternalLink className="w-4 h-4" />
                Open in new tab
              </Label>
            </div>

            {/* NoFollow */}
            <div className="flex items-center space-x-3">
              <Checkbox
                id="no-follow"
                checked={linkData.noFollow}
                onCheckedChange={(checked) =>
                  updateLinkData("noFollow", checked)
                }
              />
              <Label htmlFor="no-follow" className="text-sm cursor-pointer">
                Set to nofollow
                <span className="text-xs text-gray-500 ml-1">
                  (tells search engines not to follow this link)
                </span>
              </Label>
            </div>

            {/* Sponsored */}
            <div className="flex items-center space-x-3">
              <Checkbox
                id="sponsored"
                checked={linkData.sponsored}
                onCheckedChange={(checked) =>
                  updateLinkData("sponsored", checked)
                }
              />
              <Label htmlFor="sponsored" className="text-sm cursor-pointer">
                Set to sponsored
                <span className="text-xs text-gray-500 ml-1">
                  (indicates this is a paid or sponsored link)
                </span>
              </Label>
            </div>
          </div>
        </div>

        <CustomDialogFooter className="flex justify-between">
          <div>
            {isEditingLink && (
              <Button
                type="button"
                variant="destructive"
                size="sm"
                onClick={handleRemoveLink}
                className="flex items-center gap-2"
              >
                <Unlink className="w-4 h-4" />
                Remove Link
              </Button>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleClose}
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="button"
              size="sm"
              onClick={handleSaveLink}
              disabled={!linkData.url.trim()}
            >
              <Check className="w-4 h-4 mr-2" />
              {isEditingLink ? "Update Link" : "Add Link"}
            </Button>
          </div>
        </CustomDialogFooter>
      </CustomDialogContent>
    </CustomDialog>
  );
}
