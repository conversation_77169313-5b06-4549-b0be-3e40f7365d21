"use client";

import React from "react";
import {
  Alert<PERSON><PERSON>og,
  AlertDialog<PERSON>ction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Loader2, Trash2 } from "lucide-react";
import {
  useBlogPostDeleteState,
  useBlogPostDeleteActions,
} from "../store/use-blog-post-store";
import { useDeleteBlogPost } from "../hooks/use-blog-post-data";

export function BlogPostDeleteDialog() {
  const { isDeleteDialogOpen, deletingPost } = useBlogPostDeleteState();
  const { closeDeleteDialog } = useBlogPostDeleteActions();
  const deleteBlogPost = useDeleteBlogPost();

  const handleDelete = async () => {
    if (!deletingPost) return;

    try {
      await deleteBlogPost.mutateAsync(deletingPost.id);
      closeDeleteDialog();
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  return (
    <AlertDialog open={isDeleteDialogOpen} onOpenChange={closeDeleteDialog}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            Delete Blog Post
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-2">
            <p>Are you sure you want to delete "{deletingPost?.title}"?</p>
            <p className="text-sm text-muted-foreground">
              This action cannot be undone. The blog post and all its comments
              will be permanently removed from the system.
            </p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteBlogPost.isPending}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteBlogPost.isPending}
            >
              {deleteBlogPost.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Post
                </>
              )}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
