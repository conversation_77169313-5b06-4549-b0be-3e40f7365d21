import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNode<PERSON>iew<PERSON><PERSON><PERSON>, NodeViewWrapper, NodeViewContent } from '@tiptap/react'
import { CodeBlockLowlight } from '@tiptap/extension-code-block-lowlight'
import { createLowlight } from 'lowlight'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Copy, Check } from 'lucide-react'
import { useState } from 'react'

// Import common languages
import javascript from 'highlight.js/lib/languages/javascript'
import typescript from 'highlight.js/lib/languages/typescript'
import python from 'highlight.js/lib/languages/python'
import java from 'highlight.js/lib/languages/java'
import cpp from 'highlight.js/lib/languages/cpp'
import csharp from 'highlight.js/lib/languages/csharp'
import php from 'highlight.js/lib/languages/php'
import ruby from 'highlight.js/lib/languages/ruby'
import go from 'highlight.js/lib/languages/go'
import rust from 'highlight.js/lib/languages/rust'
import css from 'highlight.js/lib/languages/css'
import html from 'highlight.js/lib/languages/xml'
import json from 'highlight.js/lib/languages/json'
import sql from 'highlight.js/lib/languages/sql'
import bash from 'highlight.js/lib/languages/bash'
import yaml from 'highlight.js/lib/languages/yaml'
import markdown from 'highlight.js/lib/languages/markdown'

// Create lowlight instance
const lowlight = createLowlight()

// Register languages
lowlight.register('javascript', javascript)
lowlight.register('typescript', typescript)
lowlight.register('python', python)
lowlight.register('java', java)
lowlight.register('cpp', cpp)
lowlight.register('csharp', csharp)
lowlight.register('php', php)
lowlight.register('ruby', ruby)
lowlight.register('go', go)
lowlight.register('rust', rust)
lowlight.register('css', css)
lowlight.register('html', html)
lowlight.register('xml', html)
lowlight.register('json', json)
lowlight.register('sql', sql)
lowlight.register('bash', bash)
lowlight.register('shell', bash)
lowlight.register('yaml', yaml)
lowlight.register('yml', yaml)
lowlight.register('markdown', markdown)

// Available languages for the dropdown
const LANGUAGES = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'cpp', label: 'C++' },
  { value: 'csharp', label: 'C#' },
  { value: 'php', label: 'PHP' },
  { value: 'ruby', label: 'Ruby' },
  { value: 'go', label: 'Go' },
  { value: 'rust', label: 'Rust' },
  { value: 'css', label: 'CSS' },
  { value: 'html', label: 'HTML' },
  { value: 'json', label: 'JSON' },
  { value: 'sql', label: 'SQL' },
  { value: 'bash', label: 'Bash' },
  { value: 'yaml', label: 'YAML' },
  { value: 'markdown', label: 'Markdown' },
  { value: 'text', label: 'Plain Text' },
]

// Custom CodeBlock component
function CodeBlockComponent({ node, updateAttributes, extension }: any) {
  const [copied, setCopied] = useState(false)
  const language = node.attrs.language || 'text'
  
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(node.textContent)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  return (
    <NodeViewWrapper className="code-block-wrapper relative group">
      {/* Header with language selector and copy button */}
      <div className="code-block-header flex items-center justify-between px-4 py-2 bg-gray-800 text-white rounded-t-lg border-b border-gray-700">
        <Select
          value={language}
          onValueChange={(value) => updateAttributes({ language: value })}
        >
          <SelectTrigger className="w-40 h-8 bg-gray-700 border-gray-600 text-white text-sm">
            <SelectValue placeholder="Language" />
          </SelectTrigger>
          <SelectContent className="bg-gray-800 border-gray-700">
            {LANGUAGES.map((lang) => (
              <SelectItem 
                key={lang.value} 
                value={lang.value}
                className="text-white hover:bg-gray-700 focus:bg-gray-700"
              >
                {lang.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={copyToClipboard}
          className="h-8 px-2 text-white hover:bg-gray-700"
        >
          {copied ? (
            <Check className="h-4 w-4" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      </div>
      
      {/* Code content */}
      <div className="code-block-content">
        <NodeViewContent as="pre" className={`language-${language}`} />
      </div>
    </NodeViewWrapper>
  )
}

// Create the enhanced CodeBlock extension
export const EnhancedCodeBlock = CodeBlockLowlight
  .extend({
    name: 'codeBlock',
    
    addAttributes() {
      return {
        ...this.parent?.(),
        language: {
          default: 'text',
          parseHTML: element => element.getAttribute('data-language'),
          renderHTML: attributes => {
            return {
              'data-language': attributes.language,
              class: `language-${attributes.language}`,
            }
          },
        },
      }
    },

    addNodeView() {
      return ReactNodeViewRenderer(CodeBlockComponent)
    },
  })
  .configure({
    lowlight,
    defaultLanguage: 'text',
  }) 