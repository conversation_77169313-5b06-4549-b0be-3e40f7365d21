"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useBlogPostFiltersState } from "../store/use-blog-post-store";
import { useGetBlogPosts } from "../hooks/use-blog-post-data";
import { BlogPostTable } from "./blog-post-table";
import { BlogPostDeleteDialog } from "./blog-post-delete-dialog";
import { BlogPostFilters } from "./blog-post-filters";
import { BlogStatus } from "@/types/blog-api";

export function BlogPostManagement() {
  const router = useRouter();
  const {
    searchQuery,
    statusFilter,
    authorFilter,
    categoryFilter,
    tagFilter,
    publishedFilter,
    currentPage,
    pageSize,
  } = useBlogPostFiltersState();

  // Build query parameters for API call
  const queryParams = {
    page: currentPage,
    limit: pageSize,
    ...(searchQuery && { search: searchQuery }),
    ...(statusFilter !== "all" && { status: statusFilter as BlogStatus }),
    ...(authorFilter !== "all" && { author_id: authorFilter }),
    ...(categoryFilter !== "all" && { category_id: parseInt(categoryFilter) }),
    ...(tagFilter !== "all" && { tag_id: parseInt(tagFilter) }),
    ...(publishedFilter !== "all" && {
      published: publishedFilter === "true",
    }),
  };

  const { data: postsData, isLoading, error } = useGetBlogPosts(queryParams);

  const handleAddPost = () => {
    router.push("/admin/blog/new");
  };

  return (
    <div className="space-y-6">
      <Card className="shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-2xl font-bold">Blog Management</CardTitle>
          <Button
            onClick={handleAddPost}
            className="bg-primary hover:bg-primary/90"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Post
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          <BlogPostFilters />
          <BlogPostTable data={postsData} isLoading={isLoading} error={error} />
        </CardContent>
      </Card>

      <BlogPostDeleteDialog />
    </div>
  );
}
