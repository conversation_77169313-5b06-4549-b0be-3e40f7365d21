"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogFooter,
  CustomDialogTrigger,
} from "@/components/custom/custom-dialog";
import { cn } from "@/lib/utils";
import { Tag, Folder } from "lucide-react";

interface BlogCategoryTagDialogProps {
  trigger: React.ReactNode;
  categories: any[];
  tags: any[];
  selectedCategories: number[];
  selectedTags: number[];
  onSelectionChange: (categories: number[], tags: number[]) => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function BlogCategoryTagDialog({
  trigger,
  categories,
  tags,
  selectedCategories,
  selectedTags,
  onSelectionChange,
  open,
  onOpenChange,
}: BlogCategoryTagDialogProps) {
  const [tempSelectedCategories, setTempSelectedCategories] = useState<
    number[]
  >([]);
  const [tempSelectedTags, setTempSelectedTags] = useState<number[]>([]);

  // Initialize temp selections when dialog opens
  useEffect(() => {
    if (open) {
      setTempSelectedCategories([...selectedCategories]);
      setTempSelectedTags([...selectedTags]);
    }
  }, [open, selectedCategories, selectedTags]);

  const handleApply = () => {
    onSelectionChange(tempSelectedCategories, tempSelectedTags);
    onOpenChange?.(false);
  };

  const handleCancel = () => {
    setTempSelectedCategories([...selectedCategories]);
    setTempSelectedTags([...selectedTags]);
    onOpenChange?.(false);
  };

  const handleCategoryChange = (categoryId: number, checked: boolean) => {
    if (checked) {
      setTempSelectedCategories([...tempSelectedCategories, categoryId]);
    } else {
      setTempSelectedCategories(
        tempSelectedCategories.filter((id) => id !== categoryId)
      );
    }
  };

  const handleTagChange = (tagId: number, checked: boolean) => {
    if (checked) {
      setTempSelectedTags([...tempSelectedTags, tagId]);
    } else {
      setTempSelectedTags(tempSelectedTags.filter((id) => id !== tagId));
    }
  };

  return (
    <CustomDialog open={open} onOpenChange={onOpenChange}>
      <CustomDialogTrigger asChild>{trigger}</CustomDialogTrigger>
      <CustomDialogContent className="max-w-2xl w-full max-h-[80vh] p-0">
        <CustomDialogHeader>
          <CustomDialogTitle className="flex items-center gap-2 py-2">
            Categories & Tags
          </CustomDialogTitle>
        </CustomDialogHeader>

        <div className="flex h-[60vh] overflow-hidden">
          {/* Left Side - Categories */}
          <div className="flex-1 border-r p-4 overflow-y-auto custom-scrollbar">
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <Folder className="h-4 w-4 text-muted-foreground" />
                <Label className="text-sm font-semibold">Categories</Label>
                {tempSelectedCategories.length > 0 && (
                  <Badge variant="secondary" className="text-xs h-5">
                    {tempSelectedCategories.length} selected
                  </Badge>
                )}
              </div>

              <div className="space-y-2">
                {categories.map((category: any) => {
                  const isParent = !category.parent_id;
                  const isChild = category.parent_id;

                  return (
                    <div
                      key={category.id}
                      className={cn(
                        "flex items-center space-x-3 py-2 px-2 rounded-md hover:bg-muted/50 transition-colors",
                        isChild && "ml-6"
                      )}
                    >
                      <Checkbox
                        id={`category-${category.id}`}
                        checked={tempSelectedCategories.includes(category.id)}
                        onCheckedChange={(checked) =>
                          handleCategoryChange(category.id, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`category-${category.id}`}
                        className={cn(
                          "text-sm cursor-pointer flex-1",
                          isParent && "font-medium text-foreground",
                          isChild && "text-muted-foreground"
                        )}
                      >
                        {isChild && "↳ "}
                        {category.name}
                      </Label>
                      {category.children && category.children.length > 0 && (
                        <Badge variant="outline" className="text-xs h-4 px-1">
                          {category.children.length}
                        </Badge>
                      )}
                    </div>
                  );
                })}
                {(!categories || categories.length === 0) && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Folder className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No categories available</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Side - Tags */}
          <div className="flex-1 p-4 overflow-y-auto custom-scrollbar">
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <Tag className="h-4 w-4 text-muted-foreground" />
                <Label className="text-sm font-semibold">Tags</Label>
                {tempSelectedTags.length > 0 && (
                  <Badge variant="secondary" className="text-xs h-5">
                    {tempSelectedTags.length} selected
                  </Badge>
                )}
              </div>

              <div className="space-y-2">
                {tags.map((tag: any) => (
                  <div
                    key={tag.id}
                    className="flex items-center space-x-3 py-2 px-2 rounded-md hover:bg-muted/50 transition-colors"
                  >
                    <Checkbox
                      id={`tag-${tag.id}`}
                      checked={tempSelectedTags.includes(tag.id)}
                      onCheckedChange={(checked) =>
                        handleTagChange(tag.id, checked as boolean)
                      }
                    />
                    <Label
                      htmlFor={`tag-${tag.id}`}
                      className="text-sm cursor-pointer flex-1"
                    >
                      {tag.name}
                    </Label>
                  </div>
                ))}
                {(!tags || tags.length === 0) && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Tag className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No tags available</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <CustomDialogFooter className="border-t p-4 flex-row justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleApply}>Apply Selection</Button>
          </div>
        </CustomDialogFooter>
      </CustomDialogContent>
    </CustomDialog>
  );
}
