"use client";

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Edit,
  Trash2,
  MoreHorizontal,
  Eye,
  FileText,
  Calendar,
  User,
  Tag,
  Folder,
  ExternalLink,
} from "lucide-react";
import { CustomPagination } from "@/components/custom/custom-pagination";
import {
  useBlogPostDeleteActions,
  useBlogPostFiltersActions,
  useBlogPostFiltersState,
} from "../store/use-blog-post-store";
import {
  usePublishBlogPost,
  useUnpublishBlogPost,
  useArchiveBlogPost,
  useUnarchiveBlogPost,
} from "../hooks/use-blog-post-data";
import {
  BlogPostResponse,
  PaginatedResponse,
  BlogStatus,
} from "@/types/blog-api";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface BlogPostTableProps {
  data?: PaginatedResponse<BlogPostResponse>;
  isLoading: boolean;
  error?: any;
}

export function BlogPostTable({ data, isLoading, error }: BlogPostTableProps) {
  const router = useRouter();
  const { openDeleteDialog } = useBlogPostDeleteActions();
  const { setCurrentPage } = useBlogPostFiltersActions();
  const { currentPage, pageSize } = useBlogPostFiltersState();

  const publishPost = usePublishBlogPost();
  const unpublishPost = useUnpublishBlogPost();
  const archivePost = useArchiveBlogPost();
  const unarchivePost = useUnarchiveBlogPost();

  const handleEdit = (post: BlogPostResponse) => {
    router.push(`/admin/blog/edit/${post.id}`);
  };

  const handleView = (post: BlogPostResponse) => {
    // In a real app, this would navigate to the public blog post
    toast.info("View functionality would open the public blog post");
  };

  const handleStatusChange = async (post: BlogPostResponse, action: string) => {
    try {
      switch (action) {
        case "publish":
          await publishPost.mutateAsync(post.id);
          break;
        case "unpublish":
          await unpublishPost.mutateAsync(post.id);
          break;
        case "archive":
          await archivePost.mutateAsync(post.id);
          break;
        case "unarchive":
          await unarchivePost.mutateAsync(post.id);
          break;
      }
    } catch (error) {
      // Error handled by mutation hooks
    }
  };

  const getStatusBadge = (status: BlogStatus) => {
    const statusConfig = {
      [BlogStatus.DRAFT]: { variant: "secondary" as const, label: "Draft" },
      [BlogStatus.PUBLISHED]: {
        variant: "default" as const,
        label: "Published",
      },
      [BlogStatus.SCHEDULED]: {
        variant: "outline" as const,
        label: "Scheduled",
      },
      [BlogStatus.ARCHIVED]: {
        variant: "destructive" as const,
        label: "Archived",
      },
      [BlogStatus.PRIVATE]: { variant: "secondary" as const, label: "Private" },
    };

    const config = statusConfig[status];
    return (
      <Badge variant={config.variant} className="font-medium">
        {config.label}
      </Badge>
    );
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not set";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Author</TableHead>
                <TableHead>Published</TableHead>
                <TableHead>Categories</TableHead>
                <TableHead>Tags</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(5)].map((_, i) => (
                <TableRow key={i}>
                  <TableCell>
                    <Skeleton className="h-4 w-48" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-6 w-20" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-32" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-24" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-20" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-24" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-8 w-8" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load blog posts. Please try again.
        </AlertDescription>
      </Alert>
    );
  }

  if (!data?.items?.length) {
    return (
      <div className="rounded-md border border-dashed p-8 text-center">
        <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">No blog posts found</h3>
        <p className="text-muted-foreground">
          Get started by creating your first blog post.
        </p>
        <Button className="mt-4" onClick={() => router.push("/admin/blog/new")}>
          <FileText className="mr-2 h-4 w-4" />
          Create Post
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Author</TableHead>
              <TableHead>Published</TableHead>
              <TableHead>Categories</TableHead>
              <TableHead>Tags</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.items.map((post, index) => {
              const rowNumber = (currentPage - 1) * pageSize + index + 1;
              return (
                <TableRow key={post.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <span className="text-xs text-muted-foreground min-w-[20px]">
                        {rowNumber}
                      </span>
                      <div>
                        <div className="font-medium truncate max-w-[300px]">
                          {post.title}
                        </div>
                        {post.excerpt && (
                          <div className="text-sm text-muted-foreground truncate max-w-[300px]">
                            {post.excerpt}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(post.status)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="truncate max-w-[120px]">
                        {post.author?.name || post.author?.email || "Unknown"}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {formatDate(post.published_at?.toString() || null)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Folder className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {post.categories?.length || 0}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Tag className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{post.tags?.length || 0}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-muted-foreground">
                      {formatDate(post.created_at?.toString() || null)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="h-8 w-8 p-0"
                          disabled={
                            publishPost.isPending ||
                            unpublishPost.isPending ||
                            archivePost.isPending ||
                            unarchivePost.isPending
                          }
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleView(post)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(post)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />

                        {post.status === BlogStatus.DRAFT && (
                          <DropdownMenuItem
                            onClick={() => handleStatusChange(post, "publish")}
                          >
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Publish
                          </DropdownMenuItem>
                        )}

                        {post.status === BlogStatus.PUBLISHED && (
                          <DropdownMenuItem
                            onClick={() =>
                              handleStatusChange(post, "unpublish")
                            }
                          >
                            <FileText className="mr-2 h-4 w-4" />
                            Unpublish
                          </DropdownMenuItem>
                        )}

                        {post.status !== BlogStatus.ARCHIVED && (
                          <DropdownMenuItem
                            onClick={() => handleStatusChange(post, "archive")}
                          >
                            <FileText className="mr-2 h-4 w-4" />
                            Archive
                          </DropdownMenuItem>
                        )}

                        {post.status === BlogStatus.ARCHIVED && (
                          <DropdownMenuItem
                            onClick={() =>
                              handleStatusChange(post, "unarchive")
                            }
                          >
                            <FileText className="mr-2 h-4 w-4" />
                            Unarchive
                          </DropdownMenuItem>
                        )}

                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => openDeleteDialog(post)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {data.pagination.total_pages > 1 && (
        <CustomPagination
          currentPage={currentPage}
          totalPages={data.pagination.total_pages}
          onPageChange={setCurrentPage}
          hasNext={data.pagination.has_next}
          hasPrev={data.pagination.has_prev}
        />
      )}
    </div>
  );
}
