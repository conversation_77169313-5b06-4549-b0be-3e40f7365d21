"use client";

import React, { useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogFooter,
} from "@/components/custom/custom-dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Settings,
  Link as LinkIcon,
  Image as ImageIcon,
  Save,
  X,
  ExternalLink,
  Eye,
  EyeOff,
  Type,
  FileText,
  Link2,
  Unlink,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
// Import Zustand store hooks
import {
  useImageOptionsState,
  useImageOptionsActions,
  useRichEditorStore,
} from "@/feature/blog/post/store/use-rich-editor-store";

export function BlogMediaImageOptionsDialog() {
  // Use Zustand store instead of props
  const {
    isImageOptionsOpen,
    selectedImageData,
    imageOptionsActiveTab,
    isLinkEnabled,
    isLinkPopoverOpen,
    captionLinkText,
    captionLinkUrl,
    captionSelectedText,
  } = useImageOptionsState();

  const {
    closeImageOptionsDialog,
    setImageOptionsActiveTab,
    setIsLinkEnabled,
    setIsLinkPopoverOpen,
    setCaptionLinkText,
    setCaptionLinkUrl,
    setCaptionSelectedText,
    handleImageOptionsInputChange,
    handleImageOptionsLinkToggle,
    handleImageOptionsSave,
    handleImageDelete,
  } = useImageOptionsActions();

  // Get isValidUrl function from the store
  const { isValidUrl } = useRichEditorStore();

  // Caption link functionality
  const captionTextareaRef = useRef<HTMLTextAreaElement>(null);

  // Initialize form data when dialog opens or imageData changes
  useEffect(() => {
    if (isImageOptionsOpen && selectedImageData) {
      setIsLinkEnabled(!!selectedImageData.link);
    }
  }, [isImageOptionsOpen, selectedImageData, setIsLinkEnabled]);

  const handleInputChange = (field: string, value: string | number) => {
    handleImageOptionsInputChange(field as any, value);
  };

  const handleLinkToggle = (enabled: boolean) => {
    handleImageOptionsLinkToggle(enabled);
  };

  const handleSave = () => {
    handleImageOptionsSave();
  };

  const handleCancel = () => {
    closeImageOptionsDialog();
  };

  // Caption link functions
  const handleCaptionLinkInsert = () => {
    const textarea = captionTextareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selected = selectedImageData?.caption?.substring(start, end) || "";

    setCaptionSelectedText(selected);
    setCaptionLinkText(selected || "");
    setCaptionLinkUrl("");
    setIsLinkPopoverOpen(true);
  };

  const insertLinkInCaption = () => {
    if (!captionLinkUrl.trim() || !captionLinkText.trim()) {
      toast.error("Please provide both link text and URL");
      return;
    }

    if (!isValidUrl(captionLinkUrl)) {
      toast.error("Please enter a valid URL");
      return;
    }

    const textarea = captionTextareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentCaption = selectedImageData?.caption || "";

    const linkMarkup = `<a href="${captionLinkUrl.trim()}" target="_blank" rel="noopener noreferrer">${captionLinkText.trim()}</a>`;

    const newCaption =
      currentCaption.substring(0, start) +
      linkMarkup +
      currentCaption.substring(end);

    handleInputChange("caption", newCaption);

    // Reset link form
    setCaptionLinkText("");
    setCaptionLinkUrl("");
    setCaptionSelectedText("");
    setIsLinkPopoverOpen(false);

    // Focus back to textarea
    setTimeout(() => {
      textarea.focus();
      const newPosition = start + linkMarkup.length;
      textarea.setSelectionRange(newPosition, newPosition);
    }, 100);
  };

  const removeLinkFromSelection = () => {
    const textarea = captionTextareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentCaption = selectedImageData?.caption || "";
    const selectedText = currentCaption.substring(start, end);

    // Simple regex to remove <a> tags but keep the text content
    const cleanText = selectedText.replace(/<a[^>]*>(.*?)<\/a>/gi, "$1");

    const newCaption =
      currentCaption.substring(0, start) +
      cleanText +
      currentCaption.substring(end);

    handleInputChange("caption", newCaption);

    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start, start + cleanText.length);
    }, 100);
  };

  const getImagePreviewSize = () => {
    const maxWidth = 300;
    const maxHeight = 200;

    if (selectedImageData?.width && selectedImageData?.height) {
      const aspectRatio = selectedImageData.width / selectedImageData.height;

      if (selectedImageData.width > selectedImageData.height) {
        return {
          width: Math.min(maxWidth, selectedImageData.width),
          height: Math.min(maxWidth / aspectRatio, maxHeight),
        };
      } else {
        return {
          width: Math.min(maxHeight * aspectRatio, maxWidth),
          height: Math.min(maxHeight, selectedImageData.height),
        };
      }
    }

    return { width: maxWidth, height: "auto" };
  };

  if (!selectedImageData) {
    return null;
  }

  return (
    <CustomDialog
      open={isImageOptionsOpen}
      onOpenChange={closeImageOptionsDialog}
    >
      <CustomDialogContent className="max-w-xl max-h-[90vh] overflow-y-auto">
        <CustomDialogHeader>
          <CustomDialogTitle className="flex items-center gap-2">
            <ImageIcon className="w-5 h-5 text-primary" />
            Image Properties
          </CustomDialogTitle>
        </CustomDialogHeader>

        <div className="space-y-6 ">
          {/* Image Preview */}
          <div className="flex justify-center p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
            <div className="text-center">
              {selectedImageData.src ? (
                <div className="space-y-3">
                  <img
                    src={selectedImageData.src}
                    alt={selectedImageData.alt || "Preview"}
                    style={getImagePreviewSize()}
                    className="rounded-lg border border-gray-200 dark:border-gray-700 mx-auto"
                  />
                  {selectedImageData.caption && (
                    <p
                      className="text-sm text-gray-600 dark:text-gray-400 italic max-w-sm mx-auto"
                      dangerouslySetInnerHTML={{
                        __html: selectedImageData.caption,
                      }}
                    />
                  )}
                </div>
              ) : (
                <div className="text-gray-500 dark:text-gray-400">
                  <ImageIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>No image selected</p>
                </div>
              )}
            </div>
          </div>

          {/* Tabs for different options */}
          <div className="px-6">
            <Tabs
              value={imageOptionsActiveTab}
              onValueChange={(value) =>
                setImageOptionsActiveTab(value as "general" | "link")
              }
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger
                  value="general"
                  className="flex items-center gap-2"
                >
                  <Settings className="w-4 h-4" />
                  General
                </TabsTrigger>
                <TabsTrigger value="link" className="flex items-center gap-2">
                  <LinkIcon className="w-4 h-4" />
                  Link
                </TabsTrigger>
              </TabsList>

              <TabsContent value="general" className="space-y-4 mt-4">
                {/* Alt Text */}
                <div className="space-y-2">
                  <Label htmlFor="alt-text" className="flex items-center gap-2">
                    <Eye className="w-4 h-4" />
                    Alt Text (for accessibility)
                  </Label>
                  <Input
                    id="alt-text"
                    value={selectedImageData.alt || ""}
                    onChange={(e) => handleInputChange("alt", e.target.value)}
                    placeholder="Describe this image for screen readers..."
                    className="w-full"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Alternative text describes your image to visually impaired
                    users and search engines.
                  </p>
                </div>

                {/* Title */}
                <div className="space-y-2">
                  <Label htmlFor="title" className="flex items-center gap-2">
                    <Type className="w-4 h-4" />
                    Title (tooltip text)
                  </Label>
                  <Input
                    id="title"
                    value={selectedImageData.title || ""}
                    onChange={(e) => handleInputChange("title", e.target.value)}
                    placeholder="Image title..."
                    className="w-full"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Title text appears when users hover over the image.
                  </p>
                </div>

                {/* Caption */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label
                      htmlFor="caption"
                      className="flex items-center gap-2"
                    >
                      <FileText className="w-4 h-4" />
                      Caption
                    </Label>
                    <div className="flex gap-1">
                      <Popover
                        open={isLinkPopoverOpen}
                        onOpenChange={setIsLinkPopoverOpen}
                      >
                        <PopoverTrigger asChild>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleCaptionLinkInsert}
                            className="h-7 px-2 text-xs"
                          >
                            <Link2 className="w-3 h-3 mr-1" />
                            Link
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-80" align="end">
                          <div className="space-y-3">
                            <div className="space-y-2">
                              <Label
                                htmlFor="link-text"
                                className="text-sm font-medium"
                              >
                                Link Text
                              </Label>
                              <Input
                                id="link-text"
                                value={captionLinkText}
                                onChange={(e) =>
                                  setCaptionLinkText(e.target.value)
                                }
                                placeholder="Enter link text..."
                                className="text-sm"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label
                                htmlFor="link-url"
                                className="text-sm font-medium"
                              >
                                URL
                              </Label>
                              <Input
                                id="link-url"
                                type="url"
                                value={captionLinkUrl}
                                onChange={(e) =>
                                  setCaptionLinkUrl(e.target.value)
                                }
                                placeholder="https://example.com"
                                className="text-sm"
                              />
                            </div>
                            <div className="flex gap-2">
                              <Button
                                type="button"
                                size="sm"
                                onClick={insertLinkInCaption}
                                className="flex-1"
                              >
                                Insert Link
                              </Button>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => setIsLinkPopoverOpen(false)}
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={removeLinkFromSelection}
                        className="h-7 px-2 text-xs"
                        title="Remove link from selected text"
                      >
                        <Unlink className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                  <Textarea
                    ref={captionTextareaRef}
                    id="caption"
                    value={selectedImageData.caption || ""}
                    onChange={(e) =>
                      handleInputChange("caption", e.target.value)
                    }
                    placeholder="Add a caption that will appear below the image..."
                    rows={4}
                    className="w-full resize-none font-mono text-sm"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Caption supports HTML links. Select text and click "Link" to
                    add links, or type HTML manually:
                    <code className="mx-1 px-1 py-0.5 bg-gray-100 dark:bg-gray-800 rounded text-xs">
                      &lt;a href="url"&gt;text&lt;/a&gt;
                    </code>
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="link" className="space-y-4 mt-4">
                {/* Enable Link Toggle */}
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                  <div className="flex items-center gap-2">
                    <LinkIcon className="w-4 h-4" />
                    <span className="font-medium">Link Image</span>
                  </div>
                  <Switch
                    checked={isLinkEnabled}
                    onCheckedChange={handleLinkToggle}
                  />
                </div>

                {isLinkEnabled && (
                  <div className="space-y-4">
                    {/* Link URL */}
                    <div className="space-y-2">
                      <Label
                        htmlFor="link-url"
                        className="flex items-center gap-2"
                      >
                        <ExternalLink className="w-4 h-4" />
                        Link URL
                      </Label>
                      <Input
                        id="link-url"
                        type="url"
                        value={selectedImageData.link || ""}
                        onChange={(e) =>
                          handleInputChange("link", e.target.value)
                        }
                        placeholder="https://example.com or /internal-page"
                        className="w-full"
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        When readers click the image, they'll be taken to this
                        URL.
                      </p>
                    </div>

                    {/* Link Target */}
                    <div className="space-y-2">
                      <Label htmlFor="link-target">Link Target</Label>
                      <div className="flex gap-3">
                        <label className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="radio"
                            name="linkTarget"
                            value="_self"
                            checked={selectedImageData.linkTarget === "_self"}
                            onChange={(e) =>
                              handleInputChange("linkTarget", e.target.value)
                            }
                            className="w-4 h-4 text-primary"
                          />
                          <span className="text-sm">Same window</span>
                        </label>
                        <label className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="radio"
                            name="linkTarget"
                            value="_blank"
                            checked={selectedImageData.linkTarget === "_blank"}
                            onChange={(e) =>
                              handleInputChange("linkTarget", e.target.value)
                            }
                            className="w-4 h-4 text-primary"
                          />
                          <span className="text-sm">New window</span>
                        </label>
                      </div>
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </div>

        <CustomDialogFooter className="flex justify-between">
          <div>
            {handleImageDelete && (
              <Button
                type="button"
                variant="destructive"
                onClick={handleImageDelete}
                className="flex items-center gap-2 text-white"
              >
                <X className="w-4 h-4" />
                Remove Image
              </Button>
            )}
          </div>
          <div className="flex gap-2">
            <Button type="button" variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleSave}
              className="flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              Save Changes
            </Button>
          </div>
        </CustomDialogFooter>
      </CustomDialogContent>
    </CustomDialog>
  );
}
