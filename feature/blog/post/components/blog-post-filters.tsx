"use client";

import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Search, X, Filter, RotateCcw } from "lucide-react";
import {
  useBlogPostFiltersState,
  useBlogPostFiltersActions,
} from "../store/use-blog-post-store";
import { useBlogConfig } from "../hooks/use-blog-config";
import { BlogStatus } from "@/types/blog-api";

export function BlogPostFilters() {
  const {
    searchQuery,
    statusFilter,
    authorFilter,
    categoryFilter,
    tagFilter,
    publishedFilter,
  } = useBlogPostFiltersState();

  const {
    setSearchQuery,
    setStatusFilter,
    setAuthorFilter,
    setCate<PERSON>y<PERSON><PERSON><PERSON>,
    setTagFilter,
    setPublishedFilter,
    resetFilters,
  } = useBlogPostFiltersActions();

  const { data: blogConfig } = useBlogConfig();

  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchQuery(localSearchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [localSearchQuery, setSearchQuery]);

  const activeFiltersCount = [
    statusFilter !== "all",
    authorFilter !== "all",
    categoryFilter !== "all",
    tagFilter !== "all",
    publishedFilter !== "all",
    searchQuery,
  ].filter(Boolean).length;

  const statusOptions = [
    { value: "all", label: "All Statuses" },
    ...(blogConfig?.enums.blogStatuses || []).map((status) => ({
      value: status,
      label: status.charAt(0) + status.slice(1).toLowerCase(),
    })),
  ];

  const publishedOptions = [
    { value: "all", label: "All Posts" },
    { value: "true", label: "Published" },
    { value: "false", label: "Unpublished" },
  ];

  const categoryOptions = [
    { value: "all", label: "All Categories" },
    ...(blogConfig?.categories || []).map((category) => ({
      value: category.id.toString(),
      label: category.name,
    })),
  ];

  const tagOptions = [
    { value: "all", label: "All Tags" },
    ...(blogConfig?.tags || []).map((tag) => ({
      value: tag.id.toString(),
      label: tag.name,
    })),
  ];

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search blog posts..."
            value={localSearchQuery}
            onChange={(e) => setLocalSearchQuery(e.target.value)}
            className="pl-10"
          />
          {localSearchQuery && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0"
              onClick={() => {
                setLocalSearchQuery("");
                setSearchQuery("");
              }}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        <div className="flex gap-2 items-center">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Badge variant="secondary" className="text-xs">
            {activeFiltersCount} filter{activeFiltersCount !== 1 ? "s" : ""}
          </Badge>
          {activeFiltersCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={resetFilters}
              className="h-8 gap-1"
            >
              <RotateCcw className="h-3 w-3" />
              Reset
            </Button>
          )}
        </div>
      </div>

      {/* Filter Controls */}
      <div className="flex flex-row gap-4">
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger>
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            {statusOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={publishedFilter} onValueChange={setPublishedFilter}>
          <SelectTrigger>
            <SelectValue placeholder="Published" />
          </SelectTrigger>
          <SelectContent>
            {publishedOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger>
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            {categoryOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={tagFilter} onValueChange={setTagFilter}>
          <SelectTrigger>
            <SelectValue placeholder="Tag" />
          </SelectTrigger>
          <SelectContent>
            {tagOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={authorFilter} onValueChange={setAuthorFilter}>
          <SelectTrigger>
            <SelectValue placeholder="Author" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Authors</SelectItem>
            {/* Note: In a real app, you'd fetch authors from an API */}
            <SelectItem value="me">My Posts</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
