import { create } from "zustand";
import {
  BlogCategoryResponse,
  CreateBlogCategoryRequest,
  UpdateBlogCategoryRequest,
} from "@/types/blog-api";

// Blog category form data interfaces
export interface BlogCategoryFormData {
  name: string;
  description: string;
  slug: string;
  parent_id: number | null;
}

// Initial form data
const initialBlogCategoryFormData: BlogCategoryFormData = {
  name: "",
  description: "",
  slug: "",
  parent_id: null,
};

// Store interface
interface BlogCategoryStore {
  // List state
  currentPage: number;
  searchQuery: string;

  // Form state
  categoryFormData: BlogCategoryFormData;
  isCreateDialogOpen: boolean;
  isEditDialogOpen: boolean;
  isDeleteDialogOpen: boolean;
  categoryToEdit: BlogCategoryResponse | null;
  categoryToDelete: BlogCategoryResponse | null;

  // List actions
  setCurrentPage: (page: number) => void;
  setSearchQuery: (query: string) => void;
  resetFilters: () => void;

  // Form actions
  setCategoryFormData: (data: BlogCategoryFormData) => void;
  updateCategoryFormField: (
    field: keyof BlogCategoryFormData,
    value: string | number | null
  ) => void;
  resetCategoryForm: () => void;
  setIsCreateDialogOpen: (open: boolean) => void;
  setIsEditDialogOpen: (open: boolean) => void;
  setIsDeleteDialogOpen: (open: boolean) => void;
  setCategoryToEdit: (category: BlogCategoryResponse | null) => void;
  setCategoryToDelete: (category: BlogCategoryResponse | null) => void;

  // Helper actions
  openCreateDialog: () => void;
  openEditDialog: (category: BlogCategoryResponse) => void;
  openDeleteDialog: (category: BlogCategoryResponse) => void;
  closeAllDialogs: () => void;

  // Global actions
  resetAllState: () => void;
}

// Create the store
export const useBlogCategoryStore = create<BlogCategoryStore>((set) => ({
  // Initial state
  currentPage: 1,
  searchQuery: "",
  categoryFormData: initialBlogCategoryFormData,
  isCreateDialogOpen: false,
  isEditDialogOpen: false,
  isDeleteDialogOpen: false,
  categoryToEdit: null,
  categoryToDelete: null,

  // List actions
  setCurrentPage: (page) => set({ currentPage: page }),
  setSearchQuery: (query) => set({ searchQuery: query }),
  resetFilters: () =>
    set({
      searchQuery: "",
      currentPage: 1,
    }),

  // Form actions
  setCategoryFormData: (data) => set({ categoryFormData: data }),
  updateCategoryFormField: (field, value) =>
    set((state) => ({
      categoryFormData: {
        ...state.categoryFormData,
        [field]: value,
      },
    })),
  resetCategoryForm: () =>
    set({ categoryFormData: initialBlogCategoryFormData }),
  setIsCreateDialogOpen: (open) => set({ isCreateDialogOpen: open }),
  setIsEditDialogOpen: (open) => set({ isEditDialogOpen: open }),
  setIsDeleteDialogOpen: (open) => set({ isDeleteDialogOpen: open }),
  setCategoryToEdit: (category) => set({ categoryToEdit: category }),
  setCategoryToDelete: (category) => set({ categoryToDelete: category }),

  // Helper actions
  openCreateDialog: () => {
    set({
      isCreateDialogOpen: true,
      categoryFormData: initialBlogCategoryFormData,
    });
  },

  openEditDialog: (category) => {
    set({
      isEditDialogOpen: true,
      categoryToEdit: category,
      categoryFormData: {
        name: category.name,
        description: category.description || "",
        slug: category.slug,
        parent_id: category.parent_id,
      },
    });
  },

  openDeleteDialog: (category) => {
    set({
      isDeleteDialogOpen: true,
      categoryToDelete: category,
    });
  },

  closeAllDialogs: () => {
    set({
      isCreateDialogOpen: false,
      isEditDialogOpen: false,
      isDeleteDialogOpen: false,
      categoryToEdit: null,
      categoryToDelete: null,
    });
  },

  // Global actions
  resetAllState: () =>
    set({
      currentPage: 1,
      searchQuery: "",
      categoryFormData: initialBlogCategoryFormData,
      isCreateDialogOpen: false,
      isEditDialogOpen: false,
      isDeleteDialogOpen: false,
      categoryToEdit: null,
      categoryToDelete: null,
    }),
}));

// Selector hooks for specific parts of the state
export const useBlogCategoryListState = () => {
  const { currentPage, searchQuery } = useBlogCategoryStore();

  return {
    currentPage,
    searchQuery,
  };
};

export const useBlogCategoryListActions = () => {
  const { setCurrentPage, setSearchQuery, resetFilters } =
    useBlogCategoryStore();

  return {
    setCurrentPage,
    setSearchQuery,
    resetFilters,
  };
};

export const useBlogCategoryFormState = () => {
  const {
    categoryFormData,
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    categoryToEdit,
    categoryToDelete,
  } = useBlogCategoryStore();

  return {
    categoryFormData,
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    categoryToEdit,
    categoryToDelete,
  };
};

export const useBlogCategoryFormActions = () => {
  const {
    setCategoryFormData,
    updateCategoryFormField,
    resetCategoryForm,
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    setIsDeleteDialogOpen,
    setCategoryToEdit,
    setCategoryToDelete,
    openCreateDialog,
    openEditDialog,
    openDeleteDialog,
    closeAllDialogs,
  } = useBlogCategoryStore();

  return {
    setCategoryFormData,
    updateCategoryFormField,
    resetCategoryForm,
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    setIsDeleteDialogOpen,
    setCategoryToEdit,
    setCategoryToDelete,
    openCreateDialog,
    openEditDialog,
    openDeleteDialog,
    closeAllDialogs,
  };
};
