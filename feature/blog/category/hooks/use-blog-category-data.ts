import {
  useApiMutation,
  useApiQuery,
  apiPost,
  apiGet,
  apiPut,
  apiDelete,
  ApiError,
} from "@/feature/core/api/api-utils";
import {
  CreateBlogCategoryRequest,
  UpdateBlogCategoryRequest,
  BlogCategoryResponse,
  PaginatedResponse,
  ListQueryParams,
  BlogCategoryQueryParams,
} from "@/types/blog-api";
import { toast } from "sonner";

// Blog Category API functions
const blogCategoryApi = {
  getCategories: async (
    params?: BlogCategoryQueryParams
  ): Promise<PaginatedResponse<BlogCategoryResponse>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set("page", params.page.toString());
    if (params?.limit) searchParams.set("limit", params.limit.toString());
    if (params?.search) searchParams.set("search", params.search);
    if (params?.parent_id !== undefined)
      searchParams.set("parent_id", params.parent_id.toString());

    const queryString = searchParams.toString();
    const response = await apiGet<{
      data: PaginatedResponse<BlogCategoryResponse>;
    }>(`/api/blog/categories${queryString ? `?${queryString}` : ""}`);
    return response.data;
  },

  getCategory: async (id: number): Promise<BlogCategoryResponse> => {
    const response = await apiGet<{ data: BlogCategoryResponse }>(
      `/api/blog/categories/${id}`
    );
    return response.data;
  },

  createCategory: async (
    data: CreateBlogCategoryRequest
  ): Promise<BlogCategoryResponse> => {
    const response = await apiPost<{ data: BlogCategoryResponse }>(
      "/api/blog/categories",
      data
    );
    return response.data;
  },

  updateCategory: async (
    id: number,
    data: UpdateBlogCategoryRequest
  ): Promise<BlogCategoryResponse> => {
    const response = await apiPut<{ data: BlogCategoryResponse }>(
      `/api/blog/categories/${id}`,
      data
    );
    return response.data;
  },

  deleteCategory: async (id: number): Promise<{ id: number }> => {
    const response = await apiDelete<{ data: { id: number } }>(
      `/api/blog/categories/${id}`
    );
    return response.data;
  },
};

// Hook to get categories list
export function useGetBlogCategories(params?: BlogCategoryQueryParams) {
  return useApiQuery(
    ["blog-categories", JSON.stringify(params)],
    () => blogCategoryApi.getCategories(params),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

// Hook to get single category
export function useGetBlogCategory(id: number) {
  return useApiQuery(
    ["blog-category", id.toString()],
    () => blogCategoryApi.getCategory(id),
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Hook to create category
export function useCreateBlogCategory() {
  return useApiMutation(blogCategoryApi.createCategory, {
    showErrorToast: false,
    onSuccess: (data) => {
      toast.success("Blog category created successfully");
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to create blog category");
    },
    invalidateQueries: ["blog-categories"],
  });
}

// Hook to update category
export function useUpdateBlogCategory() {
  return useApiMutation(
    (variables: { id: number; data: UpdateBlogCategoryRequest }) =>
      blogCategoryApi.updateCategory(variables.id, variables.data),
    {
      showErrorToast: false,
      onSuccess: (data) => {
        toast.success("Blog category updated successfully");
      },
      onError: (error: ApiError) => {
        toast.error(error.message || "Failed to update blog category");
      },
      invalidateQueries: ["blog-categories", "blog-category"],
    }
  );
}

// Hook to delete category
export function useDeleteBlogCategory() {
  return useApiMutation(blogCategoryApi.deleteCategory, {
    showErrorToast: false,
    onSuccess: () => {
      toast.success("Blog category deleted successfully");
    },
    onError: (error: ApiError) => {
      toast.error(error.message || "Failed to delete blog category");
    },
    invalidateQueries: ["blog-categories"],
  });
}

// Custom hook that aggregates all category-related hooks
export function useBlogCategoryActions() {
  const createCategory = useCreateBlogCategory();
  const updateCategory = useUpdateBlogCategory();
  const deleteCategory = useDeleteBlogCategory();

  const isLoading =
    createCategory.isPending ||
    updateCategory.isPending ||
    deleteCategory.isPending;

  return {
    createCategory,
    updateCategory,
    deleteCategory,
    isLoading,
  };
}
