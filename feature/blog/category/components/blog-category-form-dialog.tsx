"use client";

import React, { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogDescription,
  CustomDialogFooter,
  CustomDialogHeader,
  CustomDialogTitle,
} from "@/components/custom/custom-dialog";
import {
  useBlogCategoryFormState,
  useBlogCategoryFormActions,
} from "@/feature/blog/category/store/use-blog-category-store";
import {
  useCreateBlogCategory,
  useUpdateBlogCategory,
  useGetBlogCategories,
} from "@/feature/blog/category/hooks/use-blog-category-data";
import { generateSlug } from "@/lib/slug-utils";

export function BlogCategoryFormDialog() {
  const {
    categoryFormData,
    isCreateDialogOpen,
    isEditDialogOpen,
    categoryToEdit,
  } = useBlogCategoryFormState();

  const { updateCategoryFormField, resetCategoryForm, closeAllDialogs } =
    useBlogCategoryFormActions();

  const createCategory = useCreateBlogCategory();
  const updateCategory = useUpdateBlogCategory();

  // Fetch categories for parent selection (only top-level categories)
  const { data: parentCategoriesData } = useGetBlogCategories({
    parent_id: undefined, // Only get top-level categories
    limit: 100, // Get all top-level categories
  });

  const isOpen = isCreateDialogOpen || isEditDialogOpen;
  const isEditing = isEditDialogOpen && categoryToEdit;
  const title = isEditing ? "Edit Category" : "Create Category";
  const description = isEditing
    ? "Update the category information below."
    : "Create a new blog category to organize your content.";

  // Auto-generate slug from name
  const handleNameChange = (value: string) => {
    updateCategoryFormField("name", value);
    if (!isEditing && value) {
      const slug = generateSlug(value);
      updateCategoryFormField("slug", slug);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!categoryFormData.name.trim()) {
      return;
    }

    try {
      if (isEditing && categoryToEdit) {
        await updateCategory.mutateAsync({
          id: categoryToEdit.id,
          data: {
            name: categoryFormData.name,
            description: categoryFormData.description || undefined,
            slug: categoryFormData.slug || undefined,
            parent_id: categoryFormData.parent_id || undefined,
          },
        });
      } else {
        await createCategory.mutateAsync({
          name: categoryFormData.name,
          description: categoryFormData.description || undefined,
          slug: categoryFormData.slug || undefined,
          parent_id: categoryFormData.parent_id || undefined,
        });
      }

      handleClose();
    } catch (error) {
      // Error handling is done in the hooks
      console.error("Form submission error:", error);
    }
  };

  const handleClose = () => {
    resetCategoryForm();
    closeAllDialogs();
  };

  const isLoading = createCategory.isPending || updateCategory.isPending;

  // Get available parent categories (exclude current category when editing)
  const availableParentCategories =
    parentCategoriesData?.items.filter(
      (cat) => !isEditing || cat.id !== categoryToEdit?.id
    ) || [];

  return (
    <CustomDialog open={isOpen} onOpenChange={handleClose}>
      <CustomDialogContent className="sm:max-w-xl">
        <form onSubmit={handleSubmit}>
          <CustomDialogHeader>
            <CustomDialogTitle>{title}</CustomDialogTitle>
            <CustomDialogDescription className="p-0">{description}</CustomDialogDescription>
          </CustomDialogHeader>

          <div className="grid gap-6 p-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                value={categoryFormData.name}
                onChange={(e) => handleNameChange(e.target.value)}
                placeholder="Enter category name"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  value={categoryFormData.slug}
                  onChange={(e) =>
                    updateCategoryFormField("slug", e.target.value)
                  }
                  placeholder="auto-generated"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="parent">Parent Category</Label>
                <Select
                  value={categoryFormData.parent_id?.toString() || "none"}
                  onValueChange={(value) =>
                    updateCategoryFormField(
                      "parent_id",
                      value === "none" ? null : parseInt(value)
                    )
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select parent category (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No parent (top-level)</SelectItem>
                    {availableParentCategories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id.toString()}
                      >
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={categoryFormData.description}
                onChange={(e) =>
                  updateCategoryFormField("description", e.target.value)
                }
                placeholder="Enter category description"
                className="min-h-[80px]"
              />
            </div>
          </div>

          <CustomDialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading
                ? isEditing
                  ? "Updating..."
                  : "Creating..."
                : isEditing
                  ? "Update Category"
                  : "Create Category"}
            </Button>
          </CustomDialogFooter>
        </form>
      </CustomDialogContent>
    </CustomDialog>
  );
}
