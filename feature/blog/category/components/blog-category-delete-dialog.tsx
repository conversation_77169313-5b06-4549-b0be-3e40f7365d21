"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  useBlogCategoryFormState,
  useBlogCategoryFormActions,
} from "@/feature/blog/category/store/use-blog-category-store";
import { useDeleteBlogCategory } from "@/feature/blog/category/hooks/use-blog-category-data";

export function BlogCategoryDeleteDialog() {
  const { isDeleteDialogOpen, categoryToDelete } = useBlogCategoryFormState();
  const { closeAllDialogs } = useBlogCategoryFormActions();
  const deleteCategory = useDeleteBlogCategory();

  const handleDelete = async () => {
    if (!categoryToDelete) return;

    try {
      await deleteCategory.mutateAsync(categoryToDelete.id);
      closeAllDialogs();
    } catch (error) {
      // Error handling is done in the hook
      console.error("Delete error:", error);
    }
  };

  const handleClose = () => {
    closeAllDialogs();
  };

  if (!categoryToDelete) return null;

  const hasChildren =
    categoryToDelete.children && categoryToDelete.children.length > 0;

  return (
    <AlertDialog open={isDeleteDialogOpen} onOpenChange={handleClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Category</AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-2">
              <p>
                Are you sure you want to delete the category{" "}
                <strong>"{categoryToDelete.name}"</strong>?
              </p>
              {hasChildren && (
                <p className="text-destructive">
                  <strong>Warning:</strong> This category has{" "}
                  {categoryToDelete.children?.length} subcategory(ies). You must
                  delete all subcategories first.
                </p>
              )}
              <p className="text-sm text-muted-foreground">
                This action cannot be undone. The category will be permanently
                removed from the system.
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteCategory.isPending}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteCategory.isPending || hasChildren}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {deleteCategory.isPending ? "Deleting..." : "Delete Category"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
