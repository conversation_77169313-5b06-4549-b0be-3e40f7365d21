import {
  useApiMutation,
  apiPost,
  ApiError,
} from "@/feature/core/api/api-utils";
import { signIn, SignInResponse } from "next-auth/react";
import { useRouter } from "next/navigation";

// Type definitions for API requests and responses
export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
}

export interface RegisterResponse {
  message: string;
  requiresVerification: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
}

export interface CheckUserRequest {
  email: string;
}

export interface CheckUserResponse {
  exists: boolean;
  isVerified: boolean;
  canResendVerification: boolean;
}

export interface ResendVerificationRequest {
  email: string;
}

export interface ResendVerificationResponse {
  message: string;
  remainingAttempts: number;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ForgotPasswordResponse {
  message: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
}

export interface ResetPasswordResponse {
  message: string;
}

export interface VerifyResetTokenRequest {
  token: string;
}

export interface VerifyResetTokenResponse {
  valid: boolean;
  message?: string;
}

export interface GoogleSignInRequest {
  // No specific data needed for Google sign-in
}

// Auth API functions
const authApi = {
  register: (data: RegisterRequest): Promise<RegisterResponse> =>
    apiPost("/api/auth/register", data),

  checkUser: (data: CheckUserRequest): Promise<CheckUserResponse> =>
    apiPost("/api/auth/check-user", data),

  resendVerification: (
    data: ResendVerificationRequest
  ): Promise<ResendVerificationResponse> =>
    apiPost("/api/auth/resend-verification", data),

  forgotPassword: (
    data: ForgotPasswordRequest
  ): Promise<ForgotPasswordResponse> =>
    apiPost("/api/auth/forgot-password", data),

  resetPassword: (data: ResetPasswordRequest): Promise<ResetPasswordResponse> =>
    apiPost("/api/auth/reset-password", data),

  verifyResetToken: (
    data: VerifyResetTokenRequest
  ): Promise<VerifyResetTokenResponse> =>
    apiPost("/api/auth/verify-reset-token", data),
};

export function useRegister() {
  const router = useRouter();

  return useApiMutation(authApi.register, {
    showErrorToast: false,
  });
}

// Custom hook for user login
export function useLogin() {
  const router = useRouter();

  return useApiMutation(
    async (data: LoginRequest): Promise<LoginResponse> => {
      const result = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      if (result?.error) {
        throw new Error(result.error);
      }

      return { success: true, message: "Login successful" };
    },
    {
      onSuccess: () => {
        router.push("/");
        router.refresh();
      },
      showErrorToast: false,
    }
  );
}

// Custom hook for checking if user exists
export function useCheckUser() {
  return useApiMutation(authApi.checkUser, {
    showErrorToast: false,
  });
}

// Custom hook for resending verification email
export function useResendVerification() {
  return useApiMutation(authApi.resendVerification, {
    showErrorToast: false,
  });
}

// Custom hook for forgot password
export function useForgotPassword() {
  return useApiMutation(authApi.forgotPassword, {
    showErrorToast: false,
  });
}

// Custom hook for password reset
export function useResetPassword() {
  const router = useRouter();

  return useApiMutation(authApi.resetPassword, {
    onSuccess: () => {
      router.push("/login");
    },
    showErrorToast: false,
  });
}

// Custom hook for verifying reset token
export function useVerifyResetToken() {
  return useApiMutation(authApi.verifyResetToken, {
    showErrorToast: false,
  });
}

// Custom hook for Google sign-in
export function useGoogleSignIn() {
  return useApiMutation(
    async (): Promise<void> => {
      await signIn("google", { callbackUrl: "/" });
    },
    {
      showErrorToast: false,
    }
  );
}

// Custom hook that aggregates all auth-related hooks
export function useAuthActions() {
  const register = useRegister();
  const login = useLogin();
  const checkUser = useCheckUser();
  const resendVerification = useResendVerification();
  const forgotPassword = useForgotPassword();

  const isLoading =
    register.isPending ||
    login.isPending ||
    resendVerification.isPending ||
    forgotPassword.isPending;

  return {
    register,
    login,
    checkUser,
    resendVerification,
    forgotPassword,
    isLoading,
  };
}
