import { prisma } from "@/lib/prisma";

export interface EmailRateLimitResult {
  canSend: boolean;
  reason?: string;
  nextAllowedTime?: Date;
  remainingAttempts?: number;
}

export interface EmailRateLimitConfig {
  maxVerificationEmails: number;
  verificationCooldownHours: number;
  maxPasswordResets: number;
  passwordResetCooldownDays: number;
}

const DEFAULT_CONFIG: EmailRateLimitConfig = {
  maxVerificationEmails: 2,
  verificationCooldownHours: 1,
  maxPasswordResets: 1,
  passwordResetCooldownDays: 3,
};

// Helper function to replace placeholders in translated strings
function formatMessage(
  template: string,
  replacements: Record<string, string | number>
): string {
  return Object.entries(replacements).reduce(
    (message, [key, value]) => message.replace(`{${key}}`, String(value)),
    template
  );
}

export async function checkVerificationEmailLimit(
  email: string,
  dict?: any,
  config: EmailRateLimitConfig = DEFAULT_CONFIG
): Promise<EmailRateLimitResult> {
  try {
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        verificationEmailCount: true,
        lastVerificationEmailSent: true,
        isVerified: true,
      },
    });

    if (!user) {
      return {
        canSend: false,
        reason: dict?.rate_limit?.user_not_found || "User not found",
      };
    }

    if (user.isVerified) {
      return {
        canSend: false,
        reason:
          dict?.rate_limit?.email_already_verified || "Email already verified",
      };
    }

    // Check if user has exceeded max verification emails
    if (user.verificationEmailCount >= config.maxVerificationEmails) {
      const message =
        dict?.rate_limit?.max_verification_emails ||
        "Maximum verification emails ({count}) reached. Please contact support if you need assistance.";

      return {
        canSend: false,
        reason: formatMessage(message, { count: config.maxVerificationEmails }),
        remainingAttempts: 0,
      };
    }

    // Check cooldown period
    if (user.lastVerificationEmailSent) {
      const cooldownEnd = new Date(
        user.lastVerificationEmailSent.getTime() +
          config.verificationCooldownHours * 60 * 60 * 1000
      );
      const now = new Date();

      if (now < cooldownEnd) {
        const minutes = Math.ceil(
          (cooldownEnd.getTime() - now.getTime()) / (1000 * 60)
        );
        const message =
          dict?.rate_limit?.verification_cooldown ||
          "Please wait before requesting another verification email. You can try again in {minutes} minutes.";

        return {
          canSend: false,
          reason: formatMessage(message, { minutes }),
          nextAllowedTime: cooldownEnd,
        };
      }
    }

    return {
      canSend: true,
      remainingAttempts:
        config.maxVerificationEmails - user.verificationEmailCount,
    };
  } catch (error) {
    return {
      canSend: false,
      reason: dict?.rate_limit?.internal_error || "Internal error",
    };
  }
}

export async function checkPasswordResetLimit(
  email: string,
  dict?: any,
  config: EmailRateLimitConfig = DEFAULT_CONFIG
): Promise<EmailRateLimitResult> {
  try {
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        passwordResetCount: true,
        lastPasswordResetSent: true,
      },
    });

    if (!user) {
      return {
        canSend: false,
        reason: dict?.rate_limit?.user_not_found || "User not found",
      };
    }

    // Check if user has exceeded max password resets
    if (user.passwordResetCount >= config.maxPasswordResets) {
      // Check if cooldown period has passed
      if (user.lastPasswordResetSent) {
        const cooldownEnd = new Date(
          user.lastPasswordResetSent.getTime() +
            config.passwordResetCooldownDays * 24 * 60 * 60 * 1000
        );
        const now = new Date();

        if (now < cooldownEnd) {
          const daysRemaining = Math.ceil(
            (cooldownEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
          );
          const message =
            dict?.rate_limit?.max_password_resets ||
            "You can request a password reset again in {days} day(s). This limit helps protect your account security.";

          return {
            canSend: false,
            reason: formatMessage(message, { days: daysRemaining }),
            nextAllowedTime: cooldownEnd,
          };
        } else {
          // Reset the counter after cooldown period
          await prisma.user.update({
            where: { email },
            data: {
              passwordResetCount: 0,
            },
          });
        }
      }
    }

    return { canSend: true };
  } catch (error) {
    return {
      canSend: false,
      reason: dict?.rate_limit?.internal_error || "Internal error",
    };
  }
}

export async function incrementVerificationEmailCount(
  email: string
): Promise<void> {
  try {
    await prisma.user.update({
      where: { email },
      data: {
        verificationEmailCount: { increment: 1 },
        lastVerificationEmailSent: new Date(),
      },
    });
  } catch (error) {
    // Silent fail
  }
}

export async function incrementPasswordResetCount(
  email: string
): Promise<void> {
  try {
    await prisma.user.update({
      where: { email },
      data: {
        passwordResetCount: { increment: 1 },
        lastPasswordResetSent: new Date(),
      },
    });
  } catch (error) {
    // Silent fail
  }
}

export async function resetVerificationEmailCount(
  email: string
): Promise<void> {
  try {
    await prisma.user.update({
      where: { email },
      data: {
        verificationEmailCount: 0,
        lastVerificationEmailSent: null,
      },
    });
  } catch (error) {
    // Silent fail
  }
}
