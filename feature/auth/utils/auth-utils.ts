import { auth } from "@/auth";
import { redirect } from "next/navigation";

export async function getCurrentUser() {
  const session = await auth();
  return session?.user;
}

export async function requireAuth() {
  const user = await getCurrentUser();
  if (!user) {
    redirect("/login");
  }
  return user;
}

export async function requireAdmin() {
  const user = await requireAuth();
  if (user.role !== "ADMIN") {
    redirect("/");
  }
  return user;
}

export async function requireAuthor() {
  const user = await requireAuth();
  if (user.role !== "AUTHOR" && user.role !== "ADMIN") {
    redirect("/");
  }
  return user;
}

export function isAdmin(role?: string) {
  return role === "ADMIN";
}

export function isUser(role?: string) {
  return role === "USER";
}

export function isAuthor(role?: string) {
  return role === "AUTHOR";
}

export function hasAuthorAccess(role?: string) {
  return role === "AUTHOR" || role === "ADMIN";
}
