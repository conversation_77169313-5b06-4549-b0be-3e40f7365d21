import { Resend } from "resend";
import { render } from "@react-email/render";
import EmailVerificationTemplate from "@/feature/core/email/email-verification";
import WelcomeEmailTemplate from "@/feature/core/email/welcome-email";
import PasswordResetTemplate from "@/feature/core/email/password-reset";

const resend = new Resend(process.env.RESEND_API_KEY);

export const sendVerificationEmail = async (
  email: string,
  token: string,
  lang: string = "en"
) => {
  // Don't include "en" in URL since it's the default locale
  const langPath = lang === "en" ? "" : `/${lang}`;
  const confirmLink = `${process.env.NEXT_PUBLIC_APP_URL}${langPath}/login?token=${token}`;

  const subject = lang === "es" ? "Confirma tu email" : "Confirm your email";

  try {
    const emailHtml = await render(
      EmailVerificationTemplate({
        userEmail: email,
        verificationUrl: confirmLink,
      })
    );

    const { data, error } = await resend.emails.send({
      from: "AppList <<EMAIL>>",
      to: [email],
      subject,
      html: emailHtml,
    });

    if (error) {
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    throw error;
  }
};

export const sendWelcomeEmail = async (
  email: string,
  name: string,
  lang: string = "en"
) => {
  const subject =
    lang === "es" ? "¡Bienvenido a AppList!" : "Welcome to AppList!";

  try {
    const emailHtml = await render(
      WelcomeEmailTemplate({
        userName: name,
        userEmail: email,
      })
    );

    const { data, error } = await resend.emails.send({
      from: "AppList <<EMAIL>>",
      to: [email],
      subject,
      html: emailHtml,
    });

    if (error) {
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    throw error;
  }
};

export const sendPasswordResetEmail = async (
  email: string,
  token: string,
  lang: string = "en"
) => {
  // Don't include "en" in URL since it's the default locale
  const langPath = lang === "en" ? "" : `/${lang}`;
  const resetLink = `${process.env.NEXT_PUBLIC_APP_URL}${langPath}/reset-password?token=${token}`;

  const subject =
    lang === "es"
      ? "Restablecer contraseña - AppList"
      : "Reset your password - AppList";

  try {
    const emailHtml = await render(
      PasswordResetTemplate({
        userEmail: email,
        resetUrl: resetLink,
      })
    );

    const { data, error } = await resend.emails.send({
      from: "AppList <<EMAIL>>",
      to: [email],
      subject,
      html: emailHtml,
    });

    if (error) {
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    throw error;
  }
};
