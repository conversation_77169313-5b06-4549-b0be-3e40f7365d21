import { create } from "zustand";
import { CheckUserResponse } from "@/feature/auth/hooks/use-auth-data";

// Auth form data interfaces
export interface LoginFormData {
  name: string;
  email: string;
  password: string;
}

export interface ResetPasswordFormData {
  password: string;
  confirmPassword: string;
}

// Auth store state interface
export interface AuthState {
  // Login/Register form state
  isLogin: boolean;
  loginFormData: LoginFormData;
  showEmailVerification: boolean;
  showForgotPassword: boolean;
  userCheckData: CheckUserResponse | null;

  // Email verification loading state
  isVerifyingEmail: boolean;

  // Reset password form state
  resetPasswordFormData: ResetPasswordFormData;
  showPassword: boolean;
  showConfirmPassword: boolean;
  resetToken: string | null;
  isValidToken: boolean | null;

  // Actions for login/register form
  setIsLogin: (isLogin: boolean) => void;
  setLoginFormData: (data: Partial<LoginFormData>) => void;
  updateLoginFormField: (field: keyof LoginFormData, value: string) => void;
  resetLoginForm: () => void;
  setShowEmailVerification: (show: boolean) => void;
  setShowForgotPassword: (show: boolean) => void;
  setUserCheckData: (data: CheckUserResponse | null) => void;

  // Email verification actions
  setIsVerifyingEmail: (isVerifying: boolean) => void;

  // Actions for reset password form
  setResetPasswordFormData: (data: Partial<ResetPasswordFormData>) => void;
  updateResetPasswordField: (
    field: keyof ResetPasswordFormData,
    value: string
  ) => void;
  resetResetPasswordForm: () => void;
  setShowPassword: (show: boolean) => void;
  setShowConfirmPassword: (show: boolean) => void;
  setResetToken: (token: string | null) => void;
  setIsValidToken: (isValid: boolean | null) => void;

  // Global actions
  resetAllState: () => void;
}

// Initial state values
const initialLoginFormData: LoginFormData = {
  name: "",
  email: "",
  password: "",
};

const initialResetPasswordFormData: ResetPasswordFormData = {
  password: "",
  confirmPassword: "",
};

// Create the Zustand store
export const useAuthStore = create<AuthState>((set, get) => ({
  // Initial state
  isLogin: true,
  loginFormData: initialLoginFormData,
  showEmailVerification: false,
  showForgotPassword: false,
  userCheckData: null,
  isVerifyingEmail: false,

  resetPasswordFormData: initialResetPasswordFormData,
  showPassword: false,
  showConfirmPassword: false,
  resetToken: null,
  isValidToken: null,

  // Login/Register form actions
  setIsLogin: (isLogin) => set({ isLogin }),

  setLoginFormData: (data) =>
    set((state) => ({
      loginFormData: { ...state.loginFormData, ...data },
    })),

  updateLoginFormField: (field, value) =>
    set((state) => ({
      loginFormData: { ...state.loginFormData, [field]: value },
    })),

  resetLoginForm: () => set({ loginFormData: initialLoginFormData }),

  setShowEmailVerification: (show) => set({ showEmailVerification: show }),

  setShowForgotPassword: (show) => set({ showForgotPassword: show }),

  setUserCheckData: (data) => set({ userCheckData: data }),

  // Email verification actions
  setIsVerifyingEmail: (isVerifying) => set({ isVerifyingEmail: isVerifying }),

  // Reset password form actions
  setResetPasswordFormData: (data) =>
    set((state) => ({
      resetPasswordFormData: { ...state.resetPasswordFormData, ...data },
    })),

  updateResetPasswordField: (field, value) =>
    set((state) => ({
      resetPasswordFormData: { ...state.resetPasswordFormData, [field]: value },
    })),

  resetResetPasswordForm: () =>
    set({ resetPasswordFormData: initialResetPasswordFormData }),

  setShowPassword: (show) => set({ showPassword: show }),

  setShowConfirmPassword: (show) => set({ showConfirmPassword: show }),

  setResetToken: (token) => set({ resetToken: token }),

  setIsValidToken: (isValid) => set({ isValidToken: isValid }),

  // Global actions
  resetAllState: () =>
    set({
      isLogin: true,
      loginFormData: initialLoginFormData,
      showEmailVerification: false,
      showForgotPassword: false,
      userCheckData: null,
      isVerifyingEmail: false,
      resetPasswordFormData: initialResetPasswordFormData,
      showPassword: false,
      showConfirmPassword: false,
      resetToken: null,
      isValidToken: null,
    }),
}));

// Selector hooks for specific parts of the state
export const useLoginFormState = () => {
  const {
    isLogin,
    loginFormData,
    showEmailVerification,
    showForgotPassword,
    userCheckData,
    isVerifyingEmail,
  } = useAuthStore();

  return {
    isLogin,
    loginFormData,
    showEmailVerification,
    showForgotPassword,
    userCheckData,
    isVerifyingEmail,
  };
};

export const useLoginFormActions = () => {
  const {
    setIsLogin,
    setLoginFormData,
    updateLoginFormField,
    resetLoginForm,
    setShowEmailVerification,
    setShowForgotPassword,
    setUserCheckData,
    setIsVerifyingEmail,
  } = useAuthStore();

  return {
    setIsLogin,
    setLoginFormData,
    updateLoginFormField,
    resetLoginForm,
    setShowEmailVerification,
    setShowForgotPassword,
    setUserCheckData,
    setIsVerifyingEmail,
  };
};

export const useResetPasswordState = () => {
  const {
    resetPasswordFormData,
    showPassword,
    showConfirmPassword,
    resetToken,
    isValidToken,
  } = useAuthStore();

  return {
    resetPasswordFormData,
    showPassword,
    showConfirmPassword,
    resetToken,
    isValidToken,
  };
};

export const useResetPasswordActions = () => {
  const {
    setResetPasswordFormData,
    updateResetPasswordField,
    resetResetPasswordForm,
    setShowPassword,
    setShowConfirmPassword,
    setResetToken,
    setIsValidToken,
  } = useAuthStore();

  return {
    setResetPasswordFormData,
    updateResetPasswordField,
    resetResetPasswordForm,
    setShowPassword,
    setShowConfirmPassword,
    setResetToken,
    setIsValidToken,
  };
};
