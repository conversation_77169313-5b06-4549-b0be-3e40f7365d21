"use client";

import React, { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Eye, EyeOff, CheckCircle } from "lucide-react";
import { toast } from "sonner";
import {
  useVerifyResetToken,
  useResetPassword,
} from "@/feature/auth/hooks/use-auth-data";
import {
  useResetPasswordState,
  useResetPasswordActions,
  ResetPasswordFormData,
} from "@/feature/auth/store/use-auth-store";

interface ResetPasswordProps {
  dict: any;
  lang: string;
}

const ResetPassword = ({ dict, lang }: ResetPasswordProps) => {
  const searchParams = useSearchParams();

  // Get state from Zustand store
  const {
    resetPasswordFormData,
    showPassword,
    showConfirmPassword,
    resetToken,
    isValidToken,
  } = useResetPasswordState();

  // Get actions from Zustand store
  const {
    updateResetPasswordField,
    setShowPassword,
    setShowConfirmPassword,
    setResetToken,
    setIsValidToken,
  } = useResetPasswordActions();

  // Get auth hooks
  const verifyResetToken = useVerifyResetToken();
  const resetPassword = useResetPassword();

  useEffect(() => {
    const tokenParam = searchParams.get("token");
    if (tokenParam) {
      setResetToken(tokenParam);
      handleVerifyToken(tokenParam);
    } else {
      setIsValidToken(false);
      toast.error(dict.auth.reset_link_expired);
    }
  }, [searchParams, setResetToken, setIsValidToken, dict]);

  const handleVerifyToken = async (tokenToVerify: string) => {
    try {
      await verifyResetToken.mutateAsync({ token: tokenToVerify });
      setIsValidToken(true);
    } catch (error: any) {
      setIsValidToken(false);
      toast.error(error.message || dict.auth.reset_link_expired);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateResetPasswordField(
      e.target.name as keyof ResetPasswordFormData,
      e.target.value
    );
  };

  const validatePassword = (password: string) => {
    if (password.length < 8) {
      return dict.errors.password_min_length;
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return dict.errors.password_lowercase;
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return dict.errors.password_uppercase;
    }
    if (!/(?=.*\d)/.test(password)) {
      return dict.errors.password_number;
    }
    return null;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!resetToken) {
      toast.error(dict.errors.invalid_reset_token);
      return;
    }

    // Validate passwords
    const passwordError = validatePassword(resetPasswordFormData.password);
    if (passwordError) {
      toast.error(passwordError);
      return;
    }

    if (
      resetPasswordFormData.password !== resetPasswordFormData.confirmPassword
    ) {
      toast.error(dict.errors.passwords_not_match);
      return;
    }

    resetPassword.mutate(
      {
        token: resetToken,
        password: resetPasswordFormData.password,
      },
      {
        onSuccess: () => {
          toast.success(dict.success.password_reset_success);
        },
        onError: (error) => {
          toast.error(error.message || dict.errors.failed_reset_password);
        },
      }
    );
  };

  // Loading state
  if (verifyResetToken.isPending || isValidToken === null) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md p-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted-foreground">
              {dict.auth.verifying_reset_link}
            </p>
          </div>
        </Card>
      </div>
    );
  }

  // Invalid token state
  if (isValidToken === false) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md p-6">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
              <svg
                className="w-8 h-8 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-red-600">
              {dict.auth.invalid_reset_link}
            </h1>
            <p className="text-muted-foreground">
              {dict.auth.reset_link_expired}
            </p>
            <Button
              onClick={() => (window.location.href = "/login")}
              className="w-full"
            >
              {dict.auth.back_to_login}
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  // Valid token - show reset form
  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md p-6">
        <div className="space-y-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h1 className="text-2xl font-bold">{dict.auth.reset_password}</h1>
            <p className="text-muted-foreground">
              {dict.auth.enter_new_password_below}
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password">{dict.auth.new_password}</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  placeholder={dict.auth.enter_new_password}
                  value={resetPasswordFormData.password}
                  onChange={handleInputChange}
                  required
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
              </div>
              <p className="text-xs text-muted-foreground">
                {dict.auth.password_requirements}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">
                {dict.auth.confirm_new_password}
              </Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder={dict.auth.confirm_password_placeholder}
                  value={resetPasswordFormData.confirmPassword}
                  onChange={handleInputChange}
                  required
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={resetPassword.isPending}
            >
              {resetPassword.isPending
                ? dict.auth.resetting_password
                : dict.auth.reset_password}
            </Button>
          </form>

          <div className="text-center">
            <button
              type="button"
              className="text-sm text-primary hover:underline"
              onClick={() => (window.location.href = "/login")}
            >
              {dict.auth.back_to_login}
            </button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ResetPassword;
