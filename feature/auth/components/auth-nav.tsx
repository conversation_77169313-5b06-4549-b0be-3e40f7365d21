"use client";

import { useSession, signOut } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import { User, LogOut, Shield, PenTool } from "lucide-react";
import Link from "next/link";
import { getLocalizedUrl } from "@/lib/utils";
import { FaUser } from "react-icons/fa";
import { RiLogoutCircleRLine } from "react-icons/ri";

interface AuthNavProps {
  dict?: any;
  lang?: string;
}

export function AuthNav({ dict, lang }: AuthNavProps = {}) {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return <div className="h-9 w-20 bg-muted rounded animate-pulse" />;
  }

  if (!session) {
    return (
      <Link
        href={getLocalizedUrl("/login", lang)}
        className="flex flex-row items-center border py-2 px-3 rounded-full bg-primary text-white border-white font-semibold"
      >
        <FaUser className="w-4 h-4 mr-2" />
        <span className="text-sm">{dict?.common?.signIn || "Sign In"}</span>
      </Link>
    );
  }

  return (
    <div className="flex items-center gap-2">
      {/* <div className="flex items-center gap-2 text-sm">
        <div className="flex items-center gap-1">
          {session.user.role === "ADMIN" && (
            <Shield className="w-4 h-4 text-orange-500" />
          )}
          {session.user.role === "AUTHOR" && (
            <PenTool className="w-4 h-4 text-blue-500" />
          )}
          <span className="font-medium">
            {session.user.name || session.user.email}
          </span>
        </div>
        <span className="text-muted-foreground">({session.user.role})</span>
      </div> */}
      <div
        className="flex flex-row items-center border py-2 px-3 rounded-full bg-primary text-white border-white font-semibold"
        onClick={() => signOut({ callbackUrl: "/" })}
      >
        <RiLogoutCircleRLine className="w-4 h-4 mr-2" />
        <span className="text-sm">{dict?.common?.logout}</span>
      </div>
    </div>
  );
}
