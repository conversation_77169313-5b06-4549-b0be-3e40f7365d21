"use client";

import React, { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Chrome, Mail, CheckCircle, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { signIn } from "next-auth/react";
import {
  useAuthActions,
  useResendVerification,
  useForgotPassword,
  CheckUserResponse,
} from "@/feature/auth/hooks/use-auth-data";
import {
  useLoginFormState,
  useLoginFormActions,
  LoginFormData,
} from "@/feature/auth/store/use-auth-store";

interface AuthProps {
  dict: any;
  lang: string;
}

const Auth = ({ dict, lang }: AuthProps) => {
  const searchParams = useSearchParams();

  // Get state from Zustand store
  const {
    isLogin,
    loginFormData,
    showEmailVerification,
    showForgotPassword,
    userCheckData,
    isVerifyingEmail,
  } = useLoginFormState();

  // Get actions from Zustand store
  const {
    setIsLogin,
    updateLoginFormField,
    setShowEmailVerification,
    setShowForgotPassword,
    setUserCheckData,
    setIsVerifyingEmail,
  } = useLoginFormActions();

  const { register, login, checkUser, isLoading } = useAuthActions();

  const resendVerification = useResendVerification();
  const forgotPassword = useForgotPassword();

  useEffect(() => {
    const token = searchParams.get("token");
    const verified = searchParams.get("verified");
    const error = searchParams.get("error");

    // Handle email verification token
    if (token && !verified && !error) {
      // Show verification loading state
      setIsVerifyingEmail(true);
      // Redirect to verify-email API endpoint to process the token
      window.location.href = `/api/auth/verify-email?token=${token}`;
      return;
    }

    // Reset verification loading state
    setIsVerifyingEmail(false);

    if (verified === "true") {
      toast.success(dict.success.email_verified);
    } else if (error === "EmailNotVerified") {
      toast.error(dict.errors.email_not_verified);
      setShowEmailVerification(true);
    } else if (error === "InvalidToken" || error === "ExpiredToken") {
      toast.error(dict.errors.invalid_expired_token);
      setShowEmailVerification(true);
    } else if (error === "VerificationFailed") {
      toast.error(dict.errors.verification_failed);
    }
  }, [searchParams, setShowEmailVerification, setIsVerifyingEmail, dict]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateLoginFormField(e.target.name as keyof LoginFormData, e.target.value);
  };

  const handleResendVerificationEmail = () => {
    if (!loginFormData.email) {
      toast.error(dict.errors.enter_email_address);
      return;
    }

    resendVerification.mutate(
      { email: loginFormData.email },
      {
        onSuccess: (result: any) => {
          toast.success(dict.success.verification_email_sent);
          if (result.remainingAttempts === 0) {
            setShowEmailVerification(false);
          }
        },
        onError: (error: any) => {
          if (error.status === 429) {
            toast.error(error.message, { duration: 8000 });
          } else {
            toast.error(error.message || dict.errors.reset_password_failed);
          }
          setShowEmailVerification(false);
        },
      }
    );
  };

  const handleForgotPassword = () => {
    if (!loginFormData.email) {
      toast.error(dict.errors.enter_email_address);
      return;
    }

    forgotPassword.mutate(
      { email: loginFormData.email },
      {
        onSuccess: () => {
          toast.success(dict.success.password_reset_email_sent);
          setShowForgotPassword(false);
        },
        onError: (error: any) => {
          if (error.status === 429) {
            toast.error(error.message, { duration: 8000 });
          } else {
            toast.error(error.message || dict.errors.reset_password_failed);
          }
        },
      }
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isLogin) {
      login.mutate(
        {
          email: loginFormData.email,
          password: loginFormData.password,
        },
        {
          onError: async (error) => {
            if (error.message === "CredentialsSignin") {
              try {
                const userData = await checkUser.mutateAsync({
                  email: loginFormData.email,
                });
                if (userData.exists && !userData.isVerified) {
                  toast.error(dict.errors.email_not_verified, {
                    duration: 6000,
                  });
                  setShowEmailVerification(userData.canResendVerification);
                  setUserCheckData(userData);
                  return;
                }
                toast.error(dict.errors.invalid_credentials);
              } catch (checkError) {
                toast.error(dict.errors.invalid_credentials);
              }
            } else {
              toast.error(dict.errors.signin_error);
            }
          },
        }
      );
    } else {
      register.mutate(loginFormData, {
        onSuccess: (result) => {
          if (result.requiresVerification) {
            toast.success(dict.success.verification_email_sent);
            setIsLogin(true);
          }
        },
        onError: (error) => {
          toast.error(error.message || dict.errors.registration_failed);
        },
      });
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      await signIn("google", { callbackUrl: "/" });
    } catch (error) {
      toast.error(dict.errors.google_signin_failed);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md p-6">
        <div className="space-y-6">
          {/* Email Verification Loading State */}
          {isVerifyingEmail && (
            <div className="text-center space-y-6">
              <div className="flex flex-col items-center space-y-4">
                <div className="relative">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <Loader2 className="w-8 h-8 text-white animate-spin" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <Mail className="w-3 h-3 text-white" />
                  </div>
                </div>

                <div className="space-y-2">
                  <h2 className="text-xl font-semibold text-gray-900">
                    {dict.auth.verifying_email || "Verifying Your Email"}
                  </h2>
                  <p className="text-sm text-gray-600 max-w-sm">
                    {dict.auth.please_wait_verifying ||
                      "Please wait while we verify your email address. This will only take a moment..."}
                  </p>
                </div>

                <div className="flex items-center space-x-2 text-xs text-gray-500">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                    <div
                      className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                  <span>{dict.auth.processing || "Processing"}</span>
                </div>
              </div>
            </div>
          )}
          {!isVerifyingEmail && !showForgotPassword && (
            <div className="text-center">
              <h1 className="text-2xl font-bold">
                {isLogin ? dict.auth.welcome_back : dict.auth.create_account}
              </h1>
              <p className="text-muted-foreground">
                {isLogin
                  ? dict.auth.sign_in_account
                  : dict.auth.sign_up_account}
              </p>
            </div>
          )}

          {!isVerifyingEmail && showEmailVerification && (
            <div className="p-4 text-sm bg-blue-50 rounded-md border border-blue-200">
              <div className="flex items-start gap-2 mb-3">
                <Mail className="w-4 h-4 mt-0.5 flex-shrink-0 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-800">
                    {dict.auth.email_verification_required}
                  </p>
                  <p className="text-blue-600 mt-1">
                    {dict.auth.check_email_verification}
                  </p>
                </div>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleResendVerificationEmail}
                disabled={resendVerification.isPending}
                className="w-full"
              >
                {resendVerification.isPending
                  ? dict.auth.sending
                  : dict.auth.resend_verification_email}
              </Button>
            </div>
          )}

          {!isVerifyingEmail && showForgotPassword ? (
            <div className="space-y-4">
              <div className="text-center">
                <Mail className="w-12 h-12 text-orange-600 mx-auto mb-3" />
                <h2 className="text-xl font-semibold text-orange-800">
                  {dict.auth.reset_password}
                </h2>
                <p className="text-muted-foreground mt-2">
                  {dict.auth.enter_email_reset}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">{dict.common.email}</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder={dict.auth.enter_your_email}
                  value={loginFormData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="flex gap-2">
                <Button
                  type="button"
                  onClick={handleForgotPassword}
                  disabled={forgotPassword.isPending}
                  className="flex-1"
                >
                  {forgotPassword.isPending
                    ? dict.auth.sending
                    : dict.auth.send_reset_link}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowForgotPassword(false)}
                  className="px-6"
                >
                  {dict.common.cancel}
                </Button>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              {!isLogin && (
                <div className="space-y-2">
                  <Label htmlFor="name">{dict.common.name}</Label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    placeholder={dict.auth.enter_your_name}
                    value={loginFormData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="email">{dict.common.email}</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder={dict.auth.enter_your_email}
                  value={loginFormData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">{dict.common.password}</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder={dict.auth.enter_your_password}
                  value={loginFormData.password}
                  onChange={handleInputChange}
                  required
                />
                {isLogin && (
                  <div className="text-right">
                    <button
                      type="button"
                      className="text-sm text-primary hover:underline"
                      onClick={() => setShowForgotPassword(true)}
                    >
                      {dict.auth.forgot_password}
                    </button>
                  </div>
                )}
              </div>

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading
                  ? isLogin
                    ? dict.auth.signing_in
                    : dict.auth.creating_account
                  : isLogin
                    ? dict.common.signIn
                    : dict.common.signUp}
              </Button>
            </form>
          )}

          {!isVerifyingEmail && !showForgotPassword && (
            <>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-card px-2 text-muted-foreground">
                    {dict.auth.or_continue_with}
                  </span>
                </div>
              </div>

              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={handleGoogleSignIn}
              >
                <Chrome className="w-4 h-4 mr-2" />
                {dict.auth.sign_in_google}
              </Button>

              <div className="text-center">
                <button
                  type="button"
                  className="text-sm text-primary hover:underline"
                  onClick={() => setIsLogin(!isLogin)}
                >
                  {isLogin
                    ? dict.auth.no_account_sign_up
                    : dict.auth.have_account_sign_in}
                </button>
              </div>
            </>
          )}
        </div>
      </Card>
    </div>
  );
};

export default Auth;
