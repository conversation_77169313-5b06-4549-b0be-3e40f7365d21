"use client";

import { SessionProvider } from "next-auth/react";
import { QueryClientProvider } from "@tanstack/react-query";
import { ReactNode, useState } from "react";
import { createQueryClient } from "@/feature/core/api/api-utils";
import { ProgressProvider as ProgressProviderApp } from "@bprogress/next/app";

interface AuthSessionProviderProps {
  children: ReactNode;
}

export function AuthSessionProvider({ children }: AuthSessionProviderProps) {
  const [queryClient] = useState(() => createQueryClient());

  return (
    <ProgressProviderApp
      height="4px"
      color="#cb635e"
      options={{ showSpinner: false }}
      shallowRouting
    >
      <QueryClientProvider client={queryClient}>
        <SessionProvider>{children}</SessionProvider>
      </QueryClientProvider>
    </ProgressProviderApp>
  );
}
