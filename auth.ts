import NextAuth from "next-auth";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { prisma } from "@/lib/prisma";
import { generateUserSlug, ensureUniqueUserSlug } from "@/lib/slug-utils";
import authConfig from "./auth.config";

// Custom adapter that extends PrismaAdapter to handle slug generation
const customAdapter = {
  ...PrismaAdapter(prisma),
  async createUser(user: any) {
    // Generate unique slug for the user
    const baseSlug = generateUserSlug(
      user.name || user.email?.split("@")[0] || "user"
    );
    const existingUsers = await prisma.user.findMany({
      select: { slug: true },
    });
    const existingSlugs = existingUsers.map((u) => u.slug);
    const uniqueSlug = ensureUniqueUserSlug(baseSlug, existingSlugs);

    // Create user with slug
    const newUser = await prisma.user.create({
      data: {
        ...user,
        slug: uniqueSlug,
        about: "",
      },
    });

    return newUser;
  },
};

export const { auth, handlers, signIn, signOut } = NextAuth({
  adapter: customAdapter,
  session: { strategy: "jwt" },
  ...authConfig,
  callbacks: {
    ...authConfig.callbacks,
    async signIn({ user, account, profile, email, credentials }) {
      const customResult = await authConfig.callbacks?.signIn?.({
        user,
        account,
        profile,
        email,
        credentials,
      });
      if (customResult === false) return false;

      return true;
    },
  },
  logger: {
    error: (error: Error) => {
      // Suppress CredentialsSignin errors as they are expected when login fails
      if (error.name === "CredentialsSignin") {
        return;
      }
      // Log other errors normally
      console.error(`[auth][error]`, error);
    },
    warn: (code: string) => {
      console.warn(`[auth][warn] ${code}`);
    },
    debug: (code: string, metadata?: any) => {
      // Suppress debug logs in production
      if (process.env.NODE_ENV === "development") {
        console.log(`[auth][debug] ${code}`, metadata);
      }
    },
  },
});
