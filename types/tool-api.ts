// SuperCategory enum
export enum SuperCategory {
  WRITING = "WRITING",
  IMAGE_GENERATION = "IMAGE_GENERATION",
  AUDIO = "AUDIO",
  VIDEO_GENERATION = "VIDEO_GENERATION",
  SOCIAL_MEDIA = "SOCIAL_MEDIA",
}

// Tool enums
export enum ToolType {
  SAAS = "SAAS",
  MOBILE_APP = "MOBILE_APP",
  DESKTOP_APP = "DESKTOP_APP",
  AI_MODEL = "AI_MODEL",
  CHROME_EXTENSION = "CHROME_EXTENSION",
}

export enum PricingModel {
  FREE = "FREE",
  FREEMIUM = "FREEMIUM",
  PAID = "PAID",
  SUBSCRIPTION = "SUBSCRIPTION",
  ONE_TIME = "ONE_TIME",
  USAGE_BASED = "USAGE_BASED",
  CUSTOM = "CUSTOM",
}

export enum ToolStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
  PUBLISHED = "PUBLISHED",
  DRAFT = "DRAFT",
  ARCHIVED = "ARCHIVED",
}

// Tool Translation Types
export interface ToolTranslationResponse {
  id: string;
  tool_id: string;
  language_code: string;
  name: string | null;
  short_description: string | null;
  introduction: string | null;
  what_is_it: string | null;
  how_to_use: string | null;
  full_feature: string | null;
  short_feature: string | null;
  pricing: string | null;
  meta_title: string | null;
  meta_description: string | null;
}

// Tool Types
export interface CreateToolRequest {
  name: string;
  website_url?: string;
  logo_url?: string;
  screenshot_urls?: string[];
  tool_type?: ToolType;
  pricing_models?: PricingModel[];
  is_available?: boolean;
  status?: ToolStatus;
  is_published?: boolean;
  category_ids?: number[];
  tag_ids?: number[];
  twitter_url?: string;
  facebook_url?: string;
  linkedin_url?: string;
  github_url?: string;
  contact_email?: string;
  support_email?: string;
  // Translation fields for the primary language
  short_description?: string;
  introduction?: string;
  what_is_it?: string;
  how_to_use?: string;
  full_feature?: string;
  short_feature?: string;
  pricing?: string;
  meta_title?: string;
  meta_description?: string;
  // FAQ fields
  faqs?: Array<{
    question: string;
    answer: string;
    order?: number;
  }>;
}

export interface UpdateToolRequest {
  name?: string;
  website_url?: string;
  logo_url?: string;
  screenshot_urls?: string[];
  tool_type?: ToolType;
  pricing_models?: PricingModel[];
  is_available?: boolean;
  status?: ToolStatus;
  is_published?: boolean;
  category_ids?: number[];
  tag_ids?: number[];
  twitter_url?: string;
  facebook_url?: string;
  linkedin_url?: string;
  github_url?: string;
  contact_email?: string;
  support_email?: string;
  // Translation fields for the primary language
  short_description?: string;
  introduction?: string;
  what_is_it?: string;
  how_to_use?: string;
  full_feature?: string;
  short_feature?: string;
  pricing?: string;
  meta_title?: string;
  meta_description?: string;
  // FAQ fields
  faqs?: Array<{
    id?: string;
    question: string;
    answer: string;
    order?: number;
    is_active?: boolean;
  }>;
}

export interface ToolResponse {
  id: string;
  name: string;
  slug: string;
  website_url: string | null;
  logo_url: string | null;
  screenshot_urls: string[];
  tool_type: ToolType;
  pricing_models: PricingModel[];
  is_available: boolean;
  added_date: Date;
  status: ToolStatus;
  is_published: boolean;
  category_ids: number[];
  tag_ids: number[];
  twitter_url: string | null;
  facebook_url: string | null;
  linkedin_url: string | null;
  github_url: string | null;
  contact_email: string | null;
  support_email: string | null;
  translations: ToolTranslationResponse[];
  faqs?: ToolFAQResponse[];
  categories?: CategoryResponse[];
  tags?: TagResponse[];
  reviews?: {
    items: ReviewResponse[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      total_pages: number;
      has_next: boolean;
      has_prev: boolean;
    };
    quick_review?: {
      average_rating: number;
      total_reviews: number;
      rating_distribution: Record<number, number>;
    };
  };
  saved_count: number;
  is_saved?: boolean;
}

// Tool Category Types
export interface CreateCategoryRequest {
  name: string;
  description?: string;
  slug?: string;
  super_category: SuperCategory;
  icon_url?: string;
  color?: string;
  is_active?: boolean;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  slug?: string;
  super_category?: SuperCategory;
  icon_url?: string;
  color?: string;
  is_active?: boolean;
}

export interface CategoryResponse {
  id: number;
  name: string;
  slug: string;
  super_category: SuperCategory;
  icon_url: string | null;
  color: string | null;
  is_active: boolean;
  translations: CategoryTranslationResponse[];
}

export interface CategoryTranslationResponse {
  id: number;
  category_id: number;
  language_code: string;
  name: string;
  description: string | null;
}

// Tool Tag Types
export interface CreateTagRequest {
  name: string;
  description?: string;
  slug?: string;
  color?: string;
  is_active?: boolean;
}

export interface UpdateTagRequest {
  name?: string;
  description?: string;
  slug?: string;
  color?: string;
  is_active?: boolean;
}

export interface TagResponse {
  id: number;
  name: string;
  slug: string;
  color: string | null;
  is_active: boolean;
  translations: TagTranslationResponse[];
}

export interface TagTranslationResponse {
  id: number;
  tag_id: number;
  language_code: string;
  name: string;
  description: string | null;
}

// Common Query Types
export interface ListQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  is_active?: boolean;
  language?: string;
}

export interface ToolQueryParams extends ListQueryParams {
  tool_type?: ToolType;
  pricing_model?: PricingModel;
  status?: ToolStatus;
  is_published?: boolean;
  category_id?: number;
  tag_id?: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// Tool Config Types
export interface ToolConfigCategory {
  id: number;
  name: string;
  slug: string;
  super_category: SuperCategory;
  icon_url: string | null;
  color: string | null;
  description: string | null;
}

export interface ToolConfigTag {
  id: number;
  name: string;
  slug: string;
  color: string | null;
  description: string | null;
}

export interface ToolConfigResponse {
  categories: Record<SuperCategory, ToolConfigCategory[]>;
  tags: ToolConfigTag[];
  enums: {
    toolTypes: ToolType[];
    pricingModels: PricingModel[];
    toolStatuses: ToolStatus[];
    superCategories: SuperCategory[];
  };
  stats: {
    totalCategories: number;
    totalTags: number;
    categoriesBySuper: Record<string, number>;
  };
}

// Tool FAQ Types
export interface ToolFAQResponse {
  id: string;
  tool_id: string;
  order: number;
  is_active: boolean;
  translations: ToolFAQTranslationResponse[];
}

export interface ToolFAQTranslationResponse {
  id: string;
  faq_id: string;
  language_code: string;
  question: string;
  answer: string;
}

export interface CreateToolFAQRequest {
  tool_id: string;
  question: string;
  answer: string;
  order?: number;
  is_active?: boolean;
}

export interface UpdateToolFAQRequest {
  question?: string;
  answer?: string;
  order?: number;
  is_active?: boolean;
}

export interface ToolFAQQueryParams {
  tool_id: string;
  language?: string;
  is_active?: boolean;
}

// Tool Review Types
export interface CreateReviewRequest {
  tool_id: string;
  rating: number;
  content?: string;
}

export interface UpdateReviewRequest {
  rating?: number;
  content?: string;
}

export interface AdminReviewApprovalRequest {
  is_published: boolean;
}

// Basic tool info for reviews
export interface ReviewToolInfo {
  id: string;
  name: string;
  slug: string;
  logo_url: string | null;
}

export interface ReviewUserInfo {
  id: string;
  name: string | null;
  email: string | null;
  image: string | null;
}

export interface ReviewResponse {
  id: string;
  tool_id: string;
  user_id: string;
  rating: number;
  content: string | null;
  is_published: boolean;
  helpful_votes: number;
  unhelpful_votes: number;
  created_at: Date;
  updated_at: Date;
  user?: ReviewUserInfo;
  tool?: ReviewToolInfo;
  user_vote?: VoteType;
}

export interface ReviewQueryParams extends ListQueryParams {
  tool_id?: string;
  user_id?: string;
  rating?: number;
  is_published?: boolean;
}

export interface PublicReviewQueryParams {
  tool_id?: string;
  rating?: number;
  page?: number;
  limit?: number;
}

export interface ReviewStatsResponse {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: Record<number, number>;
  publishedReviews: number;
  pendingReviews: number;
}

// Review Vote Types
export enum VoteType {
  HELPFUL = "HELPFUL",
  UNHELPFUL = "UNHELPFUL",
}

export interface CreateReviewVoteRequest {
  review_id: string;
  vote_type: VoteType;
}

export interface ReviewVoteResponse {
  id: string;
  review_id: string;
  user_id: string;
  vote_type: VoteType;
  created_at: Date;
  updated_at: Date;
}

export interface ReviewWithUserVote extends ReviewResponse {
  user_vote?: VoteType;
}

export interface ReviewVoteStatsResponse {
  review_id: string;
  helpful_votes: number;
  unhelpful_votes: number;
  user_vote?: VoteType;
}
