// Blog enums based on Prisma schema
export enum BlogStatus {
  DRAFT = "DRAFT",
  PUBLISHED = "PUBLISHED",
  SCHEDULED = "SCHEDULED",
  ARCHIVED = "ARCHIVED",
  PRIVATE = "PRIVATE",
}

export enum BlogCommentStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  SPAM = "SPAM",
  TRASH = "TRASH",
}

// Author Types
export interface AuthorResponse {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
  role: "ADMIN" | "AUTHOR" | "USER";
  created_at?: Date;
  updated_at?: Date;
}

export interface AuthorPostResponse {
  id: string;
  title: string;
  slug: string;
  excerpt: string | null;
  featured_image_url: string | null;
  published_at: Date | null;
  created_at: Date;
  categories: BlogCategoryResponse[];
}

export interface AuthorPageResponse {
  author: AuthorResponse;
  posts: AuthorPostResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// Blog Category Types
export interface CreateBlogCategoryRequest {
  name: string;
  description?: string;
  slug?: string;
  parent_id?: number;
}

export interface UpdateBlogCategoryRequest {
  name?: string;
  description?: string;
  slug?: string;
  parent_id?: number;
}

export interface BlogCategoryResponse {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  parent_id: number | null;
  created_at: Date;
  updated_at: Date;
  parent?: BlogCategoryResponse | null;
  children?: BlogCategoryResponse[];
}

// Blog Tag Types
export interface CreateBlogTagRequest {
  name: string;
  description?: string;
  slug?: string;
}

export interface UpdateBlogTagRequest {
  name?: string;
  description?: string;
  slug?: string;
}

export interface BlogTagResponse {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  created_at: Date;
  updated_at: Date;
}

// Blog Types
export interface CreateBlogPostRequest {
  title: string;
  excerpt?: string;
  content: string;
  status?: BlogStatus;
  meta_title?: string;
  meta_description?: string;
  featured_image_url?: string;
  category_ids?: number[];
  tag_ids?: number[];
  published_at?: Date;
  author_id?: string;
}

export interface UpdateBlogPostRequest {
  title?: string;
  excerpt?: string;
  content?: string;
  status?: BlogStatus;
  meta_title?: string;
  meta_description?: string;
  featured_image_url?: string;
  category_ids?: number[];
  tag_ids?: number[];
  published_at?: Date;
  author_id?: string;
}

export interface BlogPostResponse {
  id: string;
  title: string;
  slug: string;
  excerpt: string | null;
  content: string;
  status: BlogStatus;
  meta_title: string | null;
  meta_description: string | null;
  featured_image_url: string | null;
  author_id: string;
  category_ids: number[];
  tag_ids: number[];
  published_at: Date | null;
  created_at: Date;
  updated_at: Date;
  author?: {
    id: string;
    name: string | null;
    email: string;
    image: string | null;
  };
  categories?: BlogCategoryResponse[];
  tags?: BlogTagResponse[];
  related_posts?: RelatedPost[];
  recent_posts?: RelatedPost[];
}

export interface RelatedPost {
  id: string;
  title: string;
  slug: string;
  featured_image_url: string | null;
}

// Blog Listing Types (Public API)
export interface BlogListingPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string | null;
  featured_image_url: string | null;
  published_at: Date | null;
  author: {
    id: string;
    name: string | null;
    image: string | null;
  };
  parent_category: {
    id: number;
    name: string;
    slug: string;
  } | null;
}

export interface BlogListingResponse {
  posts: BlogListingPost[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// Blog Comment Types
export interface CreateBlogCommentRequest {
  post_id: string;
  author_name: string;
  author_email: string;
  author_url?: string;
  content: string;
  parent_id?: string;
}

export interface UpdateBlogCommentRequest {
  author_name?: string;
  author_email?: string;
  author_url?: string;
  content?: string;
  status?: BlogCommentStatus;
  is_approved?: boolean;
}

export interface BlogCommentResponse {
  id: string;
  post_id: string;
  author_id: string | null;
  author_name: string;
  author_email: string;
  author_url: string | null;
  content: string;
  status: BlogCommentStatus;
  parent_id: string | null;
  is_approved: boolean;
  created_at: Date;
  updated_at: Date;
  author?: {
    id: string;
    name: string | null;
    email: string;
    image: string | null;
  };
  replies?: BlogCommentResponse[];
}

// Query Parameters
export interface ListQueryParams {
  page?: number;
  limit?: number;
  search?: string;
}

export interface BlogCategoryQueryParams extends ListQueryParams {
  parent_id?: number;
}

export interface BlogPostQueryParams extends ListQueryParams {
  status?: BlogStatus;
  author_id?: string;
  category_id?: number;
  tag_id?: number;
  published?: boolean;
}

export interface BlogCommentQueryParams extends ListQueryParams {
  post_id?: string;
  status?: BlogCommentStatus;
  is_approved?: boolean;
}

// Paginated Response
export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// Blog Config Response
export interface BlogConfigResponse {
  categories: BlogCategoryResponse[];
  tags: BlogTagResponse[];
  authors: AuthorResponse[];
  enums: {
    blogStatuses: BlogStatus[];
    commentStatuses: BlogCommentStatus[];
  };
  stats: {
    totalCategories: number;
    totalTags: number;
    totalPosts: number;
    totalComments: number;
  };
}
