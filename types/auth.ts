import { DefaultSession } from "next-auth";
import { JWT } from "next-auth/jwt";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      role: "USER" | "ADMIN" | "AUTHOR";
      emailVerified?: Date | null;
      isVerified?: boolean;
      slug?: string;
    } & DefaultSession["user"];
  }

  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role: "USER" | "ADMIN" | "AUTHOR";
    emailVerified?: Date | null;
    isVerified?: boolean;
    slug?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: "USER" | "ADMIN" | "AUTHOR";
    emailVerified?: Date | null;
    isVerified?: boolean;
    slug?: string;
  }
}
