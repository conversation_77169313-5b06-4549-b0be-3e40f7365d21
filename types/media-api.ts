// Media file types
export type MediaFileType =
  | "image"
  | "video"
  | "audio"
  | "document"
  | "archive"
  | "other";

// Database media metadata
export interface MediaMetadata {
  id: string;
  file_key: string;
  file_name: string;
  original_name: string;
  file_url: string;
  file_size: number;
  mime_type: string;
  file_type: MediaFileType;
  title?: string;
  alt_text?: string;
  caption?: string;
  description?: string;
  folder_path: string;
  uploaded_by?: string;
  created_at: string;
  updated_at: string;
}

// Media item structure (for file system browsing)
export interface MediaItem {
  key: string;
  name: string;
  type: "file" | "folder";
  file_type?: MediaFileType;
  size?: number;
  last_modified?: string;
  url?: string;
  parent_path?: string;
  mime_type?: string;
  extension?: string;
  // Database metadata (when available)
  metadata?: MediaMetadata;
}

// API Request/Response types
export interface ListMediaRequest {
  path?: string;
  limit?: number;
  continuation_token?: string;
}

export interface ListMediaResponse {
  success: boolean;
  message: string;
  data: {
    items: MediaItem[];
    folders: MediaItem[];
    files: MediaItem[];
    total_count: number;
    is_truncated: boolean;
    next_continuation_token?: string;
    current_path: string;
  };
}

export interface UploadMediaRequest {
  file: File;
  path?: string;
  folder_path?: string;
}

export interface UploadMediaResponse {
  success: boolean;
  message: string;
  data: {
    key: string;
    url: string;
    file_name: string;
    file_size: number;
    mime_type: string;
  };
}

export interface CreateFolderRequest {
  folder_name: string;
  parent_path?: string;
}

export interface CreateFolderResponse {
  success: boolean;
  message: string;
  data: {
    folder_path: string;
    folder_name: string;
  };
}

export interface DeleteMediaRequest {
  keys: string[];
}

export interface DeleteMediaResponse {
  success: boolean;
  message: string;
  data: {
    deleted_count: number;
    failed_keys?: string[];
  };
}

export interface RenameMediaRequest {
  old_key: string;
  new_key: string;
}

export interface RenameMediaResponse {
  success: boolean;
  message: string;
  data: {
    old_key: string;
    new_key: string;
    new_url: string;
  };
}

export interface MoveMediaRequest {
  keys: string[];
  destination_path: string;
}

export interface MoveMediaResponse {
  success: boolean;
  message: string;
  data: {
    moved_count: number;
    failed_keys?: string[];
  };
}

export interface GetMediaDetailsRequest {
  key: string;
}

export interface GetMediaDetailsResponse {
  success: boolean;
  message: string;
  data: MediaItem & {
    public_url: string;
    download_url: string;
    metadata?: MediaMetadata;
  };
}

export interface UpdateMediaMetadataRequest {
  file_key: string;
  title?: string;
  alt_text?: string;
  caption?: string;
  description?: string;
}

export interface UpdateMediaMetadataResponse {
  success: boolean;
  message: string;
  data: MediaMetadata;
}

export interface CreateMediaMetadataRequest {
  file_key: string;
  file_name: string;
  original_name: string;
  file_url: string;
  file_size: number;
  mime_type: string;
  file_type: MediaFileType;
  folder_path: string;
  title?: string;
  alt_text?: string;
  caption?: string;
  description?: string;
}

export interface CreateMediaMetadataResponse {
  success: boolean;
  message: string;
  data: MediaMetadata;
}

// Utility types
export interface MediaUploadProgress {
  file_name: string;
  progress: number;
  status: "pending" | "uploading" | "completed" | "error";
  error?: string;
}

export interface MediaFilter {
  type?: "all" | "images" | "videos" | "documents" | "audio" | "other";
  search?: string;
  sort_by?: "name" | "size" | "date";
  sort_order?: "asc" | "desc";
}

// Media permissions
export interface MediaPermissions {
  can_upload: boolean;
  can_delete: boolean;
  can_create_folders: boolean;
  can_rename: boolean;
  can_move: boolean;
  max_file_size?: number;
  allowed_file_types?: string[];
}
