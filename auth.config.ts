import type { NextAuthConfig } from "next-auth";
import Google from "next-auth/providers/google";
import Credentials from "next-auth/providers/credentials";
import { compare } from "bcryptjs";
import { prisma } from "@/lib/prisma";
import { generateUserSlug, ensureUniqueUserSlug } from "@/lib/slug-utils";
import "./types/auth";

export default {
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    Credentials({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email as string,
          },
        });

        if (!user || !user.password) {
          return null;
        }

        const isValid = await compare(
          credentials.password as string,
          user.password
        );

        if (!isValid) {
          return null;
        }

        // Check if email is verified for credentials login
        if (!user.isVerified) {
          return null;
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.image,
          role: user.role,
          emailVerified: user.emailVerified || null,
          isVerified: user.isVerified,
          slug: user.slug,
        };
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === "google") {
        try {
          // Check if user exists
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email! },
            include: {
              accounts: true,
            },
          });

          if (existingUser) {
            const hasGoogleAccount = existingUser.accounts.some(
              (acc) => acc.provider === "google"
            );

            if (!hasGoogleAccount) {
              // Link the Google account to the existing user
              await prisma.account.create({
                data: {
                  userId: existingUser.id,
                  type: "oauth",
                  provider: "google",
                  providerAccountId: account.providerAccountId,
                  access_token: account.access_token,
                  expires_at: account.expires_at,
                  token_type: account.token_type,
                  scope: account.scope,
                  id_token: account.id_token,
                  refresh_token: account.refresh_token,
                },
              });

              // Update user verification status and profile info
              await prisma.user.update({
                where: { email: user.email! },
                data: {
                  isVerified: true,
                  emailVerified: new Date(),
                  image: user.image || existingUser.image,
                  name: user.name || existingUser.name,
                },
              });

              // Update the user object to match the existing user's ID
              user.id = existingUser.id;
            }
          }

          return true;
        } catch (error) {
          console.error("Google sign-in error:", error);
          return false;
        }
      }

      if (account?.provider === "credentials") {
        return true;
      }

      return true;
    },
    async jwt({ token, user, account, trigger }) {
      if (user) {
        token.role = user.role;
        token.emailVerified = user.emailVerified || null;
        token.isVerified = user.isVerified || false;
        token.slug = user.slug;
      }

      // Handle Google OAuth verification - update user and token
      if (account?.provider === "google" && token.sub) {
        try {
          // Update user verification status for Google OAuth
          const updatedUser = await prisma.user.update({
            where: { id: token.sub },
            data: {
              emailVerified: new Date(),
              isVerified: true,
            },
            select: {
              emailVerified: true,
              isVerified: true,
              role: true,
              name: true,
              email: true,
              image: true,
              slug: true,
            },
          });

          // Update token with fresh data
          token.emailVerified = updatedUser.emailVerified;
          token.isVerified = updatedUser.isVerified;
          token.role = updatedUser.role;
          token.name = updatedUser.name;
          token.email = updatedUser.email;
          token.picture = updatedUser.image;
          token.slug = updatedUser.slug;
        } catch (error) {
          console.error("JWT Google update error:", error);
        }
      } else if (token.sub && (!token.isVerified || trigger === "update")) {
        try {
          const freshUser = await prisma.user.findUnique({
            where: { id: token.sub },
            select: {
              emailVerified: true,
              isVerified: true,
              role: true,
              name: true,
              email: true,
              image: true,
              slug: true,
            },
          });
          if (freshUser) {
            token.emailVerified = freshUser.emailVerified;
            token.isVerified = freshUser.isVerified;
            token.role = freshUser.role;
            token.name = freshUser.name;
            token.email = freshUser.email;
            token.picture = freshUser.image;
            token.slug = freshUser.slug;
          }
        } catch (error) {}
      }

      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role;
        session.user.emailVerified = token.emailVerified || null;
        session.user.isVerified = token.isVerified || false;
        session.user.slug = token.slug;
      }
      return session;
    },
  },
  pages: {
    signIn: "/login",
  },
  events: {
    async linkAccount({ user, account }) {},
  },
} satisfies NextAuthConfig;
