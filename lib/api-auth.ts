import { auth } from "@/auth";
import { NextRequest } from "next/server";

export interface AuthResult {
  user: {
    id: string;
    email: string;
    name?: string | null;
    role: "USER" | "ADMIN" | "AUTHOR";
    isVerified: boolean;
  };
  isAdmin: boolean;
  isAuthor: boolean;
}

export async function authenticateApiRequest(): Promise<AuthResult | null> {
  try {
    const session = await auth();

    if (!session?.user) {
      return null;
    }

    return {
      user: {
        id: session.user.id,
        email: session.user.email!,
        name: session.user.name,
        role: session.user.role,
        isVerified: session.user.isVerified || false,
      },
      isAdmin: session.user.role === "ADMIN",
      isAuthor: session.user.role === "AUTHOR",
    };
  } catch (error) {
    return null;
  }
}

export async function requireAdminAuth(): Promise<AuthResult> {
  const authResult = await authenticateApiRequest();

  if (!authResult) {
    throw new Error("Authentication required");
  }

  if (!authResult.isAdmin) {
    throw new Error("Admin access required");
  }

  return authResult;
}

// New function to require either ADMIN or AUTHOR role
export async function requireAdminOrAuthorAuth(): Promise<AuthResult> {
  const authResult = await authenticateApiRequest();

  if (!authResult) {
    throw new Error("Authentication required");
  }

  if (!authResult.isAdmin && !authResult.isAuthor) {
    throw new Error("Admin or Author access required");
  }

  return authResult;
}
