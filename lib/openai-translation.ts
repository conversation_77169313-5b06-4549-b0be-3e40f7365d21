import OpenAI from "openai";
import { prisma } from "@/lib/prisma";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface TranslationRequest {
  text: string;
  from: string; // source language code (e.g., 'en')
  to: string; // target language code (e.g., 'es')
  context?: string; // additional context for better translation
}

export interface TranslationResult {
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
}

export async function translateText(
  request: TranslationRequest
): Promise<TranslationResult> {
  const { text, from, to, context } = request;

  try {
    if (from === to) {
      return {
        translatedText: text,
        sourceLanguage: from,
        targetLanguage: to,
      };
    }

    const languageNames: Record<string, string> = {
      en: "English",
      es: "Spanish",
    };

    const prompt = `Translate the following text from ${languageNames[from] || from} to ${languageNames[to] || to}.

${context ? `Context: ${context}` : ""}

Text to translate: "${text}"

Please provide only the translated text without any additional explanation or formatting.`;

    const completion = await openai.chat.completions.create({
      model: "gpt-4.1-mini",
      messages: [
        {
          role: "system",
          content:
            "You are a professional translator. Provide accurate, natural translations that preserve the original meaning and tone. Return only the translated text without any additional explanation or formatting.",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.3,
      max_tokens: 1000,
    });

    const translatedText =
      completion.choices[0]?.message?.content?.trim() || text;

    return {
      translatedText,
      sourceLanguage: from,
      targetLanguage: to,
    };
  } catch (error) {
    console.error("Translation error:", error);
    // Fallback to original text if translation fails
    return {
      translatedText: text,
      sourceLanguage: from,
      targetLanguage: to,
    };
  }
}

export async function translateToAllLanguages(
  text: string,
  context?: string
): Promise<Record<string, string>> {
  const supportedLanguages = ["en", "es"];
  const translations: Record<string, string> = {};

  // Always include English as the source
  translations["en"] = text;

  // Translate to other languages
  const translationPromises = supportedLanguages
    .filter((lang) => lang !== "en")
    .map(async (targetLang) => {
      const result = await translateText({
        text,
        from: "en",
        to: targetLang,
        context,
      });
      return { language: targetLang, translation: result.translatedText };
    });

  const results = await Promise.all(translationPromises);

  results.forEach(({ language, translation }) => {
    translations[language] = translation;
  });

  return translations;
}

export interface TranslationPair {
  name: Record<string, string>;
  description?: Record<string, string>;
}

export async function translateNameAndDescription(
  name: string,
  description?: string,
  context?: string
): Promise<TranslationPair> {
  const nameTranslations = await translateToAllLanguages(name, context);

  let descriptionTranslations: Record<string, string> | undefined;
  if (description && description.trim().length > 0) {
    descriptionTranslations = await translateToAllLanguages(
      description,
      context
    );
  }

  return {
    name: nameTranslations,
    description: descriptionTranslations,
  };
}

export interface FAQTranslationPair {
  question: Record<string, string>;
  answer: Record<string, string>;
}

export async function translateQuestionAndAnswer(
  question: string,
  answer: string,
  context?: string
): Promise<FAQTranslationPair> {
  const questionTranslations = await translateToAllLanguages(
    question,
    context ? `${context} - FAQ Question` : "FAQ Question"
  );

  const answerTranslations = await translateToAllLanguages(
    answer,
    context ? `${context} - FAQ Answer` : "FAQ Answer"
  );

  return {
    question: questionTranslations,
    answer: answerTranslations,
  };
}

// Tool-specific translation interfaces and functions
export interface ToolTranslationData {
  name?: string | null;
  short_description?: string | null;
  introduction?: string | null;
  what_is_it?: string | null;
  how_to_use?: string | null;
  full_feature?: string | null;
  short_feature?: string | null;
  pricing?: string | null;
  meta_title?: string | null;
  meta_description?: string | null;
}

export async function translateAndCreateToolTranslation(
  toolId: string,
  englishData: ToolTranslationData
): Promise<void> {
  try {
    const supportedLanguages = ["es"]; // Add more languages as needed

    for (const targetLang of supportedLanguages) {
      const translatedData: Record<string, string | null> = {};

      // Translation context for better accuracy
      const context = `This is for a technology tool/software listing. The tool name is "${englishData.name}".`;

      // Translate each field that has content
      for (const [field, value] of Object.entries(englishData)) {
        if (value && typeof value === "string" && value.trim()) {
          try {
            const result = await translateText({
              text: value,
              from: "en",
              to: targetLang,
              context,
            });
            translatedData[field] = result.translatedText;
          } catch (error) {
            console.error(`Failed to translate ${field}:`, error);
            translatedData[field] = value; // Fallback to original
          }
        } else {
          translatedData[field] = value;
        }
      }

      // Create or update the translation
      await prisma.toolTranslation.upsert({
        where: {
          tool_id_language_code: {
            tool_id: toolId,
            language_code: targetLang,
          },
        },
        update: translatedData,
        create: {
          tool_id: toolId,
          language_code: targetLang,
          name: translatedData.name || englishData.name || "",
          ...translatedData,
        },
      });
    }
  } catch (error) {
    console.error("Failed to create tool translations:", error);
    throw error;
  }
}

// Translate tool to specific language
export async function translateToolToLanguage(
  toolId: string,
  englishData: ToolTranslationData,
  targetLanguage: string
): Promise<void> {
  try {
    const translatedData: Record<string, string | null> = {};

    // Translation context for better accuracy
    const context = `This is for a technology tool/software listing. The tool name is "${englishData.name}".`;

    // Translate each field that has content
    for (const [field, value] of Object.entries(englishData)) {
      if (value && typeof value === "string" && value.trim()) {
        try {
          const result = await translateText({
            text: value,
            from: "en",
            to: targetLanguage,
            context,
          });
          translatedData[field] = result.translatedText;
        } catch (error) {
          console.error(`Failed to translate ${field}:`, error);
          translatedData[field] = value; // Fallback to original
        }
      } else {
        translatedData[field] = value;
      }
    }

    // Create or update the translation
    await prisma.toolTranslation.upsert({
      where: {
        tool_id_language_code: {
          tool_id: toolId,
          language_code: targetLanguage,
        },
      },
      update: translatedData,
      create: {
        tool_id: toolId,
        language_code: targetLanguage,
        name: translatedData.name || englishData.name || "",
        ...translatedData,
      },
    });
  } catch (error) {
    console.error("Failed to create tool translation:", error);
    throw error;
  }
}

// FAQ-specific translation interfaces and functions
export interface FAQTranslationData {
  question: string;
  answer: string;
}

export async function createFAQTranslations(
  faqId: string,
  englishData: FAQTranslationData
): Promise<void> {
  try {
    const supportedLanguages = ["es"]; // Add more languages as needed

    for (const targetLang of supportedLanguages) {
      // Translation context for better accuracy
      const questionContext =
        "This is a FAQ question for a technology tool/software.";
      const answerContext =
        "This is a FAQ answer for a technology tool/software.";

      try {
        // Translate question
        const questionResult = await translateText({
          text: englishData.question,
          from: "en",
          to: targetLang,
          context: questionContext,
        });

        // Translate answer
        const answerResult = await translateText({
          text: englishData.answer,
          from: "en",
          to: targetLang,
          context: answerContext,
        });

        // Create or update the FAQ translation
        await prisma.toolFAQTranslation.upsert({
          where: {
            faq_id_language_code: {
              faq_id: faqId,
              language_code: targetLang,
            },
          },
          update: {
            question: questionResult.translatedText,
            answer: answerResult.translatedText,
          },
          create: {
            faq_id: faqId,
            language_code: targetLang,
            question: questionResult.translatedText,
            answer: answerResult.translatedText,
          },
        });
      } catch (error) {
        console.error(`Failed to translate FAQ to ${targetLang}:`, error);
        // Create fallback translation with original text
        await prisma.toolFAQTranslation.upsert({
          where: {
            faq_id_language_code: {
              faq_id: faqId,
              language_code: targetLang,
            },
          },
          update: {
            question: englishData.question,
            answer: englishData.answer,
          },
          create: {
            faq_id: faqId,
            language_code: targetLang,
            question: englishData.question,
            answer: englishData.answer,
          },
        });
      }
    }
  } catch (error) {
    console.error("Failed to create FAQ translations:", error);
    throw error;
  }
}
