import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getLocalizedUrl(path: string, lang?: string): string {
  if (!lang || lang === "en") {
    return path;
  }
  return `/${lang}${path}`;
}

export function formatDate(date: Date, lang: string = "en"): string {
  return new Intl.DateTimeFormat(lang, {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(date);
}
