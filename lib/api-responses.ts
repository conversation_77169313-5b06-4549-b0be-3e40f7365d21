import { NextResponse } from "next/server";

export interface ApiSuccessResponse<T = any> {
  success: true;
  message: string;
  data: T;
}

export interface ApiErrorResponse {
  success: false;
  error: string;
  details?: any;
}

export function successResponse<T>(
  data: T,
  message: string = "Success",
  status: number = 200
): NextResponse<ApiSuccessResponse<T>> {
  return NextResponse.json(
    {
      success: true,
      message,
      data,
    },
    { status }
  );
}

export function errorResponse(
  error: string,
  status: number = 400,
  details?: any
): NextResponse<ApiErrorResponse> {
  return NextResponse.json(
    {
      success: false,
      error,
      ...(details && { details }),
    },
    { status }
  );
}

export function unauthorizedResponse(
  message: string = "Authentication required"
): NextResponse<ApiErrorResponse> {
  return errorResponse(message, 401);
}

export function forbiddenResponse(
  message: string = "Access forbidden"
): NextResponse<ApiErrorResponse> {
  return errorResponse(message, 403);
}

export function notFoundResponse(
  resource: string = "Resource"
): NextResponse<ApiErrorResponse> {
  return errorResponse(`${resource} not found`, 404);
}

export function validationErrorResponse(
  message: string,
  details?: any
): NextResponse<ApiErrorResponse> {
  return errorResponse(message, 422, details);
}

export function serverErrorResponse(
  message: string = "Internal server error"
): NextResponse<ApiErrorResponse> {
  return errorResponse(message, 500);
}
