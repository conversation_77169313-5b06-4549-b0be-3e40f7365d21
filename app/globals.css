@import "tailwindcss";
@import "tw-animate-css";
@import "../feature/rich-text/styles/rich-text-style.css";
@import "../feature/blog/post/styles/blog-rich-editor.css";


@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.585 0.15 25.5);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.865 0.06 25.5);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.585 0.15 25.5);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.865 0.06 25.5);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}




.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 20px;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Smooth collapsible animations */
[data-radix-collapsible-content] {
  overflow: hidden;
}

[data-radix-collapsible-content][data-state="open"] {
  animation: collapsible-down 300ms ease-in-out;
}

[data-radix-collapsible-content][data-state="closed"] {
  animation: collapsible-up 300ms ease-in-out;
}

@keyframes collapsible-down {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
    opacity: 1;
  }
}

@keyframes collapsible-up {
  from {
    height: var(--radix-collapsible-content-height);
    opacity: 1;
  }
  to {
    height: 0;
    opacity: 0;
  }
}



/* Prose styles for rich content */
.prose {
  @apply text-gray-900 leading-7;
  font-size: 1.125rem;
  line-height: 1.75;
  max-width: none;
}

.prose-lg {
  font-size: 18px;
  line-height: 1.8;
}

.prose p {
  @apply mb-4;
}

.prose h1 {
  @apply text-3xl font-bold text-gray-900 mb-6 mt-8;
}

.prose h2 {
  @apply text-3xl font-semibold text-gray-900 mb-4 mt-6;
}

.prose h3 {
  @apply text-xl font-semibold text-gray-900 mb-3 mt-5;
}

.prose h4 {
  @apply text-lg font-semibold text-gray-900 mb-3 mt-4;
}

.prose h5 {
  @apply text-base font-semibold text-gray-900 mb-2 mt-3;
}

.prose h6 {
  @apply text-sm font-semibold text-gray-900 mb-2 mt-3;
}

.prose ul, .prose ol {
  @apply mb-4 pl-6;
}

.prose li {
  @apply mb-2;
}

.prose ul li {
  @apply list-disc;
}

.prose ol li {
  @apply list-decimal;
}

.prose blockquote {
  @apply border-l-4 border-primary pl-4 py-2 my-4 italic text-gray-700 bg-gray-50 rounded-r-lg;
}

.prose code {
  @apply bg-gray-100 text-primary px-2 py-1 rounded text-sm font-mono;
}

.prose pre {
  @apply bg-gray-100 p-4 rounded-lg overflow-x-auto mb-4;
}

.prose pre code {
  @apply bg-transparent p-0;
}

.prose a {
  @apply text-primary hover:text-primary/80 underline;
}

.prose strong {
  @apply font-semibold text-gray-900;
}

.prose em {
  @apply italic;
}

.prose hr {
  @apply my-8 border-gray-200;
}

.prose table {
  @apply w-full border-collapse border border-gray-200 my-4;
}

.prose th {
  @apply bg-gray-50 border border-gray-200 px-4 py-2 text-left font-semibold;
}

.prose td {
  @apply border border-gray-200 px-4 py-2;
}

.prose img {
  @apply max-w-full h-auto rounded-lg my-4;
}

/* Dark mode prose styles */
.dark .prose {
  @apply text-gray-100;
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3,
.dark .prose h4,
.dark .prose h5,
.dark .prose h6 {
  @apply text-gray-100;
}

.dark .prose blockquote {
  @apply text-gray-300 bg-gray-800 border-primary;
}

.dark .prose code {
  @apply bg-gray-800 text-primary;
}

.dark .prose pre {
  @apply bg-gray-800;
}

.dark .prose strong {
  @apply text-gray-100;
}

.dark .prose th {
  @apply bg-gray-800 border-gray-700;
}

.dark .prose td {
  @apply border-gray-700;
}

/* Blog view content code blocks */
.blog-view-content .code-block-wrapper {
  margin: 1.5rem 0 !important;
  border-radius: 0.5rem !important;
  overflow: hidden !important;
  background: #1f2937 !important;
  border: 1px solid #374151 !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  display: block !important;
  position: relative !important;
}

.blog-view-content .code-block-header {
  background: #1f2937 !important;
  border-bottom: 1px solid #374151 !important;
  padding: 0.5rem 1rem !important;
  color: white !important;
  font-size: 0.875rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.blog-view-content .code-block-header .language-label {
  font-weight: 500 !important;
  color: #d1d5db !important;
  text-transform: capitalize !important;
}

.blog-view-content .code-block-header .copy-button {
  background: transparent !important;
  border: 1px solid #374151 !important;
  color: #d1d5db !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 0.375rem !important;
  font-size: 0.75rem !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.blog-view-content .code-block-header .copy-button:hover {
  background: #374151 !important;
  color: white !important;
}

.blog-view-content .code-block-content {
  background: #111827 !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
  display: block !important;
}

.blog-view-content .code-block-content pre {
  background: #111827 !important;
  color: #f9fafb !important;
  margin: 0 !important;
  padding: 1rem !important;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  border-radius: 0 !important;
  overflow: visible !important;
  white-space: pre-wrap !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  display: block !important;
}

.blog-view-content .code-block-content code {
  background: transparent !important;
  color: inherit !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-family: inherit !important;
  font-size: inherit !important;
  font-weight: inherit !important;
}

/* Syntax highlighting colors for blog view */
.blog-view-content .hljs-keyword { color: #8b5cf6 !important; }
.blog-view-content .hljs-string { color: #10b981 !important; }
.blog-view-content .hljs-comment { color: #6b7280 !important; font-style: italic !important; }
.blog-view-content .hljs-number { color: #f59e0b !important; }
.blog-view-content .hljs-function { color: #3b82f6 !important; }
.blog-view-content .hljs-variable { color: #ef4444 !important; }
.blog-view-content .hljs-title { color: #06b6d4 !important; }
.blog-view-content .hljs-attr { color: #8b5cf6 !important; }
.blog-view-content .hljs-tag { color: #ef4444 !important; }
.blog-view-content .hljs-built_in { color: #f59e0b !important; }
.blog-view-content .hljs-literal { color: #06b6d4 !important; }
.blog-view-content .hljs-meta { color: #6b7280 !important; }
.blog-view-content .hljs-selector-tag { color: #ef4444 !important; }
.blog-view-content .hljs-selector-class { color: #f59e0b !important; }
.blog-view-content .hljs-selector-id { color: #3b82f6 !important; }
.blog-view-content .hljs-property { color: #10b981 !important; }
.blog-view-content .hljs-operator { color: #8b5cf6 !important; }

/* Custom scrollbar for code blocks in blog view */
.blog-view-content .code-block-content::-webkit-scrollbar {
  height: 12px !important;
  width: 12px !important;
}

.blog-view-content .code-block-content::-webkit-scrollbar-track {
  background: #374151 !important;
  border-radius: 6px !important;
}

.blog-view-content .code-block-content::-webkit-scrollbar-thumb {
  background: #6b7280 !important;
  border-radius: 6px !important;
  border: 2px solid #374151 !important;
}

.blog-view-content .code-block-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af !important;
}

.blog-view-content .code-block-content::-webkit-scrollbar-corner {
  background: #374151 !important;
}

/* Enhanced default code block styling for blog view */
.blog-view-content pre:not(.code-block-content pre) {
  background: #111827 !important;
  color: #f9fafb !important;
  border-radius: 0.5rem !important;
  border: 1px solid #374151 !important;
  padding: 1rem !important;
  margin: 1.5rem 0 !important;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  overflow-x: auto !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.blog-view-content pre:not(.code-block-content pre) code {
  background: transparent !important;
  color: inherit !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-family: inherit !important;
  font-size: inherit !important;
  font-weight: inherit !important;
}

/* ================================ */
/* TWITTER EMBED STYLES FOR BLOG VIEW */
/* ================================ */

/* Twitter embed wrapper in blog view */
.blog-view-content .twitter-embed-wrapper {
  margin: 2rem 0 !important;
  display: flex !important;
  justify-content: center !important;
  width: 100% !important;
  max-width: none !important;
}

.blog-view-content .twitter-embed-container {
  max-width: 550px !important;
  margin: 0 auto !important;
  border: none !important;
  border-radius: 0 !important;
  overflow: visible !important;
  background: transparent !important;
  box-shadow: none !important;
  width: 100% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.blog-view-content .twitter-embed-content {
  max-width: 100% !important;
  overflow: visible !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.blog-view-content .twitter-embed-content blockquote {
  margin: 0 auto !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
  max-width: 550px !important;
  width: 100% !important;
}

.blog-view-content .twitter-embed-content .twitter-tweet {
  margin: 0 auto !important;
  max-width: 550px !important;
  width: 100% !important;
}

.blog-view-content .twitter-embed-content iframe {
  max-width: 100% !important;
  border: none !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

/* Loading and error states for Twitter embeds in blog view */
.blog-view-content .twitter-embed-loading,
.blog-view-content .twitter-embed-error {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 100px !important;
  padding: 20px !important;
  text-align: center !important;
  color: #657786 !important;
  font-size: 14px !important;
  border: 1px solid #e1e8ed !important;
  border-radius: 12px !important;
  background: white !important;
  max-width: 550px !important;
  margin: 0 auto !important;
}

.dark .blog-view-content .twitter-embed-loading,
.dark .blog-view-content .twitter-embed-error {
  color: #9ca3af !important;
  background: rgb(21 32 43) !important;
  border-color: rgb(56 68 77) !important;
}

.blog-view-content .twitter-embed-error a {
  color: #1da1f2 !important;
  text-decoration: none !important;
  font-weight: 500 !important;
}

.blog-view-content .twitter-embed-error a:hover {
  text-decoration: underline !important;
}

/* Mobile responsiveness for Twitter embeds in blog view */
@media (max-width: 640px) {
  .blog-view-content .twitter-embed-wrapper {
    margin: 1.5rem 0 !important;
  }
  
  .blog-view-content .twitter-embed-container {
    max-width: 100% !important;
    margin: 0 !important;
  }
  
  .blog-view-content .twitter-embed-content {
    padding: 0 !important;
  }
  
  .blog-view-content .twitter-embed-loading,
  .blog-view-content .twitter-embed-error {
    padding: 15px !important;
    font-size: 13px !important;
  }
}

