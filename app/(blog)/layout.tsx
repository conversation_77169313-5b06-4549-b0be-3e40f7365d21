import { Footer } from "@/components/layout/footer";
import { getDictionary } from "@/app/[lang]/dictionaries";
import { HeaderBlog } from "@/components/layout/header-blog";

interface BlogLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    lang: string;
  }>;
}

export default async function BlogLayout({
  children,
  params,
}: BlogLayoutProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <div>
      <HeaderBlog lang={lang} dict={dict} />
      <div className="max-w-7xl mx-auto px-4 md:px-0">{children}</div>
    </div>
  );
}
