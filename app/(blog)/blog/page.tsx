import { getDictionary } from "@/app/[lang]/dictionaries";
import { BlogListing } from "@/feature/(client)/blog";

interface BlogPageProps {
  params: Promise<{
    lang: string;
  }>;
  searchParams: Promise<{
    page?: string;
  }>;
}

const BlogPage = async ({ params, searchParams }: BlogPageProps) => {
  const { lang } = await params;
  const { page } = await searchParams;
  const dict = await getDictionary(lang);

  const currentPage = parseInt(page || "1", 10);

  return <BlogListing dict={dict} lang={lang} initialPage={currentPage} />;
};

export default BlogPage;
