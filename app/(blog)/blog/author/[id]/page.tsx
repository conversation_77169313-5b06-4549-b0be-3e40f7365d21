import { getDictionary } from "@/app/[lang]/dictionaries";
import { AuthorView } from "@/feature/(client)/blog/author";

interface AuthorPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

const AuthorPage = async ({ params }: AuthorPageProps) => {
  const { lang, id } = await params;
  const dict = await getDictionary(lang);

  return (
    <AuthorView
      dict={dict}
      lang={lang}
      authorId={id}
    />
  );
};

export default AuthorPage;