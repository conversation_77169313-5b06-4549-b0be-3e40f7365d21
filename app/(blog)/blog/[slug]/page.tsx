import { getDictionary } from "@/app/[lang]/dictionaries";
import { BlogView } from "@/feature/(client)/blog";

interface BlogViewPageProps {
  params: Promise<{
    lang: string;
    slug: string;
  }>;
}

const BlogViewPage = async ({ params }: BlogViewPageProps) => {
  const { lang, slug } = await params;
  const dict = await getDictionary(lang);

  return (
    <div>
      <BlogView dict={dict} lang={lang} slug={slug} />
    </div>
  );
};

export default BlogViewPage;