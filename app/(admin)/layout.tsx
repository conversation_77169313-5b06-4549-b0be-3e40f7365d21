import { AppSidebar } from "@/components/sidebar/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { requireAuthor } from "@/feature/auth/utils/auth-utils";

interface AdminLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    lang: string;
  }>;
}

export default async function AdminLayout({
  children,
  params,
}: AdminLayoutProps) {
  // Ensure user has author or admin access
  await requireAuthor();

  return (
    <div className="min-h-screen bg-background">
      <SidebarProvider>
        <AppSidebar variant="inset" />
        <SidebarInset>
          <main className="w-full">{children}</main>
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
}
