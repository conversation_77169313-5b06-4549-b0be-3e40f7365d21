import React from "react";
import DashboardHeader from "@/components/sidebar/dashboard-header";
import { BlogPostForm } from "@/feature/blog/post/components/blog-post-form";

interface EditBlogPostPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditBlogPostPage({
  params,
}: EditBlogPostPageProps) {
  const { id } = await params;

  return (
    <DashboardHeader
      title="Edit Blog Post"
      parentTitle="Blog"
      parentHref="/admin/blog"
    >
      <BlogPostForm postId={id} mode="edit" />
    </DashboardHeader>
  );
}
