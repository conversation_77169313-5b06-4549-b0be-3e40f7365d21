import React from "react";
import DashboardHeader from "@/components/sidebar/dashboard-header";
import { ToolEditForm } from "@/feature/tool/components/tool-edit-form";

interface EditToolPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditToolPage({ params }: EditToolPageProps) {
  const { id } = await params;

  return (
    <DashboardHeader
      title="Edit Tool"
      parentTitle="Tools"
      parentHref="/admin/tools"
    >
      <ToolEditForm toolId={id} />
    </DashboardHeader>
  );
}
