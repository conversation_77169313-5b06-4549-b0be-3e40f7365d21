import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { authenticateApiRe<PERSON> } from "@/lib/api-auth";
import {
  CreateMediaMetadataRequest,
  UpdateMediaMetadataRequest,
  MediaFileType,
} from "@/types/media-api";
import { z } from "zod";

// Validation schemas
const createMetadataSchema = z.object({
  file_key: z.string().min(1, "File key is required"),
  file_name: z.string().min(1, "File name is required"),
  original_name: z.string().min(1, "Original name is required"),
  file_url: z.string().url("Valid file URL is required"),
  file_size: z.number().positive("File size must be positive"),
  mime_type: z.string().min(1, "MIME type is required"),
  file_type: z.nativeEnum({
    image: "image",
    video: "video",
    audio: "audio",
    document: "document",
    archive: "archive",
    other: "other",
  } as const),
  folder_path: z.string().default(""),
  title: z.string().optional(),
  alt_text: z.string().optional(),
  caption: z.string().optional(),
  description: z.string().optional(),
});

const updateMetadataSchema = z.object({
  file_key: z.string().min(1, "File key is required"),
  title: z.string().optional(),
  alt_text: z.string().optional(),
  caption: z.string().optional(),
  description: z.string().optional(),
});

// GET - Get metadata by file_key
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateApiRequest();
    if (!authResult) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const fileKey = searchParams.get("file_key");

    if (!fileKey) {
      return NextResponse.json(
        { error: "File key is required" },
        { status: 400 }
      );
    }

    const metadata = await prisma.media.findUnique({
      where: { file_key: fileKey },
      include: {
        uploader: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!metadata) {
      return NextResponse.json(
        { error: "Media metadata not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Media metadata retrieved successfully",
      data: metadata,
    });
  } catch (error) {
    console.error("Error fetching media metadata:", error);
    return NextResponse.json(
      { error: "Failed to fetch media metadata" },
      { status: 500 }
    );
  }
}

// POST - Create new metadata
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateApiRequest();
    if (!authResult) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = createMetadataSchema.parse(body);

    // Check if metadata already exists
    const existingMetadata = await prisma.media.findUnique({
      where: { file_key: validatedData.file_key },
    });

    if (existingMetadata) {
      return NextResponse.json(
        { error: "Media metadata already exists" },
        { status: 409 }
      );
    }

    const metadata = await prisma.media.create({
      data: {
        ...validatedData,
        uploaded_by: authResult.user.id,
      },
      include: {
        uploader: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "Media metadata created successfully",
      data: metadata,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating media metadata:", error);
    return NextResponse.json(
      { error: "Failed to create media metadata" },
      { status: 500 }
    );
  }
}

// PUT - Update existing metadata
export async function PUT(request: NextRequest) {
  try {
    const authResult = await authenticateApiRequest();
    if (!authResult) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updateMetadataSchema.parse(body);

    // Check if metadata exists
    const existingMetadata = await prisma.media.findUnique({
      where: { file_key: validatedData.file_key },
    });

    if (!existingMetadata) {
      return NextResponse.json(
        { error: "Media metadata not found" },
        { status: 404 }
      );
    }

    // Check if user owns the media or is admin
    if (
      existingMetadata.uploaded_by !== authResult.user.id &&
      authResult.user.role !== "ADMIN"
    ) {
      return NextResponse.json({ error: "Permission denied" }, { status: 403 });
    }

    // Extract only the fields we want to update
    const { file_key, ...updateFields } = validatedData;

    const updatedMetadata = await prisma.media.update({
      where: { file_key: validatedData.file_key },
      data: updateFields,
      include: {
        uploader: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "Media metadata updated successfully",
      data: updatedMetadata,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating media metadata:", error);
    return NextResponse.json(
      { error: "Failed to update media metadata" },
      { status: 500 }
    );
  }
}
