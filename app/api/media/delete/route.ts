import { NextRequest, NextResponse } from "next/server";
import {
  DeleteObjectCommand,
  ListObjectsV2Command,
  DeleteObjectsCommand,
} from "@aws-sdk/client-s3";
import { r2Client, R2_BUCKET_NAME } from "@/lib/r2-client";
import { requireAdminAuth } from "@/lib/api-auth";
import { DeleteMediaResponse } from "@/types/media-api";

async function deleteFolder(folderPath: string): Promise<string[]> {
  const deletedKeys: string[] = [];

  // List all objects in the folder
  const listCommand = new ListObjectsV2Command({
    Bucket: R2_BUCKET_NAME,
    Prefix: folderPath,
  });

  const listResponse = await r2Client.send(listCommand);

  if (listResponse.Contents && listResponse.Contents.length > 0) {
    // Delete all objects in the folder
    const objectsToDelete = listResponse.Contents.map((obj) => ({
      Key: obj.Key!,
    }));

    const deleteCommand = new DeleteObjectsCommand({
      Bucket: R2_BUCKET_NAME,
      Delete: {
        Objects: objectsToDelete,
      },
    });

    const deleteResponse = await r2Client.send(deleteCommand);

    if (deleteResponse.Deleted) {
      deletedKeys.push(...deleteResponse.Deleted.map((obj) => obj.Key!));
    }
  }

  return deletedKeys;
}

async function deleteFile(fileKey: string): Promise<boolean> {
  const deleteCommand = new DeleteObjectCommand({
    Bucket: R2_BUCKET_NAME,
    Key: fileKey,
  });

  await r2Client.send(deleteCommand);
  return true;
}

export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    await requireAdminAuth();

    const body = await request.json();
    const { keys } = body;

    if (!keys || !Array.isArray(keys) || keys.length === 0) {
      return NextResponse.json(
        { error: "Keys array is required" },
        { status: 400 }
      );
    }

    const deletedKeys: string[] = [];
    const failedKeys: string[] = [];

    for (const key of keys) {
      try {
        if (key.endsWith("/")) {
          // It's a folder - delete all contents
          const folderDeletedKeys = await deleteFolder(key);
          deletedKeys.push(...folderDeletedKeys);
        } else {
          // It's a file - delete directly
          await deleteFile(key);
          deletedKeys.push(key);
        }
      } catch (error) {
        console.error(`Failed to delete ${key}:`, error);
        failedKeys.push(key);
      }
    }

    const responseData: DeleteMediaResponse = {
      success: true,
      message:
        failedKeys.length > 0
          ? `Deleted ${deletedKeys.length} items, ${failedKeys.length} failed`
          : `Successfully deleted ${deletedKeys.length} items`,
      data: {
        deleted_count: deletedKeys.length,
        failed_keys: failedKeys.length > 0 ? failedKeys : undefined,
      },
    };

    return NextResponse.json(responseData);
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }
      if (error.message === "Admin access required") {
        return NextResponse.json(
          { error: "Admin access required" },
          { status: 403 }
        );
      }
    }

    console.error("Error deleting media:", error);
    return NextResponse.json(
      { error: "Failed to delete media items" },
      { status: 500 }
    );
  }
}
