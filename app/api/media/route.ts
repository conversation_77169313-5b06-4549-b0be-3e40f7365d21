import { NextRequest, NextResponse } from "next/server";
import {
  ListObjectsV2Command,
  GetObjectCommand,
  HeadObjectCommand,
} from "@aws-sdk/client-s3";
import { r2Client, R2_BUCKET_NAME, R2_CUSTOM_DOMAIN } from "@/lib/r2-client";
import { requireAdminAuth } from "@/lib/api-auth";
import {
  ListMediaRequest,
  ListMediaResponse,
  MediaItem,
  MediaFileType,
} from "@/types/media-api";

function getFileType(fileName: string): MediaFileType {
  const extension = fileName.split(".").pop()?.toLowerCase() || "";

  const imageExtensions = [
    "jpg",
    "jpeg",
    "png",
    "gif",
    "webp",
    "svg",
    "bmp",
    "ico",
  ];
  const videoExtensions = [
    "mp4",
    "avi",
    "mov",
    "wmv",
    "flv",
    "webm",
    "mkv",
    "m4v",
  ];
  const audioExtensions = ["mp3", "wav", "flac", "aac", "ogg", "wma", "m4a"];
  const documentExtensions = [
    "pdf",
    "doc",
    "docx",
    "txt",
    "rtf",
    "odt",
    "xls",
    "xlsx",
    "ppt",
    "pptx",
  ];
  const archiveExtensions = ["zip", "rar", "7z", "tar", "gz", "bz2"];

  if (imageExtensions.includes(extension)) return "image";
  if (videoExtensions.includes(extension)) return "video";
  if (audioExtensions.includes(extension)) return "audio";
  if (documentExtensions.includes(extension)) return "document";
  if (archiveExtensions.includes(extension)) return "archive";

  return "other";
}

function formatFileSize(bytes: number): number {
  return Math.round(bytes);
}

function getPublicUrl(key: string): string {
  if (R2_CUSTOM_DOMAIN) {
    return `${R2_CUSTOM_DOMAIN}/${key}`;
  }
  return `https://${R2_BUCKET_NAME}.r2.dev/${key}`;
}

export async function GET(request: NextRequest) {
  try {
    // Require admin authentication
    await requireAdminAuth();

    const searchParams = request.nextUrl.searchParams;
    const path = searchParams.get("path") || "";
    const limit = parseInt(searchParams.get("limit") || "50");
    const continuationToken =
      searchParams.get("continuation_token") || undefined;

    // List objects from R2
    const command = new ListObjectsV2Command({
      Bucket: R2_BUCKET_NAME,
      Prefix: path,
      MaxKeys: limit,
      ContinuationToken: continuationToken,
      Delimiter: "/",
    });

    const response = await r2Client.send(command);

    // Process folders (CommonPrefixes)
    const folders: MediaItem[] = (response.CommonPrefixes || []).map(
      (prefix) => ({
        key: prefix.Prefix!,
        name: prefix.Prefix!.replace(path, "").replace("/", ""),
        type: "folder" as const,
        parent_path: path || undefined,
      })
    );

    // Process files (Contents)
    const files: MediaItem[] = (response.Contents || [])
      .filter((item) => item.Key !== path) // Exclude the current directory itself
      .map((item) => {
        const fileName = item.Key!.split("/").pop() || item.Key!;
        const fileType = getFileType(fileName);
        const extension = fileName.split(".").pop()?.toLowerCase();

        return {
          key: item.Key!,
          name: fileName,
          type: "file" as const,
          file_type: fileType,
          size: item.Size ? formatFileSize(item.Size) : 0,
          last_modified: item.LastModified?.toISOString(),
          url: getPublicUrl(item.Key!),
          parent_path: path || undefined,
          extension: extension,
        };
      });

    const allItems = [...folders, ...files];

    const responseData: ListMediaResponse = {
      success: true,
      message: "Media items retrieved successfully",
      data: {
        items: allItems,
        folders,
        files,
        total_count:
          (response.KeyCount || 0) + (response.CommonPrefixes?.length || 0),
        is_truncated: response.IsTruncated || false,
        next_continuation_token: response.NextContinuationToken,
        current_path: path,
      },
    };

    return NextResponse.json(responseData);
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }
      if (error.message === "Admin access required") {
        return NextResponse.json(
          { error: "Admin access required" },
          { status: 403 }
        );
      }
    }

    console.error("Error listing media:", error);
    return NextResponse.json(
      { error: "Failed to list media items" },
      { status: 500 }
    );
  }
}
