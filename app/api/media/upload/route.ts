import { NextRequest, NextResponse } from "next/server";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { r2Client, R2_BUCKET_NAME, R2_CUSTOM_DOMAIN } from "@/lib/r2-client";
import { requireAdminAuth } from "@/lib/api-auth";
import { UploadMediaResponse } from "@/types/media-api";

function getPublicUrl(key: string): string {
  if (R2_CUSTOM_DOMAIN) {
    return `${R2_CUSTOM_DOMAIN}/${key}`;
  }
  return `https://${R2_BUCKET_NAME}.r2.dev/${key}`;
}

function sanitizeFileName(fileName: string): string {
  // Replace spaces and special characters
  return fileName
    .replace(/[^a-zA-Z0-9.-]/g, "_")
    .replace(/_+/g, "_")
    .replace(/^_|_$/g, "");
}

export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    await requireAdminAuth();

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const folderPath = (formData.get("folder_path") as string) || "";

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Validate file size (50MB limit)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File size exceeds 50MB limit" },
        { status: 400 }
      );
    }

    // Sanitize and create file key
    const sanitizedFileName = sanitizeFileName(file.name);
    const timestamp = Date.now();
    const fileExtension = sanitizedFileName.split(".").pop() || "";
    const baseName = sanitizedFileName.replace(`.${fileExtension}`, "");
    const uniqueFileName = `${baseName}_${timestamp}.${fileExtension}`;

    const fileKey = folderPath
      ? `${folderPath.replace(/\/$/, "")}/${uniqueFileName}`
      : uniqueFileName;

    // Convert file to buffer
    const fileBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(fileBuffer);

    // Upload to R2
    const uploadCommand = new PutObjectCommand({
      Bucket: R2_BUCKET_NAME,
      Key: fileKey,
      Body: buffer,
      ContentType: file.type,
      ContentLength: file.size,
    });

    await r2Client.send(uploadCommand);

    const responseData: UploadMediaResponse = {
      success: true,
      message: "File uploaded successfully",
      data: {
        key: fileKey,
        url: getPublicUrl(fileKey),
        file_name: uniqueFileName,
        file_size: file.size,
        mime_type: file.type,
      },
    };

    return NextResponse.json(responseData);
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }
      if (error.message === "Admin access required") {
        return NextResponse.json(
          { error: "Admin access required" },
          { status: 403 }
        );
      }
    }

    console.error("Error uploading file:", error);
    return NextResponse.json(
      { error: "Failed to upload file" },
      { status: 500 }
    );
  }
}
