import { NextRequest, NextResponse } from "next/server";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { r2Client, R2_BUCKET_NAME } from "@/lib/r2-client";
import { requireAdminAuth } from "@/lib/api-auth";
import { CreateFolderResponse } from "@/types/media-api";

function sanitizeFolderName(folderName: string): string {
  // Replace spaces and special characters, ensure no leading/trailing slashes
  return folderName
    .replace(/[^a-zA-Z0-9.-]/g, "_")
    .replace(/_+/g, "_")
    .replace(/^_|_$/g, "")
    .replace(/^\/|\/$/g, "");
}

export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    await requireAdminAuth();

    const body = await request.json();
    const { folder_name, parent_path } = body;

    if (!folder_name || typeof folder_name !== "string") {
      return NextResponse.json(
        { error: "Folder name is required" },
        { status: 400 }
      );
    }

    // Sanitize folder name
    const sanitizedFolderName = sanitizeFolderName(folder_name);

    if (!sanitizedFolderName) {
      return NextResponse.json(
        { error: "Invalid folder name" },
        { status: 400 }
      );
    }

    // Create folder path
    const basePath = parent_path ? parent_path.replace(/\/$/, "") : "";
    const folderPath = basePath
      ? `${basePath}/${sanitizedFolderName}/`
      : `${sanitizedFolderName}/`;

    // Create a placeholder object to represent the folder
    // S3/R2 doesn't have actual folders, so we create an empty object with trailing slash
    const createFolderCommand = new PutObjectCommand({
      Bucket: R2_BUCKET_NAME,
      Key: folderPath,
      Body: "",
      ContentLength: 0,
    });

    await r2Client.send(createFolderCommand);

    const responseData: CreateFolderResponse = {
      success: true,
      message: "Folder created successfully",
      data: {
        folder_path: folderPath,
        folder_name: sanitizedFolderName,
      },
    };

    return NextResponse.json(responseData);
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }
      if (error.message === "Admin access required") {
        return NextResponse.json(
          { error: "Admin access required" },
          { status: 403 }
        );
      }
    }

    console.error("Error creating folder:", error);
    return NextResponse.json(
      { error: "Failed to create folder" },
      { status: 500 }
    );
  }
}
