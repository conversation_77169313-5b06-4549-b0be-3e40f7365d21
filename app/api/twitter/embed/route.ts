import { NextRequest } from "next/server";
import {
  successResponse,
  errorResponse,
  validationErrorResponse,
  serverErrorResponse,
} from "@/lib/api-responses";

interface TwitterEmbedData {
  url: string;
  author_name: string;
  author_url: string;
  html: string;
  width: number;
  height: number | null;
  type: string;
  cache_age: string;
  provider_name: string;
  provider_url: string;
  version: string;
}

// GET /api/twitter/embed - Get Twitter embed data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get("url");

    if (!url) {
      return validationErrorResponse("URL parameter is required");
    }

    // Validate that it's a Twitter/X URL
    const isValidTwitterUrl = /^https?:\/\/(twitter\.com|x\.com)\/\w+\/status\/\d+/.test(url);
    if (!isValidTwitterUrl) {
      return validationErrorResponse("Invalid Twitter/X URL");
    }

    // Fetch from Twitter oEmbed API with parameters for full rich embed
    const oembedUrl = `https://publish.twitter.com/oembed?url=${encodeURIComponent(url)}&theme=light&align=center&hide_thread=false&maxwidth=550&dnt=false&widget_type=&omit_script=false`;
    
    const response = await fetch(oembedUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; TwitterEmbedBot/1.0)',
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return errorResponse("Tweet not found or private", 404);
      }
      throw new Error(`Twitter API responded with status ${response.status}`);
    }

    const embedData: TwitterEmbedData = await response.json();

    // Return the embed data
    return successResponse({
      html: embedData.html,
      author_name: embedData.author_name,
      author_url: embedData.author_url,
      url: embedData.url,
      width: embedData.width,
      height: embedData.height,
      provider_name: embedData.provider_name,
      provider_url: embedData.provider_url,
    }, "Twitter embed data fetched successfully");

  } catch (error) {
    console.error("Error fetching Twitter embed:", error);
    return serverErrorResponse("Failed to fetch Twitter embed data");
  }
} 