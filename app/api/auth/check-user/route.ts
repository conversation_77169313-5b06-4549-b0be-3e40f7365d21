import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { checkVerificationEmailLimit } from "@/feature/auth/utils/email-rate-limit";
import { getDictionaryFromRequest } from "@/feature/lang/translation/server-dict";

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      const dict = await getDictionaryFromRequest(request);
      return NextResponse.json(
        { error: dict?.common?.email_required || "Email is required" },
        { status: 400 }
      );
    }

    // Get dictionary for internationalized messages
    const dict = await getDictionaryFromRequest(request);

    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        isVerified: true,
        password: true,
        verificationEmailCount: true,
        lastVerificationEmailSent: true,
      },
    });

    if (!user) {
      return NextResponse.json({
        exists: false,
        isVerified: false,
      });
    }

    // Check if user has a password (registered with credentials)
    const hasPassword = !!user.password;

    // Check verification email rate limiting
    const rateLimitResult = await checkVerificationEmailLimit(email, dict);

    return NextResponse.json({
      exists: true,
      isVerified: user.isVerified,
      hasPassword,
      canResendVerification: rateLimitResult.canSend,
      verificationEmailCount: user.verificationEmailCount,
      rateLimitReason: rateLimitResult.reason,
      remainingAttempts: rateLimitResult.remainingAttempts,
    });
  } catch (error) {
    const dict = await getDictionaryFromRequest(request);
    return NextResponse.json(
      { error: dict?.api?.check_user_failed || "Failed to check user" },
      { status: 500 }
    );
  }
}
