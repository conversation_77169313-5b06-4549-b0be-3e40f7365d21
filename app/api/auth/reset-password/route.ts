import { NextRequest, NextResponse } from "next/server";
import { hash } from "bcryptjs";
import { prisma } from "@/lib/prisma";
import { checkTokenValidity } from "@/feature/auth/actions/verification-token";
import { getDictionaryFromRequest } from "@/feature/lang/translation/server-dict";

export async function POST(request: NextRequest) {
  try {
    const { token, password } = await request.json();

    // Get dictionary for internationalized messages
    const dict = await getDictionaryFromRequest(request);

    if (!token || !password) {
      return NextResponse.json(
        {
          error:
            dict?.api?.token_password_required ||
            "Token and password are required",
        },
        { status: 400 }
      );
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json(
        {
          error:
            dict?.api?.password_min_length ||
            "Password must be at least 8 characters long",
        },
        { status: 400 }
      );
    }

    if (!/(?=.*[a-z])/.test(password)) {
      return NextResponse.json(
        {
          error:
            dict?.api?.password_lowercase ||
            "Password must contain at least one lowercase letter",
        },
        { status: 400 }
      );
    }

    if (!/(?=.*[A-Z])/.test(password)) {
      return NextResponse.json(
        {
          error:
            dict?.api?.password_uppercase ||
            "Password must contain at least one uppercase letter",
        },
        { status: 400 }
      );
    }

    if (!/(?=.*\d)/.test(password)) {
      return NextResponse.json(
        {
          error:
            dict?.api?.password_number ||
            "Password must contain at least one number",
        },
        { status: 400 }
      );
    }

    // Verify the token without deleting it yet
    const verificationToken = await checkTokenValidity(token);

    if (!verificationToken) {
      return NextResponse.json(
        {
          error:
            dict?.api?.invalid_expired_reset_token ||
            "Invalid or expired reset token",
        },
        { status: 400 }
      );
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email: verificationToken.identifier },
      select: {
        id: true,
        email: true,
        password: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: dict?.common?.user_not_found || "User not found" },
        { status: 404 }
      );
    }

    // Only allow password reset for users with passwords (not OAuth-only users)
    if (!user.password) {
      return NextResponse.json(
        {
          error:
            dict?.api?.social_login_no_password_reset ||
            "This account uses social login. Password reset is not available.",
        },
        { status: 400 }
      );
    }

    // Hash the new password
    const hashedPassword = await hash(password, 12);

    // Update user's password
    await prisma.user.update({
      where: { email: verificationToken.identifier },
      data: {
        password: hashedPassword,
      },
    });

    // Delete the used token to prevent reuse
    await prisma.verificationToken.delete({
      where: {
        identifier_token: {
          identifier: verificationToken.identifier,
          token: verificationToken.token,
        },
      },
    });

    return NextResponse.json({
      message:
        dict?.api?.password_reset_successfully || "Password reset successfully",
    });
  } catch (error) {
    const dict = await getDictionaryFromRequest(request);
    return NextResponse.json(
      { error: dict?.api?.failed_reset_password || "Failed to reset password" },
      { status: 500 }
    );
  }
}
