import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { generateVerificationToken } from "@/feature/auth/actions/verification-token";
import { sendVerificationEmail } from "@/feature/auth/actions/email";
import {
  checkVerificationEmailLimit,
  incrementVerificationEmailCount,
} from "@/feature/auth/utils/email-rate-limit";
import {
  getDictionaryFromRequest,
  getLanguageFromRequest,
} from "@/feature/lang/translation/server-dict";

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      const dict = await getDictionaryFromRequest(request);
      return NextResponse.json(
        { error: dict?.common?.email_required || "Email is required" },
        { status: 400 }
      );
    }

    // Get dictionary and language for internationalized messages
    const dict = await getDictionaryFromRequest(request);
    const lang = getLanguageFromRequest(request);

    // Check rate limiting first
    const rateLimitResult = await checkVerificationEmailLimit(email, dict);

    if (!rateLimitResult.canSend) {
      return NextResponse.json(
        {
          error: rateLimitResult.reason,
          nextAllowedTime: rateLimitResult.nextAllowedTime,
          remainingAttempts: rateLimitResult.remainingAttempts,
        },
        { status: 429 }
      );
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return NextResponse.json(
        { error: dict?.common?.user_not_found || "User not found" },
        { status: 404 }
      );
    }

    // Check if user is already verified
    if (user.isVerified) {
      return NextResponse.json(
        {
          error:
            dict?.api?.email_already_verified || "Email is already verified",
        },
        { status: 400 }
      );
    }

    // Generate new verification token
    const verificationToken = await generateVerificationToken(email);

    // Send verification email
    await sendVerificationEmail(email, verificationToken.token, lang);

    // Increment the email count
    await incrementVerificationEmailCount(email);

    return NextResponse.json({
      message:
        dict?.api?.verification_email_sent_successfully ||
        "Verification email sent successfully",
      remainingAttempts: rateLimitResult.remainingAttempts
        ? rateLimitResult.remainingAttempts - 1
        : 0,
    });
  } catch (error) {
    const dict = await getDictionaryFromRequest(request);
    return NextResponse.json(
      {
        error:
          dict?.api?.resend_verification_failed ||
          "Failed to resend verification email",
      },
      { status: 500 }
    );
  }
}
