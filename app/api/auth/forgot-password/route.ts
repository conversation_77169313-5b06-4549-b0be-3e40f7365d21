import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { generateVerificationToken } from "@/feature/auth/actions/verification-token";
import { sendPasswordResetEmail } from "@/feature/auth/actions/email";
import {
  checkPasswordResetLimit,
  incrementPasswordResetCount,
} from "@/feature/auth/utils/email-rate-limit";
import {
  getDictionaryFromRequest,
  getLanguageFromRequest,
} from "@/feature/lang/translation/server-dict";

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      const dict = await getDictionaryFromRequest(request);
      return NextResponse.json(
        { error: dict?.common?.email_required || "Email is required" },
        { status: 400 }
      );
    }

    // Get dictionary and language for internationalized messages
    const dict = await getDictionaryFromRequest(request);
    const lang = getLanguageFromRequest(request);

    // Check rate limiting first
    const rateLimitResult = await checkPasswordResetLimit(email, dict);

    if (!rateLimitResult.canSend) {
      return NextResponse.json(
        {
          error: rateLimitResult.reason,
          nextAllowedTime: rateLimitResult.nextAllowedTime,
        },
        { status: 429 }
      );
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        password: true,
      },
    });

    if (!user) {
      // Don't reveal if user exists or not for security
      return NextResponse.json({
        message:
          dict?.api?.password_reset_sent_message ||
          "If an account with that email exists, we've sent a password reset link.",
      });
    }

    // Only allow password reset for users with passwords (not OAuth-only users)
    if (!user.password) {
      return NextResponse.json({
        message:
          dict?.api?.password_reset_sent_message ||
          "If an account with that email exists, we've sent a password reset link.",
      });
    }

    // Generate password reset token (reusing verification token structure)
    const resetToken = await generateVerificationToken(email);

    // Send password reset email
    await sendPasswordResetEmail(email, resetToken.token, lang);

    // Increment the password reset count
    await incrementPasswordResetCount(email);

    return NextResponse.json({
      message:
        dict?.api?.password_reset_sent_message ||
        "If an account with that email exists, we've sent a password reset link.",
    });
  } catch (error) {
    const dict = await getDictionaryFromRequest(request);
    return NextResponse.json(
      {
        error:
          dict?.api?.forgot_password_failed ||
          "Failed to send password reset email",
      },
      { status: 500 }
    );
  }
}
