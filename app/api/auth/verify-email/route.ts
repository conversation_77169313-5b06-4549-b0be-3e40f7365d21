import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyEmailToken } from "@/feature/auth/actions/verification-token";
import { sendWelcomeEmail } from "@/feature/auth/actions/email";
import { resetVerificationEmailCount } from "@/feature/auth/utils/email-rate-limit";
import { getDictionaryFromRequest } from "@/feature/lang/translation/server-dict";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const token = searchParams.get("token");

    if (!token) {
      return NextResponse.redirect(
        new URL("/login?error=InvalidToken", request.url)
      );
    }

    // Verify the token
    const verificationToken = await verifyEmailToken(token);

    if (!verificationToken) {
      return NextResponse.redirect(
        new URL("/login?error=ExpiredToken", request.url)
      );
    }

    // Update user's email verification status
    const user = await prisma.user.update({
      where: { email: verificationToken.identifier },
      data: {
        emailVerified: new Date(),
        isVerified: true,
      },
    });

    // Reset verification email count since email is now verified
    await resetVerificationEmailCount(user.email);

    // Send welcome email
    await sendWelcomeEmail(user.email, user.name || "User");

    // Redirect to success page
    return NextResponse.redirect(new URL("/login?verified=true", request.url));
  } catch (error) {
    const dict = await getDictionaryFromRequest(request);
    return NextResponse.json(
      { error: dict?.api?.verification_failed || "Verification failed" },
      { status: 500 }
    );
  }
}
