import { NextRequest, NextResponse } from "next/server";
import { hash } from "bcryptjs";
import { prisma } from "@/lib/prisma";
import { generateVerificationToken } from "@/feature/auth/actions/verification-token";
import { sendVerificationEmail } from "@/feature/auth/actions/email";
import { incrementVerificationEmailCount } from "@/feature/auth/utils/email-rate-limit";
import {
  getDictionaryFromRequest,
  getLanguageFromRequest,
} from "@/feature/lang/translation/server-dict";
import { generateUserSlug, ensureUniqueUserSlug } from "@/lib/slug-utils";

export async function POST(request: NextRequest) {
  try {
    const { name, email, password } = await request.json();

    // Get dictionary and language for internationalized messages
    const dict = await getDictionaryFromRequest(request);
    const lang = getLanguageFromRequest(request);

    // Validate input
    if (!name || !email || !password) {
      return NextResponse.json(
        {
          message:
            dict?.api?.missing_required_fields || "Missing required fields",
        },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { message: dict?.api?.user_already_exists || "User already exists" },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await hash(password, 12);

    // Generate unique slug for user
    const baseSlug = generateUserSlug(name);
    const existingUsers = await prisma.user.findMany({
      select: { slug: true },
    });
    const existingSlugs = existingUsers.map((u) => u.slug);
    const uniqueSlug = ensureUniqueUserSlug(baseSlug, existingSlugs);

    // Create user (email not verified initially)
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        slug: uniqueSlug,
        about: "",
        role: "USER",
        emailVerified: null, // Not verified yet
        isVerified: false, // Not verified yet
      },
    });

    // Generate verification token and send email
    const verificationToken = await generateVerificationToken(email);
    await sendVerificationEmail(email, verificationToken.token, lang);

    // Increment verification email count
    await incrementVerificationEmailCount(email);

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    return NextResponse.json(
      {
        message:
          dict?.api?.user_created_successfully ||
          "User created successfully. Please check your email to verify your account.",
        user: userWithoutPassword,
        requiresVerification: true,
      },
      { status: 201 }
    );
  } catch (error) {
    const dict = await getDictionaryFromRequest(request);
    return NextResponse.json(
      { error: dict?.api?.registration_failed || "Registration failed" },
      { status: 500 }
    );
  }
}
