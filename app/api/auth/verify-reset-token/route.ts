import { NextRequest, NextResponse } from "next/server";
import { checkTokenValidity } from "@/feature/auth/actions/verification-token";
import { getDictionaryFromRequest } from "@/feature/lang/translation/server-dict";

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    // Get dictionary for internationalized messages
    const dict = await getDictionaryFromRequest(request);

    if (!token) {
      return NextResponse.json(
        { error: dict?.api?.token_required || "Token is required" },
        { status: 400 }
      );
    }

    // Check token validity without deleting it
    const verificationToken = await checkTokenValidity(token);

    if (!verificationToken) {
      return NextResponse.json(
        {
          error:
            dict?.api?.invalid_expired_reset_token ||
            "Invalid or expired reset token",
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      valid: true,
      email: verificationToken.identifier,
    });
  } catch (error) {
    const dict = await getDictionaryFromRequest(request);
    return NextResponse.json(
      {
        error:
          dict?.api?.token_verification_failed || "Token verification failed",
      },
      { status: 500 }
    );
  }
}
