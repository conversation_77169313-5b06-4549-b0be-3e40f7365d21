import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  successResponse,
  notFoundResponse,
  serverErrorResponse,
} from "@/lib/api-responses";

// GET /api/(public)/v1/user/[slug] - Get user profile by slug (public)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    const user = await prisma.user.findUnique({
      where: { slug },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        slug: true,
        about: true,
        role: true,
        emailVerified: true,
        isVerified: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            ToolReview: true,
            ToolSave: true,
            BlogPost: true,
          },
        },
      },
    });

    if (!user) {
      return notFoundResponse("User not found");
    }

    return successResponse(
      {
        id: user.id,
        name: user.name,
        email: user.email,
        image: user.image,
        slug: user.slug,
        about: user.about,
        role: user.role,
        emailVerified: user.emailVerified,
        isVerified: user.isVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        stats: {
          reviewsCount: user._count.ToolReview,
          savedToolsCount: user._count.ToolSave,
          blogPostsCount: user._count.BlogPost,
        },
      },
      "User profile retrieved successfully"
    );
  } catch (error) {
    console.error("GET /api/(public)/v1/user/[slug] error:", error);
    return serverErrorResponse("Failed to retrieve user profile");
  }
}
