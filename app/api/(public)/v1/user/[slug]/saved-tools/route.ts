import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  successResponse,
  notFoundResponse,
  serverErrorResponse,
} from "@/lib/api-responses";

// GET /api/v1/user/[slug]/saved-tools - Get user's saved tools (public)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    
    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(parseInt(searchParams.get("limit") || "20"), 100);
    const search = searchParams.get("search") || "";
    const toolType = searchParams.get("tool_type");
    const categoryId = searchParams.get("category_id");
    const language = searchParams.get("language") || "en";

    const skip = (page - 1) * limit;

    // Check if user exists by slug
    const user = await prisma.user.findUnique({
      where: { slug },
      select: { id: true },
    });

    if (!user) {
      return notFoundResponse("User not found");
    }

    // Build where clause for tool filtering
    const toolWhere: any = {
      is_published: true,
      is_available: true,
    };

    if (search) {
      toolWhere.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { slug: { contains: search, mode: "insensitive" } },
        {
          translations: {
            some: {
              OR: [
                { name: { contains: search, mode: "insensitive" } },
                {
                  short_description: { contains: search, mode: "insensitive" },
                },
              ],
            },
          },
        },
      ];
    }

    if (toolType) {
      toolWhere.tool_type = toolType;
    }

    if (categoryId) {
      toolWhere.category_ids = { has: parseInt(categoryId) };
    }

    // Get total count of saved tools
    const total = await prisma.toolSave.count({
      where: {
        user_id: user.id,
        tool: toolWhere,
      },
    });

    // Get saved tools with tool details
    const savedTools = await prisma.toolSave.findMany({
      where: {
        user_id: user.id,
        tool: toolWhere,
      },
      include: {
        tool: {
          include: {
            translations: {
              where: {
                language_code: language,
              },
            },
          },
        },
      },
      orderBy: {
        created_at: "desc",
      },
      skip,
      take: limit,
    });

    // Get categories for the tools
    const categoryIds = [
      ...new Set(
        savedTools.flatMap((st) => st.tool.category_ids)
      ),
    ];

    const categories = await prisma.toolCategory.findMany({
      where: {
        id: { in: categoryIds },
        is_active: true,
      },
      include: {
        translations: {
          where: { language_code: language },
        },
      },
    });

    const categoryMap = new Map(
      categories.map((cat) => [
        cat.id,
        {
          id: cat.id,
          name: cat.name,
          slug: cat.slug,
          super_category: cat.super_category,
          icon_url: cat.icon_url,
          color: cat.color,
          translations: cat.translations,
        },
      ])
    );

    const totalPages = Math.ceil(total / limit);

    const response = {
      items: savedTools.map((savedTool) => ({
        id: savedTool.id,
        saved_at: savedTool.created_at,
        tool: {
          id: savedTool.tool.id,
          name: savedTool.tool.name,
          slug: savedTool.tool.slug,
          logo_url: savedTool.tool.logo_url,
          tool_type: savedTool.tool.tool_type,
          pricing_models: savedTool.tool.pricing_models,
          website_url: savedTool.tool.website_url,
          category_ids: savedTool.tool.category_ids,
          tag_ids: savedTool.tool.tag_ids,
          created_at: savedTool.tool.created_at,
          updated_at: savedTool.tool.updated_at,
          translations: savedTool.tool.translations.map((t) => ({
            id: t.id,
            language_code: t.language_code,
            name: t.name,
            short_description: t.short_description,
          })),
          categories: savedTool.tool.category_ids
            .map((catId) => categoryMap.get(catId))
            .filter(Boolean),
        },
      })),
      pagination: {
        page,
        limit,
        total,
        total_pages: totalPages,
        has_next: page < totalPages,
        has_prev: page > 1,
      },
    };

    return successResponse(response, "Saved tools retrieved successfully");
  } catch (error) {
    console.error("GET /api/v1/user/[slug]/saved-tools error:", error);
    return serverErrorResponse("Failed to retrieve saved tools");
  }
}
