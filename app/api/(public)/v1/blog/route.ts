import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  successResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { BlogStatus } from "@/types/blog-api";

// Blog listing response type for public API
export interface BlogListingPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string | null;
  featured_image_url: string | null;
  published_at: Date | null;
  author: {
    id: string;
    name: string | null;
    image: string | null;
  };
  parent_category: {
    id: number;
    name: string;
    slug: string;
  } | null;
}

export interface BlogListingResponse {
  posts: BlogListingPost[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// GET /api/v1/blog - Get all published blog posts with pagination (public API)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = 15; // Fixed limit as per requirements
    const offset = (page - 1) * limit;

    // Get total count of published posts
    const total = await prisma.blogPost.count({
      where: {
        status: BlogStatus.PUBLISHED,
        published_at: { lte: new Date() },
      },
    });

    // Get blog posts
    const posts = await prisma.blogPost.findMany({
      where: {
        status: BlogStatus.PUBLISHED,
        published_at: { lte: new Date() },
      },
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        featured_image_url: true,
        published_at: true,
        category_ids: true,
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
      orderBy: {
        published_at: "desc",
      },
      skip: offset,
      take: limit,
    });

    // Get all categories to find parent categories
    const categoryIds = [...new Set(posts.flatMap(post => post.category_ids))];
    const categories = await prisma.blogCategory.findMany({
      where: {
        id: { in: categoryIds },
      },
      select: {
        id: true,
        name: true,
        slug: true,
        parent_id: true,
      },
    });

    // Create category lookup map
    const categoryMap = new Map(categories.map(cat => [cat.id, cat]));

    // Helper function to get parent category
    const getParentCategory = (categoryIds: number[]) => {
      for (const catId of categoryIds) {
        const category = categoryMap.get(catId);
        if (category) {
          if (category.parent_id === null) {
            // This is a parent category
            return {
              id: category.id,
              name: category.name,
              slug: category.slug,
            };
          } else {
            // This is a child category, find its parent
            const parentCategory = categoryMap.get(category.parent_id);
            if (parentCategory) {
              return {
                id: parentCategory.id,
                name: parentCategory.name,
                slug: parentCategory.slug,
              };
            }
          }
        }
      }
      return null;
    };

    // Format posts with parent category data
    const formattedPosts: BlogListingPost[] = posts.map(post => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      featured_image_url: post.featured_image_url,
      published_at: post.published_at,
      author: {
        id: post.author.id,
        name: post.author.name,
        image: post.author.image,
      },
      parent_category: getParentCategory(post.category_ids),
    }));

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const response: BlogListingResponse = {
      posts: formattedPosts,
      pagination: {
        page,
        limit,
        total,
        total_pages: totalPages,
        has_next: hasNext,
        has_prev: hasPrev,
      },
    };

    return successResponse(response, "Blog posts retrieved successfully");
  } catch (error) {
    console.error("GET /api/v1/blog error:", error);
    return serverErrorResponse("Failed to retrieve blog posts");
  }
}
