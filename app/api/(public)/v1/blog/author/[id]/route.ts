import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  successResponse,
  notFoundResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { BlogStatus } from "@/types/blog-api";

// GET /api/v1/blog/author/[id] - Get author with their blog posts (public API)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    
    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Find author by ID
    const author = await prisma.user.findUnique({
      where: {
        id,
        role: { in: ["AUTHOR", "ADMIN"] }, // Only authors and admins can have blog posts
      },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!author) {
      return notFoundResponse("Author not found");
    }

    // Get total count of published posts by this author
    const totalPosts = await prisma.blogPost.count({
      where: {
        author_id: id,
        status: BlogStatus.PUBLISHED,
      },
    });

    // Get paginated blog posts by this author
    const blogPosts = await prisma.blogPost.findMany({
      where: {
        author_id: id,
        status: BlogStatus.PUBLISHED,
      },
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        featured_image_url: true,
        published_at: true,
        created_at: true,
        category_ids: true,
      },
      orderBy: {
        published_at: "desc",
      },
      skip: offset,
      take: limit,
    });

    // Get categories for the blog posts
    const categoryIds = [...new Set(blogPosts.flatMap(post => post.category_ids))];
    const categories = await prisma.blogCategory.findMany({
      where: {
        id: { in: categoryIds },
      },
      select: {
        id: true,
        name: true,
        slug: true,
        description: true,
        parent_id: true,
      },
    });

    // Create category lookup map
    const categoryMap = new Map(categories.map(cat => [cat.id, cat]));

    // Format blog posts with category data
    const formattedPosts = blogPosts.map(post => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      featured_image_url: post.featured_image_url,
      published_at: post.published_at,
      created_at: post.created_at,
      categories: post.category_ids.map(catId => categoryMap.get(catId)).filter(Boolean),
    }));

    // Calculate pagination info
    const totalPages = Math.ceil(totalPosts / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const response = {
      author: {
        id: author.id,
        name: author.name,
        email: author.email,
        image: author.image,
        role: author.role,
        created_at: author.createdAt,
        updated_at: author.updatedAt,
      },
      posts: formattedPosts,
      pagination: {
        page,
        limit,
        total: totalPosts,
        total_pages: totalPages,
        has_next: hasNext,
        has_prev: hasPrev,
      },
    };

    return successResponse(response, "Author data retrieved successfully");
  } catch (error) {
    console.error("GET /api/v1/blog/author/[id] error:", error);
    return serverErrorResponse("Failed to retrieve author data");
  }
}
