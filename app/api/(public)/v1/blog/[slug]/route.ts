import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  successResponse,
  notFoundResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { BlogPostResponse, BlogStatus, RelatedPost } from "@/types/blog-api";

// GET /api/v1/blog/[slug] - Get single blog post by slug (public API)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Find blog post by slug and ensure it's published
    const blogPost = await prisma.blogPost.findUnique({
      where: {
        slug,
        status: BlogStatus.PUBLISHED,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    if (!blogPost) {
      return notFoundResponse("Blog post not found");
    }

    // Get categories and tags data
    const [categories, tags] = await Promise.all([
      prisma.blogCategory.findMany({
        where: {
          id: { in: blogPost.category_ids },
        },
      }),
      prisma.blogTag.findMany({
        where: {
          id: { in: blogPost.tag_ids },
        },
      }),
    ]);

    // Fetch related posts
    const relatedPosts = await prisma.blogPost.findMany({
      where: {
        id: { not: blogPost.id },
        status: BlogStatus.PUBLISHED,
        OR: blogPost.category_ids.map((categoryId) => ({
          category_ids: { has: categoryId },
        })),
      },
      select: {
        id: true,
        title: true,
        slug: true,
        featured_image_url: true,
      },
      orderBy: {
        published_at: "desc",
      },
      take: 2,
    });

    // Fetch recent posts
    const recentPosts = await prisma.blogPost.findMany({
      where: {
        id: { not: blogPost.id },
        status: BlogStatus.PUBLISHED,
      },
      select: {
        id: true,
        title: true,
        slug: true,
        featured_image_url: true,
      },
      orderBy: {
        published_at: "desc",
      },
      take: 5,
    });

    const response: BlogPostResponse = {
      id: blogPost.id,
      title: blogPost.title,
      slug: blogPost.slug,
      excerpt: blogPost.excerpt,
      content: blogPost.content,
      status: blogPost.status as BlogStatus,
      meta_title: blogPost.meta_title,
      meta_description: blogPost.meta_description,
      featured_image_url: blogPost.featured_image_url,
      author_id: blogPost.author_id,
      category_ids: blogPost.category_ids,
      tag_ids: blogPost.tag_ids,
      published_at: blogPost.published_at,
      created_at: blogPost.created_at,
      updated_at: blogPost.updated_at,
      author: blogPost.author
        ? {
            id: blogPost.author.id,
            name: blogPost.author.name,
            email: blogPost.author.email,
            image: blogPost.author.image,
          }
        : undefined,
      categories: categories.map((cat) => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
        description: cat.description,
        parent_id: cat.parent_id,
        created_at: cat.created_at,
        updated_at: cat.updated_at,
      })),
      tags: tags.map((tag) => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
        description: tag.description,
        created_at: tag.created_at,
        updated_at: tag.updated_at,
      })),
      related_posts: relatedPosts as RelatedPost[],
      recent_posts: recentPosts as RelatedPost[],
    };

    return successResponse(response, "Blog post retrieved successfully");
  } catch (error) {
    console.error("GET /api/v1/blog/[slug] error:", error);
    return serverErrorResponse("Failed to retrieve blog post");
  }
}
