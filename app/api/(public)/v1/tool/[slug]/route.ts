import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  successResponse,
  notFoundResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { ToolResponse, ToolType, PricingModel, ToolStatus, SuperCategory } from "@/types/tool-api";
import { authenticateApiRequest } from "@/lib/api-auth";

// GET /api/v1/tool/[slug] - Get single tool by slug with reviews (public API)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    const language = searchParams.get("lang") || "en";

    // Review pagination parameters
    const reviewPage = parseInt(searchParams.get("review-page") || "1");
    const reviewLimit = parseInt(searchParams.get("review-limit") || "10");
    const reviewRating = searchParams.get("review-rating")
      ? parseInt(searchParams.get("review-rating")!)
      : undefined;

    // Find tool by slug and ensure it's published
    const tool = await prisma.tool.findUnique({
      where: {
        slug,
        is_published: true,
        is_available: true,
      },
      include: {
        translations: {
          where: { language_code: language },
        },
        faqs: {
          where: { is_active: true },
          include: {
            translations: {
              where: { language_code: language },
            },
          },
          orderBy: { order: "asc" },
        },
      },
    });

    if (!tool) {
      return notFoundResponse("Tool not found");
    }

    // Get categories and tags data
    const categories = await prisma.toolCategory.findMany({
      where: {
        id: { in: tool.category_ids },
        is_active: true,
      },
      include: {
        translations: {
          where: { language_code: language },
        },
      },
    });

    const tags = await prisma.toolTag.findMany({
      where: {
        id: { in: tool.tag_ids },
        is_active: true,
      },
      include: {
        translations: {
          where: { language_code: language },
        },
      },
    });

    // Get reviews with pagination
    const reviewSkip = (reviewPage - 1) * reviewLimit;
    const reviewWhere: any = {
      tool_id: tool.id,
      is_published: true,
    };

    if (reviewRating !== undefined) {
      reviewWhere.rating = reviewRating;
    }

    // Check if user is authenticated to get their vote information
    const authResult = await authenticateApiRequest();

    // Get all reviews for quick review summary (not filtered by rating)
    const allReviewsWhere = {
      tool_id: tool.id,
      is_published: true,
    };

    const [reviews, reviewTotal, allReviews, savedCount, userSave] =
      await Promise.all([
        prisma.toolReview.findMany({
          where: reviewWhere,
          skip: reviewSkip,
          take: reviewLimit,
          orderBy: { created_at: "desc" },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
            votes: authResult
              ? {
                  where: {
                    user_id: authResult.user.id,
                  },
                  select: {
                    vote_type: true,
                  },
                }
              : false,
          },
        }),
        prisma.toolReview.count({ where: reviewWhere }),
        prisma.toolReview.findMany({
          where: allReviewsWhere,
          select: {
            rating: true,
          },
        }),
        // Get total saved count
        prisma.toolSave.count({
          where: {
            tool_id: tool.id,
          },
        }),
        // Check if current user has saved this tool
        authResult
          ? prisma.toolSave.findUnique({
              where: {
                tool_id_user_id: {
                  tool_id: tool.id,
                  user_id: authResult.user.id,
                },
              },
            })
          : null,
      ]);

    // Calculate quick review summary
    const calculateQuickReview = () => {
      if (!allReviews || allReviews.length === 0) {
        return {
          average_rating: 0,
          total_reviews: 0,
          rating_distribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        };
      }

      const totalReviews = allReviews.length;
      const ratingSum = allReviews.reduce(
        (sum, review) => sum + review.rating,
        0
      );
      const averageRating = ratingSum / totalReviews;

      const ratingDistribution = allReviews.reduce(
        (acc, review) => {
          acc[review.rating] = (acc[review.rating] || 0) + 1;
          return acc;
        },
        { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 } as Record<number, number>
      );

      return {
        average_rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
        total_reviews: totalReviews,
        rating_distribution: ratingDistribution,
      };
    };

    const quickReview = calculateQuickReview();

    const response: ToolResponse = {
      id: tool.id,
      name: tool.name,
      slug: tool.slug,
      website_url: tool.website_url,
      logo_url: tool.logo_url,
      screenshot_urls: tool.screenshot_urls,
      tool_type: tool.tool_type as ToolType,
      pricing_models: tool.pricing_models as PricingModel[],
      is_available: tool.is_available,
      added_date: tool.added_date,
      status: tool.status as ToolStatus,
      is_published: tool.is_published,
      category_ids: tool.category_ids,
      tag_ids: tool.tag_ids,
      twitter_url: tool.twitter_url,
      facebook_url: tool.facebook_url,
      linkedin_url: tool.linkedin_url,
      github_url: tool.github_url,
      contact_email: tool.contact_email,
      support_email: tool.support_email,
      translations: tool.translations.map((t) => ({
        id: t.id,
        tool_id: t.tool_id,
        language_code: t.language_code,
        name: t.name,
        short_description: t.short_description,
        introduction: t.introduction,
        what_is_it: t.what_is_it,
        how_to_use: t.how_to_use,
        full_feature: t.full_feature,
        short_feature: t.short_feature,
        pricing: t.pricing,
        meta_title: t.meta_title,
        meta_description: t.meta_description,
      })),
      faqs: tool.faqs?.map((faq) => ({
        id: faq.id,
        tool_id: faq.tool_id,
        order: faq.order,
        is_active: faq.is_active,
        translations: faq.translations.map((ft) => ({
          id: ft.id,
          faq_id: ft.faq_id,
          language_code: ft.language_code,
          question: ft.question,
          answer: ft.answer,
        })),
      })),
      categories: categories.map((cat) => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
        super_category: cat.super_category as SuperCategory,
        icon_url: cat.icon_url,
        color: cat.color,
        is_active: cat.is_active,
        translations: cat.translations.map((ct) => ({
          id: ct.id,
          category_id: ct.category_id,
          language_code: ct.language_code,
          name: ct.name,
          description: ct.description,
        })),
      })),
      tags: tags.map((tag) => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
        color: tag.color,
        is_active: tag.is_active,
        translations: tag.translations.map((tt) => ({
          id: tt.id,
          tag_id: tt.tag_id,
          language_code: tt.language_code,
          name: tt.name,
          description: tt.description,
        })),
      })),
      reviews: {
        items: reviews.map((review) => ({
          id: review.id,
          tool_id: review.tool_id,
          user_id: review.user_id,
          rating: review.rating,
          content: review.content,
          is_published: review.is_published,
          helpful_votes: review.helpful_votes,
          unhelpful_votes: review.unhelpful_votes,
          created_at: review.created_at,
          updated_at: review.updated_at,
          user: review.user
            ? {
                id: review.user.id,
                name: review.user.name,
                email: null, // Don't expose email in public API
                image: review.user.image,
              }
            : undefined,
          user_vote:
            review.votes && review.votes.length > 0
              ? (review.votes[0].vote_type as any)
              : undefined,
        })),
        pagination: {
          page: reviewPage,
          limit: reviewLimit,
          total: reviewTotal,
          total_pages: Math.ceil(reviewTotal / reviewLimit),
          has_next: reviewPage < Math.ceil(reviewTotal / reviewLimit),
          has_prev: reviewPage > 1,
        },
        quick_review: quickReview,
      },
      saved_count: savedCount,
      is_saved: !!userSave,
    };

    return successResponse(response, "Tool retrieved successfully");
  } catch (error) {
    console.error("GET /api/v1/tool/[slug] error:", error);
    return serverErrorResponse("Failed to retrieve tool");
  }
}
