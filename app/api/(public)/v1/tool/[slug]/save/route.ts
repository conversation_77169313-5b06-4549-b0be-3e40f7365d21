import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  successResponse,
  notFoundResponse,
  serverErrorResponse,
  unauthorizedResponse,
} from "@/lib/api-responses";
import { authenticateApiRequest } from "@/lib/api-auth";

// POST /api/v1/tool/[slug]/save - Save/unsave tool (requires authentication)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Authenticate user
    const authResult = await authenticateApiRequest();
    if (!authResult) {
      return unauthorizedResponse("Authentication required");
    }

    // Find tool by slug
    const tool = await prisma.tool.findUnique({
      where: {
        slug,
        is_published: true,
        is_available: true,
      },
    });

    if (!tool) {
      return notFoundResponse("Tool not found");
    }

    // Check if user has already saved this tool
    const existingSave = await prisma.toolSave.findUnique({
      where: {
        tool_id_user_id: {
          tool_id: tool.id,
          user_id: authResult.user.id,
        },
      },
    });

    let action: "saved" | "unsaved";
    let savedCount: number;

    if (existingSave) {
      // Remove save (unsave)
      await prisma.toolSave.delete({
        where: {
          id: existingSave.id,
        },
      });
      action = "unsaved";
    } else {
      // Create save
      await prisma.toolSave.create({
        data: {
          tool_id: tool.id,
          user_id: authResult.user.id,
        },
      });
      action = "saved";
    }

    // Get updated saved count
    savedCount = await prisma.toolSave.count({
      where: {
        tool_id: tool.id,
      },
    });

    return successResponse(
      {
        action,
        saved_count: savedCount,
        is_saved: action === "saved",
      },
      `Tool ${action} successfully`
    );
  } catch (error) {
    console.error("POST /api/v1/tool/[slug]/save error:", error);
    return serverErrorResponse("Failed to save/unsave tool");
  }
} 