import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  successResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { ToolType, PricingModel, ToolStatus, SuperCategory } from "@/types/tool-api";

// Tool listing response type for public API
export interface ToolListingItem {
  id: string;
  name: string;
  slug: string;
  logo_url: string | null;
  pricing_models: PricingModel[];
  short_description: string | null;
  website_url: string | null;
  super_categories: SuperCategory[];
  is_direct: boolean;
}

export interface ToolListingResponse {
  tools: ToolListingItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// GET /api/v1/tool - Get all published tools with pagination (public API)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = 30; // Fixed limit as per requirements
    const offset = (page - 1) * limit;
    
    // Language parameter for translations
    const language = searchParams.get("lang") || "en";

    // Get total count of published tools
    const total = await prisma.tool.count({
      where: {
        is_published: true,
        is_available: true,
        status: ToolStatus.PUBLISHED,
      },
    });

    // Get tools with translations and categories
    const tools = await prisma.tool.findMany({
      where: {
        is_published: true,
        is_available: true,
        status: ToolStatus.PUBLISHED,
      },
      select: {
        id: true,
        name: true,
        slug: true,
        logo_url: true,
        pricing_models: true,
        website_url: true,
        category_ids: true,
        translations: {
          where: { language_code: language },
          select: {
            short_description: true,
          },
        },
      },
      orderBy: {
        added_date: "desc",
      },
      skip: offset,
      take: limit,
    });

    // Get all categories to extract super categories
    const allCategoryIds = [...new Set(tools.flatMap(tool => tool.category_ids))];
    const categories = await prisma.toolCategory.findMany({
      where: {
        id: { in: allCategoryIds },
        is_active: true,
      },
      select: {
        id: true,
        super_category: true,
      },
    });

    // Create category lookup map
    const categoryMap = new Map(categories.map(cat => [cat.id, cat.super_category]));

    // Format tools with translation data
    const formattedTools: ToolListingItem[] = tools.map(tool => {
      const translation = tool.translations?.[0];
      
      // Get unique super categories for this tool
      const toolSuperCategories = [...new Set(
        tool.category_ids
          .map(catId => categoryMap.get(catId))
          .filter(Boolean)
      )] as SuperCategory[];
      
      return {
        id: tool.id,
        name: tool.name,
        slug: tool.slug,
        logo_url: tool.logo_url,
        pricing_models: tool.pricing_models as PricingModel[],
        short_description: translation?.short_description || null,
        website_url: tool.website_url,
        super_categories: toolSuperCategories,
        is_direct: false, // Default to false as requested
      };
    });

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const response: ToolListingResponse = {
      tools: formattedTools,
      pagination: {
        page,
        limit,
        total,
        total_pages: totalPages,
        has_next: hasNext,
        has_prev: hasPrev,
      },
    };

    return successResponse(response, "Tools retrieved successfully");
  } catch (error) {
    console.error("GET /api/v1/tool error:", error);
    return serverErrorResponse("Failed to retrieve tools");
  }
}
