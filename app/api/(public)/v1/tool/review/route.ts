import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { authenticateApiRequest } from "@/lib/api-auth";
import { 
  ReviewResponse, 
  CreateReviewRequest, 
  PublicReviewQueryParams,
  PaginatedResponse
} from "@/types/tool-api";

// GET - List published reviews (Public)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const tool_id = searchParams.get("tool_id");
    const rating = searchParams.get("rating")
      ? parseInt(searchParams.get("rating")!)
      : undefined;

    const skip = (page - 1) * limit;

    // Build where clause for filtering
    const where: any = {
      is_published: true, // Only show published reviews
    };

    if (tool_id) {
      where.tool_id = tool_id;
    }

    if (rating !== undefined) {
      where.rating = rating;
    }

    // Check if user is authenticated to get their vote information
    const authResult = await authenticateApiRequest();

    // Get reviews with pagination
    const [reviews, total] = await Promise.all([
      prisma.toolReview.findMany({
        where,
        skip,
        take: limit,
        orderBy: { created_at: "desc" },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          tool: {
            select: {
              id: true,
              name: true,
              slug: true,
              logo_url: true,
            },
          },
          votes: authResult
            ? {
                where: {
                  user_id: authResult.user.id,
                },
                select: {
                  vote_type: true,
                },
              }
            : false,
        },
      }),
      prisma.toolReview.count({ where }),
    ]);

    const response: PaginatedResponse<ReviewResponse> = {
      items: reviews.map((review) => ({
        id: review.id,
        tool_id: review.tool_id,
        user_id: review.user_id,
        rating: review.rating,
        content: review.content,
        is_published: review.is_published,
        helpful_votes: review.helpful_votes,
        unhelpful_votes: review.unhelpful_votes,
        created_at: review.created_at,
        updated_at: review.updated_at,
        user: review.user
          ? {
              id: review.user.id,
              name: review.user.name,
              email: null, // Don't expose email in public API
              image: review.user.image,
            }
          : undefined,
        tool: review.tool
          ? {
              id: review.tool.id,
              name: review.tool.name,
              slug: review.tool.slug,
              logo_url: review.tool.logo_url,
            }
          : undefined,
        user_vote:
          review.votes && review.votes.length > 0
            ? (review.votes[0].vote_type as any)
            : undefined,
      })),
      pagination: {
        page,
        limit,
        total,
        total_pages: Math.ceil(total / limit),
        has_next: page < Math.ceil(total / limit),
        has_prev: page > 1,
      },
    };

    return NextResponse.json({
      success: true,
      data: response,
    });
  } catch (error) {
    console.error("Error fetching public reviews:", error);
    return NextResponse.json(
      { error: "Failed to fetch reviews" },
      { status: 500 }
    );
  }
}

// POST - Create review (Authenticated users only)
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateApiRequest();
    
    if (!authResult) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const createData = body as CreateReviewRequest;

    // Validation
    if (!createData.tool_id || !createData.rating) {
      return NextResponse.json(
        { error: "Tool ID and rating are required" },
        { status: 400 }
      );
    }

    if (createData.rating < 1 || createData.rating > 5) {
      return NextResponse.json(
        { error: "Rating must be between 1 and 5" },
        { status: 400 }
      );
    }

    // Check if tool exists
    const tool = await prisma.tool.findUnique({
      where: { id: createData.tool_id },
      select: { id: true }
    });

    if (!tool) {
      return NextResponse.json(
        { error: "Tool not found" },
        { status: 404 }
      );
    }

    // Check if user already reviewed this tool
    const existingReview = await prisma.toolReview.findUnique({
      where: {
        tool_id_user_id: {
          tool_id: createData.tool_id,
          user_id: authResult.user.id
        }
      }
    });

    if (existingReview) {
      return NextResponse.json(
        { error: "You have already reviewed this tool" },
        { status: 400 }
      );
    }

    // Create review
    const review = await prisma.toolReview.create({
      data: {
        tool_id: createData.tool_id,
        user_id: authResult.user.id,
        rating: createData.rating,
        content: createData.content,
        is_published: false // Reviews need admin approval
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        tool: {
          select: {
            id: true,
            name: true,
            slug: true,
            logo_url: true
          }
        }
      }
    });

    const response: ReviewResponse = {
      id: review.id,
      tool_id: review.tool_id,
      user_id: review.user_id,
      rating: review.rating,
      content: review.content,
      is_published: review.is_published,
      helpful_votes: review.helpful_votes,
      unhelpful_votes: review.unhelpful_votes,
      created_at: review.created_at,
      updated_at: review.updated_at,
      user: review.user ? {
        id: review.user.id,
        name: review.user.name,
        email: review.user.email,
        image: review.user.image,
      } : undefined,
      tool: review.tool ? {
        id: review.tool.id,
        name: review.tool.name,
        slug: review.tool.slug,
        logo_url: review.tool.logo_url,
      } : undefined
    };

    return NextResponse.json({
      success: true,
      message: "Review submitted successfully. It will be published after admin approval.",
      data: response
    });

  } catch (error) {
    console.error("Error creating review:", error);
    return NextResponse.json(
      { error: "Failed to create review" },
      { status: 500 }
    );
  }
} 