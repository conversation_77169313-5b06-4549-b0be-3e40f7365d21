import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { authenticateApiRequest } from "@/lib/api-auth";
import { 
  CreateReviewVoteRequest, 
  ReviewVoteResponse, 
  VoteType,
  ReviewVoteStatsResponse
} from "@/types/tool-api";

// POST - Vote on a review (Authenticated users only)
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateApiRequest();
    
    if (!authResult) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const voteData = body as CreateReviewVoteRequest;

    // Validation
    if (!voteData.review_id || !voteData.vote_type) {
      return NextResponse.json(
        { error: "Review ID and vote type are required" },
        { status: 400 }
      );
    }

    if (!Object.values(VoteType).includes(voteData.vote_type)) {
      return NextResponse.json(
        { error: "Invalid vote type" },
        { status: 400 }
      );
    }

    // Check if review exists and is published
    const review = await prisma.toolReview.findUnique({
      where: { id: voteData.review_id },
      select: { id: true, is_published: true, user_id: true }
    });

    if (!review) {
      return NextResponse.json(
        { error: "Review not found" },
        { status: 404 }
      );
    }

    if (!review.is_published) {
      return NextResponse.json(
        { error: "Cannot vote on unpublished review" },
        { status: 400 }
      );
    }

    // Prevent self-voting
    if (review.user_id === authResult.user.id) {
      return NextResponse.json(
        { error: "Cannot vote on your own review" },
        { status: 400 }
      );
    }

    // Check if user has already voted on this review
    const existingVote = await prisma.reviewVote.findUnique({
      where: {
        review_id_user_id: {
          review_id: voteData.review_id,
          user_id: authResult.user.id
        }
      }
    });

    let vote;
    
    if (existingVote) {
      // If same vote type, remove the vote (toggle off)
      if (existingVote.vote_type === voteData.vote_type) {
        await prisma.reviewVote.delete({
          where: { id: existingVote.id }
        });
        
        // Update review vote counts
        const updateData = voteData.vote_type === VoteType.HELPFUL
          ? { helpful_votes: { decrement: 1 } }
          : { unhelpful_votes: { decrement: 1 } };
        
        await prisma.toolReview.update({
          where: { id: voteData.review_id },
          data: updateData
        });

        // Get updated vote counts after removal
        const updatedReview = await prisma.toolReview.findUnique({
          where: { id: voteData.review_id },
          select: {
            helpful_votes: true,
            unhelpful_votes: true
          }
        });

        const response: ReviewVoteStatsResponse = {
          review_id: voteData.review_id,
          helpful_votes: updatedReview?.helpful_votes || 0,
          unhelpful_votes: updatedReview?.unhelpful_votes || 0,
          user_vote: undefined // Vote was removed
        };

        return NextResponse.json({
          success: true,
          message: "Vote removed successfully",
          data: response
        });
      } else {
        // Update existing vote to new type
        vote = await prisma.reviewVote.update({
          where: { id: existingVote.id },
          data: { vote_type: voteData.vote_type }
        });

        // Update review vote counts - decrement old type, increment new type
        const oldType = existingVote.vote_type;
        const newType = voteData.vote_type;
        
        await prisma.toolReview.update({
          where: { id: voteData.review_id },
          data: {
            helpful_votes: {
              [oldType === VoteType.HELPFUL ? 'decrement' : 'increment']: 1
            },
            unhelpful_votes: {
              [oldType === VoteType.UNHELPFUL ? 'decrement' : 'increment']: 1
            }
          }
        });
      }
    } else {
      // Create new vote
      vote = await prisma.reviewVote.create({
        data: {
          review_id: voteData.review_id,
          user_id: authResult.user.id,
          vote_type: voteData.vote_type
        }
      });

      // Update review vote counts
      const updateData = voteData.vote_type === VoteType.HELPFUL
        ? { helpful_votes: { increment: 1 } }
        : { unhelpful_votes: { increment: 1 } };
      
      await prisma.toolReview.update({
        where: { id: voteData.review_id },
        data: updateData
      });
    }

    // Get updated vote counts
    const updatedReview = await prisma.toolReview.findUnique({
      where: { id: voteData.review_id },
      select: {
        helpful_votes: true,
        unhelpful_votes: true
      }
    });

    const response: ReviewVoteStatsResponse = {
      review_id: voteData.review_id,
      helpful_votes: updatedReview?.helpful_votes || 0,
      unhelpful_votes: updatedReview?.unhelpful_votes || 0,
      user_vote: vote ? (vote.vote_type as any) : undefined
    };

    return NextResponse.json({
      success: true,
      message: "Vote recorded successfully",
      data: response
    });

  } catch (error) {
    console.error("Error processing vote:", error);
    return NextResponse.json(
      { error: "Failed to process vote" },
      { status: 500 }
    );
  }
}

// GET - Get vote statistics for a review
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const review_id = searchParams.get("review_id");

    if (!review_id) {
      return NextResponse.json(
        { error: "Review ID is required" },
        { status: 400 }
      );
    }

    // Get review vote counts
    const review = await prisma.toolReview.findUnique({
      where: { id: review_id },
      select: {
        helpful_votes: true,
        unhelpful_votes: true,
        is_published: true
      }
    });

    if (!review) {
      return NextResponse.json(
        { error: "Review not found" },
        { status: 404 }
      );
    }

    if (!review.is_published) {
      return NextResponse.json(
        { error: "Review not published" },
        { status: 400 }
      );
    }

    let user_vote;
    
    // Check if user is authenticated and has voted
    const authResult = await authenticateApiRequest();
    if (authResult) {
      const vote = await prisma.reviewVote.findUnique({
        where: {
          review_id_user_id: {
            review_id: review_id,
            user_id: authResult.user.id
          }
        }
      });
      user_vote = vote?.vote_type as any;
    }

    const response: ReviewVoteStatsResponse = {
      review_id: review_id,
      helpful_votes: review.helpful_votes,
      unhelpful_votes: review.unhelpful_votes,
      user_vote
    };

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error("Error fetching vote stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch vote statistics" },
      { status: 500 }
    );
  }
} 