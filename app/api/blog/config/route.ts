import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdminOrAuthorAuth } from "@/lib/api-auth";
import { successResponse, serverErrorResponse } from "@/lib/api-responses";
import {
  BlogConfigResponse,
  BlogStatus,
  BlogCommentStatus,
} from "@/types/blog-api";

// GET /api/blog/config - Get blog configuration data (categories, tags, authors, enums, stats)
export async function GET(request: NextRequest) {
  try {
    // Check admin/author authentication
    await requireAdminOrAuthorAuth();

    // Get categories and tags for form dropdowns
    const categories = await prisma.blogCategory.findMany({
      include: {
        parent: true,
        children: {
          orderBy: {
            name: "asc",
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Sort categories hierarchically: parents first, then their children
    const sortedCategories = [];

    // First, add all parent categories (those without parent_id)
    const parentCategories = categories
      .filter((cat) => !cat.parent_id)
      .sort((a, b) => a.name.localeCompare(b.name));

    for (const parent of parentCategories) {
      sortedCategories.push(parent);
      // Then add all children of this parent, sorted alphabetically
      const children = categories
        .filter((cat) => cat.parent_id === parent.id)
        .sort((a, b) => a.name.localeCompare(b.name));
      sortedCategories.push(...children);
    }

    const tags = await prisma.blogTag.findMany({
      orderBy: {
        name: "asc",
      },
    });

    // Get authors (users with ADMIN or AUTHOR role)
    const authors = await prisma.user.findMany({
      where: {
        role: {
          in: ["ADMIN", "AUTHOR"],
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        role: true,
      },
      orderBy: [
        { role: "asc" }, // ADMIN first, then AUTHOR
        { name: "asc" },
      ],
    });

    // Get blog statistics
    const stats = await Promise.all([
      prisma.blogCategory.count(),
      prisma.blogTag.count(),
      prisma.blogPost.count(),
      prisma.blogComment.count(),
    ]);

    const [totalCategories, totalTags, totalPosts, totalComments] = stats;

    const response: BlogConfigResponse = {
      categories: sortedCategories.map((category) => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description,
        parent_id: category.parent_id,
        created_at: category.created_at,
        updated_at: category.updated_at,
        parent: category.parent
          ? {
              id: category.parent.id,
              name: category.parent.name,
              slug: category.parent.slug,
              description: null,
              parent_id: null,
              created_at: category.created_at,
              updated_at: category.updated_at,
            }
          : undefined,
        children: category.children.map((child) => ({
          id: child.id,
          name: child.name,
          slug: child.slug,
          description: null,
          parent_id: category.id,
          created_at: category.created_at,
          updated_at: category.updated_at,
        })),
      })),
      tags: tags.map((tag) => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
        description: tag.description,
        created_at: tag.created_at,
        updated_at: tag.updated_at,
      })),
      authors: authors.map((author) => ({
        id: author.id,
        name: author.name,
        email: author.email,
        image: author.image,
        role: author.role as "ADMIN" | "AUTHOR" | "USER",
      })),
      enums: {
        blogStatuses: Object.values(BlogStatus),
        commentStatuses: Object.values(BlogCommentStatus),
      },
      stats: {
        totalCategories,
        totalTags,
        totalPosts,
        totalComments,
      },
    };

    return successResponse(
      response,
      "Blog configuration retrieved successfully"
    );
  } catch (error) {
    console.error("GET /api/blog/config error:", error);
    return serverErrorResponse("Failed to retrieve blog configuration");
  }
}
