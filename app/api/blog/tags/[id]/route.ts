import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { authenticateApiRequest } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
  forbiddenResponse,
  validationErrorResponse,
  serverErrorResponse,
  notFoundResponse,
} from "@/lib/api-responses";
import { generateSlug, validateSlug, ensureUniqueSlug } from "@/lib/slug-utils";
import type { UpdateBlogTagRequest, BlogTagResponse } from "@/types/blog-api";

// GET /api/blog/tags/[id] - Get tag by ID (public endpoint)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const tagId = parseInt(id);

    if (isNaN(tagId)) {
      return validationErrorResponse("Invalid tag ID");
    }

    const tag = await prisma.blogTag.findUnique({
      where: { id: tagId },
    });

    if (!tag) {
      return notFoundResponse("Blog tag not found");
    }

    const response: BlogTagResponse = {
      id: tag.id,
      name: tag.name,
      slug: tag.slug,
      description: tag.description,
      created_at: tag.created_at,
      updated_at: tag.updated_at,
    };

    return successResponse(response, "Blog tag retrieved successfully");
  } catch (error) {
    console.error("GET /api/blog/tags/[id] error:", error);
    return serverErrorResponse("Failed to retrieve blog tag");
  }
}

// PUT /api/blog/tags/[id] - Update tag (admin/author only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication first
    const authResult = await authenticateApiRequest();

    if (!authResult) {
      return unauthorizedResponse("Authentication required");
    }

    // Check if user is admin or author
    if (authResult.user.role !== "ADMIN" && authResult.user.role !== "AUTHOR") {
      return forbiddenResponse("Only admins and authors can update blog tags");
    }

    const { id } = await params;
    const tagId = parseInt(id);

    if (isNaN(tagId)) {
      return validationErrorResponse("Invalid tag ID");
    }

    // Check if tag exists
    const existingTag = await prisma.blogTag.findUnique({
      where: { id: tagId },
    });

    if (!existingTag) {
      return notFoundResponse("Blog tag not found");
    }

    const body: UpdateBlogTagRequest = await request.json();
    const { name, description, slug } = body;

    // Validation
    if (name !== undefined) {
      if (!name || name.trim().length === 0) {
        return validationErrorResponse("Tag name cannot be empty");
      }

      if (name.length > 50) {
        return validationErrorResponse(
          "Tag name must be 50 characters or less"
        );
      }
    }

    if (description !== undefined && description && description.length > 500) {
      return validationErrorResponse(
        "Tag description must be 500 characters or less"
      );
    }

    // Handle slug validation if provided
    let finalSlug = existingTag.slug;
    if (slug !== undefined) {
      if (slug && slug !== existingTag.slug) {
        if (!validateSlug(slug)) {
          return validationErrorResponse(
            "Invalid slug format. Use only lowercase letters, numbers, and hyphens."
          );
        }

        // Check if new slug already exists
        const existingTags = await prisma.blogTag.findMany({
          where: { id: { not: tagId } },
          select: { slug: true },
        });

        const existingSlugs = existingTags.map((t: any) => t.slug);

        if (existingSlugs.includes(slug)) {
          finalSlug = ensureUniqueSlug(slug, existingSlugs);
        } else {
          finalSlug = slug;
        }
      } else if (!slug) {
        // Generate new slug from name if slug is empty
        const newSlug = generateSlug(name || existingTag.name);
        const existingTags = await prisma.blogTag.findMany({
          where: { id: { not: tagId } },
          select: { slug: true },
        });
        const existingSlugs = existingTags.map((t: any) => t.slug);
        finalSlug = ensureUniqueSlug(newSlug, existingSlugs);
      }
    }

    // Update tag
    const updatedTag = await prisma.blogTag.update({
      where: { id: tagId },
      data: {
        ...(name !== undefined && { name: name.trim() }),
        ...(description !== undefined && {
          description: description?.trim() || null,
        }),
        ...(finalSlug !== existingTag.slug && { slug: finalSlug }),
      },
    });

    const response: BlogTagResponse = {
      id: updatedTag.id,
      name: updatedTag.name,
      slug: updatedTag.slug,
      description: updatedTag.description,
      created_at: updatedTag.created_at,
      updated_at: updatedTag.updated_at,
    };

    return successResponse(response, "Blog tag updated successfully");
  } catch (error) {
    console.error("PUT /api/blog/tags/[id] error:", error);
    return serverErrorResponse("Failed to update blog tag");
  }
}

// DELETE /api/blog/tags/[id] - Delete tag (admin/author only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication first
    const authResult = await authenticateApiRequest();

    if (!authResult) {
      return unauthorizedResponse("Authentication required");
    }

    // Check if user is admin or author
    if (authResult.user.role !== "ADMIN" && authResult.user.role !== "AUTHOR") {
      return forbiddenResponse("Only admins and authors can delete blog tags");
    }

    const { id } = await params;
    const tagId = parseInt(id);

    if (isNaN(tagId)) {
      return validationErrorResponse("Invalid tag ID");
    }

    // Check if tag exists
    const existingTag = await prisma.blogTag.findUnique({
      where: { id: tagId },
    });

    if (!existingTag) {
      return notFoundResponse("Blog tag not found");
    }

    // Check if tag is used by any blog posts
    const postsUsingTag = await prisma.blogPost.findFirst({
      where: {
        tag_ids: {
          has: tagId,
        },
      },
    });

    if (postsUsingTag) {
      return validationErrorResponse(
        "Cannot delete tag that is being used by blog posts"
      );
    }

    // Delete tag
    await prisma.blogTag.delete({
      where: { id: tagId },
    });

    return successResponse({ id: tagId }, "Blog tag deleted successfully");
  } catch (error) {
    console.error("DELETE /api/blog/tags/[id] error:", error);
    return serverErrorResponse("Failed to delete blog tag");
  }
}
