import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { authenticateApiRequest } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
  forbiddenResponse,
  validationErrorResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { generateSlug, validateSlug, ensureUniqueSlug } from "@/lib/slug-utils";
import type {
  CreateBlogTagRequest,
  BlogTagResponse,
  ListQueryParams,
  PaginatedResponse,
} from "@/types/blog-api";

// GET /api/blog/tags - List tags (public endpoint)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(parseInt(searchParams.get("limit") || "20"), 100);
    const search = searchParams.get("search") || "";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { slug: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    // Get total count
    const total = await prisma.blogTag.count({ where });

    // Get tags
    const tags = await prisma.blogTag.findMany({
      where,
      orderBy: {
        created_at: "desc",
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    const response: PaginatedResponse<BlogTagResponse> = {
      items: tags.map((tag: any) => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
        description: tag.description,
        created_at: tag.created_at,
        updated_at: tag.updated_at,
      })),
      pagination: {
        page,
        limit,
        total,
        total_pages: totalPages,
        has_next: page < totalPages,
        has_prev: page > 1,
      },
    };

    return successResponse(response, "Blog tags retrieved successfully");
  } catch (error) {
    console.error("GET /api/blog/tags error:", error);
    return serverErrorResponse("Failed to retrieve blog tags");
  }
}

// POST /api/blog/tags - Create tag (admin/author only)
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication first
    const authResult = await authenticateApiRequest();

    if (!authResult) {
      return unauthorizedResponse("Authentication required");
    }

    // Check if user is admin or author
    if (authResult.user.role !== "ADMIN" && authResult.user.role !== "AUTHOR") {
      return forbiddenResponse("Only admins and authors can create blog tags");
    }

    const body: CreateBlogTagRequest = await request.json();
    const { name, description, slug } = body;

    // Validation
    if (!name || name.trim().length === 0) {
      return validationErrorResponse("Tag name is required");
    }

    if (name.length > 50) {
      return validationErrorResponse("Tag name must be 50 characters or less");
    }

    if (description && description.length > 500) {
      return validationErrorResponse(
        "Tag description must be 500 characters or less"
      );
    }

    // Generate or validate slug
    let finalSlug = slug || generateSlug(name);

    if (slug && !validateSlug(slug)) {
      return validationErrorResponse(
        "Invalid slug format. Use only lowercase letters, numbers, and hyphens."
      );
    }

    // Check if slug already exists
    const existingTags = await prisma.blogTag.findMany({
      select: { slug: true },
    });

    const existingSlugs = existingTags.map((t: any) => t.slug);
    finalSlug = ensureUniqueSlug(finalSlug, existingSlugs);

    // Create tag
    const tag = await prisma.blogTag.create({
      data: {
        name: name.trim(),
        slug: finalSlug,
        description: description?.trim() || null,
      },
    });

    const response: BlogTagResponse = {
      id: tag.id,
      name: tag.name,
      slug: tag.slug,
      description: tag.description,
      created_at: tag.created_at,
      updated_at: tag.updated_at,
    };

    return successResponse(response, "Blog tag created successfully", 201);
  } catch (error) {
    console.error("POST /api/blog/tags error:", error);
    return serverErrorResponse("Failed to create blog tag");
  }
}
