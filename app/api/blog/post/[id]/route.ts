import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdminOr<PERSON>uthorAuth } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  validationErrorResponse,
  serverErrorResponse,
  notFoundResponse,
} from "@/lib/api-responses";
import { generateSlug, validateSlug, ensureUniqueSlug } from "@/lib/slug-utils";
import {
  UpdateBlogPostRequest,
  BlogPostResponse,
  BlogStatus,
} from "@/types/blog-api";

// GET /api/blog/post/[id] - Get single blog post
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const post = await prisma.blogPost.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        comments: {
          where: {
            status: "APPROVED",
            is_approved: true,
            parent_id: null, // Only top-level comments
          },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
            replies: {
              where: {
                status: "APPROVED",
                is_approved: true,
              },
              include: {
                author: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    image: true,
                  },
                },
              },
              orderBy: {
                created_at: "asc",
              },
            },
          },
          orderBy: {
            created_at: "desc",
          },
        },
      },
    });

    if (!post) {
      return notFoundResponse("Blog post not found");
    }

    // Get categories and tags
    const [categories, tags] = await Promise.all([
      post.category_ids.length > 0
        ? prisma.blogCategory.findMany({
            where: { id: { in: post.category_ids } },
          })
        : [],
      post.tag_ids.length > 0
        ? prisma.blogTag.findMany({
            where: { id: { in: post.tag_ids } },
          })
        : [],
    ]);

    const response: BlogPostResponse = {
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      status: post.status as BlogStatus,
      meta_title: post.meta_title,
      meta_description: post.meta_description,
      featured_image_url: post.featured_image_url,
      author_id: post.author_id,
      category_ids: post.category_ids,
      tag_ids: post.tag_ids,
      published_at: post.published_at,
      created_at: post.created_at,
      updated_at: post.updated_at,
      author: post.author,
      categories: categories.map((cat) => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
        description: cat.description,
        parent_id: cat.parent_id,
        created_at: cat.created_at,
        updated_at: cat.updated_at,
      })),
      tags: tags.map((tag) => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
        description: tag.description,
        created_at: tag.created_at,
        updated_at: tag.updated_at,
      })),
    };

    return successResponse(response, "Blog post retrieved successfully");
  } catch (error) {
    console.error("GET /api/blog/post/[id] error:", error);
    return serverErrorResponse("Failed to retrieve blog post");
  }
}

// PUT /api/blog/post/[id] - Update blog post
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAdminOrAuthorAuth();

    const { id } = await params;
    const body: UpdateBlogPostRequest = await request.json();

    // Check if post exists
    const existingPost = await prisma.blogPost.findUnique({
      where: { id },
    });

    if (!existingPost) {
      return notFoundResponse("Blog post not found");
    }

    // Check if user can edit this post (admin or original author)
    if (
      authResult.user.role !== "ADMIN" &&
      existingPost.author_id !== authResult.user.id
    ) {
      return errorResponse("You don't have permission to edit this post", 403);
    }

    // Handle author_id changes
    let finalAuthorId = existingPost.author_id; // Keep existing author by default

    if (body.author_id !== undefined) {
      if (authResult.isAdmin) {
        // Only admins can change the author
        if (body.author_id && body.author_id !== existingPost.author_id) {
          // Verify the new author exists and has appropriate role
          const targetAuthor = await prisma.user.findUnique({
            where: { id: body.author_id },
            select: { id: true, role: true },
          });

          if (!targetAuthor) {
            return validationErrorResponse("Specified author not found");
          }

          if (targetAuthor.role !== "ADMIN" && targetAuthor.role !== "AUTHOR") {
            return validationErrorResponse(
              "Specified user cannot be an author"
            );
          }

          finalAuthorId = body.author_id;
        }
      } else {
        // Non-admin users cannot change the author
        if (body.author_id !== existingPost.author_id) {
          return errorResponse(
            "You don't have permission to change the author",
            403
          );
        }
      }
    }

    // Generate new slug if title is being updated
    let slug = existingPost.slug;
    if (body.title && body.title !== existingPost.title) {
      const newSlug = generateSlug(body.title);

      // Check if new slug conflicts with existing posts (excluding current post)
      const existingPosts = await prisma.blogPost.findMany({
        where: {
          slug: newSlug,
          id: { not: id },
        },
        select: { slug: true },
      });

      if (existingPosts.length > 0) {
        return validationErrorResponse(
          "A blog post with this title already exists"
        );
      }

      slug = newSlug;
    }

    // Validate category_ids if provided
    if (body.category_ids && body.category_ids.length > 0) {
      const categories = await prisma.blogCategory.findMany({
        where: { id: { in: body.category_ids } },
      });
      if (categories.length !== body.category_ids.length) {
        return validationErrorResponse("One or more category IDs are invalid");
      }
    }

    // Validate tag_ids if provided
    if (body.tag_ids && body.tag_ids.length > 0) {
      const tags = await prisma.blogTag.findMany({
        where: { id: { in: body.tag_ids } },
      });
      if (tags.length !== body.tag_ids.length) {
        return validationErrorResponse("One or more tag IDs are invalid");
      }
    }

    // Set published_at if status is being changed to PUBLISHED and no published_at provided
    let finalPublishedAt = body.published_at;
    if (
      body.status === "PUBLISHED" &&
      existingPost.status !== "PUBLISHED" &&
      !body.published_at
    ) {
      finalPublishedAt = new Date();
    }

    // Update blog post
    const post = await prisma.blogPost.update({
      where: { id },
      data: {
        ...(body.title && { title: body.title.trim(), slug }),
        ...(body.excerpt !== undefined && {
          excerpt: body.excerpt?.trim() || null,
        }),
        ...(body.content && { content: body.content.trim() }),
        ...(body.status && { status: body.status }),
        ...(body.meta_title !== undefined && {
          meta_title: body.meta_title?.trim() || null,
        }),
        ...(body.meta_description !== undefined && {
          meta_description: body.meta_description?.trim() || null,
        }),
        ...(body.featured_image_url !== undefined && {
          featured_image_url: body.featured_image_url?.trim() || null,
        }),
        ...(body.category_ids && { category_ids: body.category_ids }),
        ...(body.tag_ids && { tag_ids: body.tag_ids }),
        ...(finalPublishedAt !== undefined && {
          published_at: finalPublishedAt,
        }),
        author_id: finalAuthorId,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    // Get categories and tags for response
    const [categories, tags] = await Promise.all([
      post.category_ids.length > 0
        ? prisma.blogCategory.findMany({
            where: { id: { in: post.category_ids } },
          })
        : [],
      post.tag_ids.length > 0
        ? prisma.blogTag.findMany({
            where: { id: { in: post.tag_ids } },
          })
        : [],
    ]);

    const response: BlogPostResponse = {
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      status: post.status as BlogStatus,
      meta_title: post.meta_title,
      meta_description: post.meta_description,
      featured_image_url: post.featured_image_url,
      author_id: post.author_id,
      category_ids: post.category_ids,
      tag_ids: post.tag_ids,
      published_at: post.published_at,
      created_at: post.created_at,
      updated_at: post.updated_at,
      author: post.author,
      categories: categories.map((cat) => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
        description: cat.description,
        parent_id: cat.parent_id,
        created_at: cat.created_at,
        updated_at: cat.updated_at,
      })),
      tags: tags.map((tag) => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
        description: tag.description,
        created_at: tag.created_at,
        updated_at: tag.updated_at,
      })),
    };

    return successResponse(response, "Blog post updated successfully");
  } catch (error) {
    console.error("PUT /api/blog/post/[id] error:", error);
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return validationErrorResponse(
        "A blog post with this slug already exists"
      );
    }
    return serverErrorResponse("Failed to update blog post");
  }
}

// DELETE /api/blog/post/[id] - Delete blog post
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAdminOrAuthorAuth();

    const { id } = await params;

    // Check if post exists
    const existingPost = await prisma.blogPost.findUnique({
      where: { id },
    });

    if (!existingPost) {
      return notFoundResponse("Blog post not found");
    }

    // Check if user can delete this post (admin or author)
    if (
      authResult.user.role !== "ADMIN" &&
      existingPost.author_id !== authResult.user.id
    ) {
      return errorResponse(
        "You don't have permission to delete this post",
        403
      );
    }

    // Delete blog post (cascade will handle comments)
    await prisma.blogPost.delete({
      where: { id },
    });

    return successResponse(null, "Blog post deleted successfully");
  } catch (error) {
    console.error("DELETE /api/blog/post/[id] error:", error);
    return serverErrorResponse("Failed to delete blog post");
  }
}
