import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdminOr<PERSON>uthor<PERSON>uth } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
  forbiddenResponse,
  validationErrorResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { generateSlug, validateSlug, ensureUniqueSlug } from "@/lib/slug-utils";
import {
  CreateBlogPostRequest,
  BlogPostResponse,
  PaginatedResponse,
  BlogPostQueryParams,
  BlogStatus,
} from "@/types/blog-api";

// GET /api/blog/post - List blog posts with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(parseInt(searchParams.get("limit") || "20"), 100);
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status");
    const authorId = searchParams.get("author_id");
    const categoryId = searchParams.get("category_id");
    const tagId = searchParams.get("tag_id");
    const published = searchParams.get("published");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { slug: { contains: search, mode: "insensitive" } },
        { excerpt: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (authorId) {
      where.author_id = authorId;
    }

    if (categoryId) {
      where.category_ids = { has: parseInt(categoryId) };
    }

    if (tagId) {
      where.tag_ids = { has: parseInt(tagId) };
    }

    if (published === "true") {
      where.status = "PUBLISHED";
      where.published_at = { lte: new Date() };
    } else if (published === "false") {
      where.OR = [
        { status: { not: "PUBLISHED" } },
        { published_at: { gt: new Date() } },
        { published_at: null },
      ];
    }

    // Get total count
    const total = await prisma.blogPost.count({ where });

    // Get blog posts with author, categories, and tags
    const posts = await prisma.blogPost.findMany({
      where,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
      orderBy: {
        created_at: "desc",
      },
      skip,
      take: limit,
    });

    // Get categories and tags for posts
    const allCategoryIds = Array.from(
      new Set(posts.flatMap((post) => post.category_ids))
    );
    const allTagIds = Array.from(
      new Set(posts.flatMap((post) => post.tag_ids))
    );

    const [categories, tags] = await Promise.all([
      allCategoryIds.length > 0
        ? prisma.blogCategory.findMany({
            where: { id: { in: allCategoryIds } },
          })
        : [],
      allTagIds.length > 0
        ? prisma.blogTag.findMany({
            where: { id: { in: allTagIds } },
          })
        : [],
    ]);

    const categoriesMap = new Map(categories.map((cat) => [cat.id, cat]));
    const tagsMap = new Map(tags.map((tag) => [tag.id, tag]));

    const totalPages = Math.ceil(total / limit);

    const response: PaginatedResponse<BlogPostResponse> = {
      items: posts.map((post) => ({
        id: post.id,
        title: post.title,
        slug: post.slug,
        excerpt: post.excerpt,
        content: post.content,
        status: post.status as BlogStatus,
        meta_title: post.meta_title,
        meta_description: post.meta_description,
        featured_image_url: post.featured_image_url,
        author_id: post.author_id,
        category_ids: post.category_ids,
        tag_ids: post.tag_ids,
        published_at: post.published_at,
        created_at: post.created_at,
        updated_at: post.updated_at,
        author: post.author,
        categories: post.category_ids
          .map((id) => categoriesMap.get(id))
          .filter(Boolean)
          .map((cat) => ({
            id: cat!.id,
            name: cat!.name,
            slug: cat!.slug,
            description: cat!.description,
            parent_id: cat!.parent_id,
            created_at: cat!.created_at,
            updated_at: cat!.updated_at,
          })),
        tags: post.tag_ids
          .map((id) => tagsMap.get(id))
          .filter(Boolean)
          .map((tag) => ({
            id: tag!.id,
            name: tag!.name,
            slug: tag!.slug,
            description: tag!.description,
            created_at: tag!.created_at,
            updated_at: tag!.updated_at,
          })),
      })),
      pagination: {
        page,
        limit,
        total,
        total_pages: totalPages,
        has_next: page < totalPages,
        has_prev: page > 1,
      },
    };

    return successResponse(response, "Blog posts retrieved successfully");
  } catch (error) {
    console.error("GET /api/blog/post error:", error);
    return serverErrorResponse("Failed to retrieve blog posts");
  }
}

// POST /api/blog/post - Create new blog post (admin/author only)
export async function POST(request: NextRequest) {
  try {
    // Check admin/author authentication
    const authResult = await requireAdminOrAuthorAuth();

    const body: CreateBlogPostRequest = await request.json();
    const {
      title,
      excerpt,
      content,
      status = "DRAFT",
      meta_title,
      meta_description,
      featured_image_url,
      category_ids = [],
      tag_ids = [],
      published_at,
      author_id,
    } = body;

    // Validation
    if (!title?.trim()) {
      return validationErrorResponse("Title is required");
    }

    if (!content?.trim()) {
      return validationErrorResponse("Content is required");
    }

    // Determine the final author_id based on user role
    let finalAuthorId = authResult.user.id; // Default to current user

    if (authResult.isAdmin && author_id) {
      // Only admins can specify a different author
      // Verify the specified author exists and has appropriate role
      const targetAuthor = await prisma.user.findUnique({
        where: { id: author_id },
        select: { id: true, role: true },
      });

      if (!targetAuthor) {
        return validationErrorResponse("Specified author not found");
      }

      if (targetAuthor.role !== "ADMIN" && targetAuthor.role !== "AUTHOR") {
        return validationErrorResponse("Specified user cannot be an author");
      }

      finalAuthorId = author_id;
    }
    // If user is AUTHOR, they can only create posts for themselves
    // finalAuthorId is already set to authResult.user.id

    // Generate slug from title
    const baseSlug = generateSlug(title.trim());

    // Check for existing posts with same slug
    const existingPosts = await prisma.blogPost.findMany({
      where: { slug: { startsWith: baseSlug } },
      select: { slug: true },
    });

    const slug = ensureUniqueSlug(
      baseSlug,
      existingPosts.map((p) => p.slug)
    );

    // Validate category_ids if provided
    if (category_ids.length > 0) {
      const categories = await prisma.blogCategory.findMany({
        where: { id: { in: category_ids } },
      });
      if (categories.length !== category_ids.length) {
        return validationErrorResponse("One or more category IDs are invalid");
      }
    }

    // Validate tag_ids if provided
    if (tag_ids.length > 0) {
      const tags = await prisma.blogTag.findMany({
        where: { id: { in: tag_ids } },
      });
      if (tags.length !== tag_ids.length) {
        return validationErrorResponse("One or more tag IDs are invalid");
      }
    }

    // Set published_at if status is PUBLISHED and no published_at provided
    let finalPublishedAt = published_at;
    if (status === "PUBLISHED" && !published_at) {
      finalPublishedAt = new Date();
    }

    // Create blog post
    const post = await prisma.blogPost.create({
      data: {
        title: title.trim(),
        slug,
        excerpt: excerpt?.trim() || null,
        content: content.trim(),
        status,
        meta_title: meta_title?.trim() || null,
        meta_description: meta_description?.trim() || null,
        featured_image_url: featured_image_url?.trim() || null,
        author_id: finalAuthorId,
        category_ids,
        tag_ids,
        published_at: finalPublishedAt,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    // Get categories and tags for response
    const [categories, tags] = await Promise.all([
      category_ids.length > 0
        ? prisma.blogCategory.findMany({
            where: { id: { in: category_ids } },
          })
        : [],
      tag_ids.length > 0
        ? prisma.blogTag.findMany({
            where: { id: { in: tag_ids } },
          })
        : [],
    ]);

    const response: BlogPostResponse = {
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      status: post.status as BlogStatus,
      meta_title: post.meta_title,
      meta_description: post.meta_description,
      featured_image_url: post.featured_image_url,
      author_id: post.author_id,
      category_ids: post.category_ids,
      tag_ids: post.tag_ids,
      published_at: post.published_at,
      created_at: post.created_at,
      updated_at: post.updated_at,
      author: post.author,
      categories: categories.map((cat) => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
        description: cat.description,
        parent_id: cat.parent_id,
        created_at: cat.created_at,
        updated_at: cat.updated_at,
      })),
      tags: tags.map((tag) => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
        description: tag.description,
        created_at: tag.created_at,
        updated_at: tag.updated_at,
      })),
    };

    return successResponse(response, "Blog post created successfully");
  } catch (error) {
    console.error("POST /api/blog/post error:", error);
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return validationErrorResponse(
        "A blog post with this slug already exists"
      );
    }
    return serverErrorResponse("Failed to create blog post");
  }
}
