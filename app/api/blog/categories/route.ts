import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { authenticateApiRequest } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
  forbiddenResponse,
  validationErrorResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { generateSlug, validateSlug, ensureUniqueSlug } from "@/lib/slug-utils";
import type {
  CreateBlogCategoryRequest,
  BlogCategoryResponse,
  ListQueryParams,
  PaginatedResponse,
  BlogCategoryQueryParams,
} from "@/types/blog-api";

// GET /api/blog/categories - List categories (public endpoint)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(parseInt(searchParams.get("limit") || "20"), 100);
    const search = searchParams.get("search") || "";
    const parentId = searchParams.get("parent_id");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (parentId) {
      where.parent_id = parseInt(parentId);
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { slug: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    // Get total count
    const total = await prisma.blogCategory.count({ where });

    // Get categories with parent and children
    const categories = await prisma.blogCategory.findMany({
      where,
      include: {
        parent: true,
        children: true,
      },
      orderBy: {
        created_at: "desc",
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    const response: PaginatedResponse<BlogCategoryResponse> = {
      items: categories.map((category: any) => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description,
        parent_id: category.parent_id,
        created_at: category.created_at,
        updated_at: category.updated_at,
        parent: category.parent
          ? {
              id: category.parent.id,
              name: category.parent.name,
              slug: category.parent.slug,
              description: category.parent.description,
              parent_id: category.parent.parent_id,
              created_at: category.parent.created_at,
              updated_at: category.parent.updated_at,
            }
          : null,
        children: category.children.map((child: any) => ({
          id: child.id,
          name: child.name,
          slug: child.slug,
          description: child.description,
          parent_id: child.parent_id,
          created_at: child.created_at,
          updated_at: child.updated_at,
        })),
      })),
      pagination: {
        page,
        limit,
        total,
        total_pages: totalPages,
        has_next: page < totalPages,
        has_prev: page > 1,
      },
    };

    return successResponse(response, "Blog categories retrieved successfully");
  } catch (error) {
    console.error("GET /api/blog/categories error:", error);
    return serverErrorResponse("Failed to retrieve blog categories");
  }
}

// POST /api/blog/categories - Create category (admin/author only)
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication first
    const authResult = await authenticateApiRequest();

    if (!authResult) {
      return unauthorizedResponse("Authentication required");
    }

    // Check if user is admin or author
    if (authResult.user.role !== "ADMIN" && authResult.user.role !== "AUTHOR") {
      return forbiddenResponse(
        "Only admins and authors can create blog categories"
      );
    }

    const body: CreateBlogCategoryRequest = await request.json();
    const { name, description, slug, parent_id } = body;

    // Validation
    if (!name || name.trim().length === 0) {
      return validationErrorResponse("Category name is required");
    }

    if (name.length > 100) {
      return validationErrorResponse(
        "Category name must be 100 characters or less"
      );
    }

    if (description && description.length > 1000) {
      return validationErrorResponse(
        "Category description must be 1000 characters or less"
      );
    }

    // Generate or validate slug
    let finalSlug = slug || generateSlug(name);

    if (slug && !validateSlug(slug)) {
      return validationErrorResponse(
        "Invalid slug format. Use only lowercase letters, numbers, and hyphens."
      );
    }

    // Check if slug already exists
    const existingCategories = await prisma.blogCategory.findMany({
      select: { slug: true },
    });

    const existingSlugs = existingCategories.map((c: any) => c.slug);
    finalSlug = ensureUniqueSlug(finalSlug, existingSlugs);

    // Validate parent category if provided
    if (parent_id) {
      const parentCategory = await prisma.blogCategory.findUnique({
        where: { id: parent_id },
      });

      if (!parentCategory) {
        return validationErrorResponse("Parent category not found");
      }

      // Check for circular reference (parent cannot be a child of current category)
      if (parentCategory.parent_id) {
        const grandParent = await prisma.blogCategory.findUnique({
          where: { id: parentCategory.parent_id },
        });
        if (grandParent) {
          return validationErrorResponse(
            "Cannot create more than 2 levels of category hierarchy"
          );
        }
      }
    }

    // Create category
    const category = await prisma.blogCategory.create({
      data: {
        name: name.trim(),
        slug: finalSlug,
        description: description?.trim() || null,
        parent_id: parent_id || null,
      },
      include: {
        parent: true,
        children: true,
      },
    });

    const response: BlogCategoryResponse = {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      parent_id: category.parent_id,
      created_at: category.created_at,
      updated_at: category.updated_at,
      parent: category.parent
        ? {
            id: category.parent.id,
            name: category.parent.name,
            slug: category.parent.slug,
            description: category.parent.description,
            parent_id: category.parent.parent_id,
            created_at: category.parent.created_at,
            updated_at: category.parent.updated_at,
          }
        : null,
      children: category.children.map((child: any) => ({
        id: child.id,
        name: child.name,
        slug: child.slug,
        description: child.description,
        parent_id: child.parent_id,
        created_at: child.created_at,
        updated_at: child.updated_at,
      })),
    };

    return successResponse(response, "Blog category created successfully", 201);
  } catch (error) {
    console.error("POST /api/blog/categories error:", error);
    return serverErrorResponse("Failed to create blog category");
  }
}
