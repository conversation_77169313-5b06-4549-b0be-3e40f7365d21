import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { authenticateApiRequest } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
  forbiddenResponse,
  validationErrorResponse,
  serverErrorResponse,
  notFoundResponse,
} from "@/lib/api-responses";
import { generateSlug, validateSlug, ensureUniqueSlug } from "@/lib/slug-utils";
import type {
  UpdateBlogCategoryRequest,
  BlogCategoryResponse,
} from "@/types/blog-api";

// GET /api/blog/categories/[id] - Get category by ID (public endpoint)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const categoryId = parseInt(id);

    if (isNaN(categoryId)) {
      return validationErrorResponse("Invalid category ID");
    }

    const category = await prisma.blogCategory.findUnique({
      where: { id: categoryId },
      include: {
        parent: true,
        children: true,
      },
    });

    if (!category) {
      return notFoundResponse("Blog category not found");
    }

    const response: BlogCategoryResponse = {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      parent_id: category.parent_id,
      created_at: category.created_at,
      updated_at: category.updated_at,
      parent: category.parent
        ? {
            id: category.parent.id,
            name: category.parent.name,
            slug: category.parent.slug,
            description: category.parent.description,
            parent_id: category.parent.parent_id,
            created_at: category.parent.created_at,
            updated_at: category.parent.updated_at,
          }
        : null,
      children: category.children.map((child: any) => ({
        id: child.id,
        name: child.name,
        slug: child.slug,
        description: child.description,
        parent_id: child.parent_id,
        created_at: child.created_at,
        updated_at: child.updated_at,
      })),
    };

    return successResponse(response, "Blog category retrieved successfully");
  } catch (error) {
    console.error("GET /api/blog/categories/[id] error:", error);
    return serverErrorResponse("Failed to retrieve blog category");
  }
}

// PUT /api/blog/categories/[id] - Update category (admin/author only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication first
    const authResult = await authenticateApiRequest();

    if (!authResult) {
      return unauthorizedResponse("Authentication required");
    }

    // Check if user is admin or author
    if (authResult.user.role !== "ADMIN" && authResult.user.role !== "AUTHOR") {
      return forbiddenResponse(
        "Only admins and authors can update blog categories"
      );
    }

    const { id } = await params;
    const categoryId = parseInt(id);

    if (isNaN(categoryId)) {
      return validationErrorResponse("Invalid category ID");
    }

    // Check if category exists
    const existingCategory = await prisma.blogCategory.findUnique({
      where: { id: categoryId },
    });

    if (!existingCategory) {
      return notFoundResponse("Blog category not found");
    }

    const body: UpdateBlogCategoryRequest = await request.json();
    const { name, description, slug, parent_id } = body;

    // Validation
    if (name !== undefined) {
      if (!name || name.trim().length === 0) {
        return validationErrorResponse("Category name cannot be empty");
      }

      if (name.length > 100) {
        return validationErrorResponse(
          "Category name must be 100 characters or less"
        );
      }
    }

    if (description !== undefined && description && description.length > 1000) {
      return validationErrorResponse(
        "Category description must be 1000 characters or less"
      );
    }

    // Handle slug validation if provided
    let finalSlug = existingCategory.slug;
    if (slug !== undefined) {
      if (slug && slug !== existingCategory.slug) {
        if (!validateSlug(slug)) {
          return validationErrorResponse(
            "Invalid slug format. Use only lowercase letters, numbers, and hyphens."
          );
        }

        // Check if new slug already exists
        const existingCategories = await prisma.blogCategory.findMany({
          where: { id: { not: categoryId } },
          select: { slug: true },
        });

        const existingSlugs = existingCategories.map((c: any) => c.slug);

        if (existingSlugs.includes(slug)) {
          finalSlug = ensureUniqueSlug(slug, existingSlugs);
        } else {
          finalSlug = slug;
        }
      } else if (!slug) {
        // Generate new slug from name if slug is empty
        const newSlug = generateSlug(name || existingCategory.name);
        const existingCategories = await prisma.blogCategory.findMany({
          where: { id: { not: categoryId } },
          select: { slug: true },
        });
        const existingSlugs = existingCategories.map((c: any) => c.slug);
        finalSlug = ensureUniqueSlug(newSlug, existingSlugs);
      }
    }

    // Validate parent category if provided
    if (parent_id !== undefined && parent_id !== null) {
      if (parent_id === categoryId) {
        return validationErrorResponse("Category cannot be its own parent");
      }

      const parentCategory = await prisma.blogCategory.findUnique({
        where: { id: parent_id },
      });

      if (!parentCategory) {
        return validationErrorResponse("Parent category not found");
      }

      // Check for circular reference
      if (parentCategory.parent_id === categoryId) {
        return validationErrorResponse(
          "Cannot create circular parent-child relationship"
        );
      }

      // Check for deep nesting (max 2 levels)
      if (parentCategory.parent_id) {
        return validationErrorResponse(
          "Cannot create more than 2 levels of category hierarchy"
        );
      }
    }

    // Update category
    const updatedCategory = await prisma.blogCategory.update({
      where: { id: categoryId },
      data: {
        ...(name !== undefined && { name: name.trim() }),
        ...(description !== undefined && {
          description: description?.trim() || null,
        }),
        ...(finalSlug !== existingCategory.slug && { slug: finalSlug }),
        ...(parent_id !== undefined && { parent_id: parent_id || null }),
      },
      include: {
        parent: true,
        children: true,
      },
    });

    const response: BlogCategoryResponse = {
      id: updatedCategory.id,
      name: updatedCategory.name,
      slug: updatedCategory.slug,
      description: updatedCategory.description,
      parent_id: updatedCategory.parent_id,
      created_at: updatedCategory.created_at,
      updated_at: updatedCategory.updated_at,
      parent: updatedCategory.parent
        ? {
            id: updatedCategory.parent.id,
            name: updatedCategory.parent.name,
            slug: updatedCategory.parent.slug,
            description: updatedCategory.parent.description,
            parent_id: updatedCategory.parent.parent_id,
            created_at: updatedCategory.parent.created_at,
            updated_at: updatedCategory.parent.updated_at,
          }
        : null,
      children: updatedCategory.children.map((child: any) => ({
        id: child.id,
        name: child.name,
        slug: child.slug,
        description: child.description,
        parent_id: child.parent_id,
        created_at: child.created_at,
        updated_at: child.updated_at,
      })),
    };

    return successResponse(response, "Blog category updated successfully");
  } catch (error) {
    console.error("PUT /api/blog/categories/[id] error:", error);
    return serverErrorResponse("Failed to update blog category");
  }
}

// DELETE /api/blog/categories/[id] - Delete category (admin/author only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication first
    const authResult = await authenticateApiRequest();

    if (!authResult) {
      return unauthorizedResponse("Authentication required");
    }

    // Check if user is admin or author
    if (authResult.user.role !== "ADMIN" && authResult.user.role !== "AUTHOR") {
      return forbiddenResponse(
        "Only admins and authors can delete blog categories"
      );
    }

    const { id } = await params;
    const categoryId = parseInt(id);

    if (isNaN(categoryId)) {
      return validationErrorResponse("Invalid category ID");
    }

    // Check if category exists
    const existingCategory = await prisma.blogCategory.findUnique({
      where: { id: categoryId },
      include: {
        children: true,
      },
    });

    if (!existingCategory) {
      return notFoundResponse("Blog category not found");
    }

    // Check if category has children
    if (existingCategory.children.length > 0) {
      return validationErrorResponse(
        "Cannot delete category with subcategories. Please delete subcategories first."
      );
    }

    // Check if category is used by any blog posts
    const postsUsingCategory = await prisma.blogPost.findFirst({
      where: {
        category_ids: {
          has: categoryId,
        },
      },
    });

    if (postsUsingCategory) {
      return validationErrorResponse(
        "Cannot delete category that is being used by blog posts"
      );
    }

    // Delete category
    await prisma.blogCategory.delete({
      where: { id: categoryId },
    });

    return successResponse(
      { id: categoryId },
      "Blog category deleted successfully"
    );
  } catch (error) {
    console.error("DELETE /api/blog/categories/[id] error:", error);
    return serverErrorResponse("Failed to delete blog category");
  }
}
