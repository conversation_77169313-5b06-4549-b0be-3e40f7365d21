import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { auth } from "@/auth";
import {
  successResponse,
  unauthorizedResponse,
  forbiddenResponse,
  notFoundResponse,
  serverErrorResponse,
} from "@/lib/api-responses";

// DELETE /api/user/[id]/saved-tools/[toolId] - Remove saved tool
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; toolId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return unauthorizedResponse("Authentication required");
    }

    const { id: userId, toolId } = await params;

    // Users can only remove their own saved tools
    if (session.user.id !== userId) {
      return forbiddenResponse("Access denied");
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true },
    });

    if (!user) {
      return notFoundResponse("User not found");
    }

    // Find the saved tool with tool details
    const savedTool = await prisma.toolSave.findFirst({
      where: {
        tool_id: toolId,
        user_id: userId,
      },
      include: {
        tool: {
          include: {
            translations: {
              where: {
                language_code: "en", // Default to English
              },
            },
          },
        },
      },
    });

    if (!savedTool) {
      return notFoundResponse("Saved tool not found");
    }

    // Get category details for the tool
    const categories = await prisma.toolCategory.findMany({
      where: {
        id: { in: savedTool.tool.category_ids },
        is_active: true,
      },
      select: {
        id: true,
        name: true,
        slug: true,
        super_category: true,
        icon_url: true,
        color: true,
      },
    });

    // Prepare the response data with tool details
    const toolDetails = {
      id: savedTool.tool.id,
      name: savedTool.tool.name,
      slug: savedTool.tool.slug,
      logo_url: savedTool.tool.logo_url,
      tool_type: savedTool.tool.tool_type,
      category_ids: savedTool.tool.category_ids,
      categories: categories.map((cat) => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
        super_category: cat.super_category,
        icon_url: cat.icon_url,
        color: cat.color,
      })),
      // Only include super_category from categories
      super_categories: [
        ...new Set(categories.map((cat) => cat.super_category)),
      ],
      translations: savedTool.tool.translations.map((t) => ({
        id: t.id,
        language_code: t.language_code,
        name: t.name,
        short_description: t.short_description,
      })),
      saved_at: savedTool.created_at,
    };

    // Delete the saved tool
    await prisma.toolSave.delete({
      where: {
        id: savedTool.id,
      },
    });

    return successResponse(
      {
        removed_tool: toolDetails,
        message: "Tool removed from saved list",
      },
      "Saved tool removed successfully"
    );
  } catch (error) {
    console.error("DELETE /api/user/[id]/saved-tools/[toolId] error:", error);
    return serverErrorResponse("Failed to remove saved tool");
  }
}
