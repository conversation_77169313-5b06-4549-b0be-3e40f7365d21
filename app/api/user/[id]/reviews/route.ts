import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { auth } from "@/auth";
import {
  successResponse,
  unauthorizedResponse,
  forbiddenResponse,
  notFoundResponse,
  serverErrorResponse,
} from "@/lib/api-responses";

// GET /api/user/[id]/reviews - Get user's reviews
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return unauthorizedResponse("Authentication required");
    }

    const { id } = await params;
    const { searchParams } = new URL(request.url);
    
    // Users can only access their own reviews, or admins can access any user's reviews
    if (session.user.id !== id && session.user.role !== "ADMIN") {
      return forbiddenResponse("Access denied");
    }

    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(parseInt(searchParams.get("limit") || "20"), 100);
    const search = searchParams.get("search") || "";
    const rating = searchParams.get("rating") ? parseInt(searchParams.get("rating")!) : undefined;
    const status = searchParams.get("status");
    const toolType = searchParams.get("tool_type");
    const language = searchParams.get("language") || "en";

    const skip = (page - 1) * limit;

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id },
      select: { id: true, name: true, image: true },
    });

    if (!user) {
      return notFoundResponse("User not found");
    }

    // Build where clause for review filtering
    const reviewWhere: any = {
      user_id: id,
    };

    if (rating !== undefined) {
      reviewWhere.rating = rating;
    }

    if (status) {
      reviewWhere.is_published = status === "published";
    }

    // Build where clause for tool filtering
    const toolWhere: any = {};

    if (search) {
      toolWhere.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { slug: { contains: search, mode: "insensitive" } },
        {
          translations: {
            some: {
              OR: [
                { name: { contains: search, mode: "insensitive" } },
                {
                  short_description: { contains: search, mode: "insensitive" },
                },
              ],
            },
          },
        },
      ];
    }

    if (toolType) {
      toolWhere.tool_type = toolType;
    }

    // Add tool filter to review where clause
    if (Object.keys(toolWhere).length > 0) {
      reviewWhere.tool = toolWhere;
    }

    // Get total count of reviews
    const total = await prisma.toolReview.count({
      where: reviewWhere,
    });

    // Get reviews with tool details
    const reviews = await prisma.toolReview.findMany({
      where: reviewWhere,
      include: {
        tool: {
          include: {
            translations: {
              where: {
                language_code: language,
              },
            },
          },
        },
        votes: {
          select: {
            vote_type: true,
            user_id: true,
          },
        },
      },
      orderBy: {
        created_at: "desc",
      },
      skip,
      take: limit,
    });

    // Get categories for the tools
    const categoryIds = [
      ...new Set(
        reviews.flatMap((review) => review.tool.category_ids)
      ),
    ];

    const categories = await prisma.toolCategory.findMany({
      where: {
        id: { in: categoryIds },
        is_active: true,
      },
      include: {
        translations: {
          where: { language_code: language },
        },
      },
    });

    const categoryMap = new Map(
      categories.map((cat) => [
        cat.id,
        {
          id: cat.id,
          name: cat.name,
          slug: cat.slug,
          super_category: cat.super_category,
          icon_url: cat.icon_url,
          color: cat.color,
          translations: cat.translations,
        },
      ])
    );

    const totalPages = Math.ceil(total / limit);

    const response = {
      items: reviews.map((review) => {
        // Calculate vote counts
        const helpfulVotes = review.votes.filter((v) => v.vote_type === "HELPFUL").length;
        const unhelpfulVotes = review.votes.filter((v) => v.vote_type === "UNHELPFUL").length;
        const userVote = review.votes.find((v) => v.user_id === session.user.id)?.vote_type || null;

        return {
          id: review.id,
          rating: review.rating,
          content: review.content,
          is_published: review.is_published,
          helpful_votes: review.helpful_votes,
          unhelpful_votes: review.unhelpful_votes,
          created_at: review.created_at,
          updated_at: review.updated_at,
          votes: {
            helpful: helpfulVotes,
            unhelpful: unhelpfulVotes,
            total: helpfulVotes - unhelpfulVotes,
            user_vote: userVote,
          },
          tool: {
            id: review.tool.id,
            name: review.tool.name,
            slug: review.tool.slug,
            logo_url: review.tool.logo_url,
            tool_type: review.tool.tool_type,
            pricing_models: review.tool.pricing_models,
            website_url: review.tool.website_url,
            category_ids: review.tool.category_ids,
            tag_ids: review.tool.tag_ids,
            created_at: review.tool.created_at,
            updated_at: review.tool.updated_at,
            translations: review.tool.translations.map((t) => ({
              id: t.id,
              language_code: t.language_code,
              name: t.name,
              short_description: t.short_description,
            })),
            categories: review.tool.category_ids
              .map((catId) => categoryMap.get(catId))
              .filter(Boolean),
          },
        };
      }),
      pagination: {
        page,
        limit,
        total,
        total_pages: totalPages,
        has_next: page < totalPages,
        has_prev: page > 1,
      },
      user: {
        id: user.id,
        name: user.name,
        image: user.image,
      },
    };

    return successResponse(response, "User reviews retrieved successfully");
  } catch (error) {
    console.error("GET /api/user/[id]/reviews error:", error);
    return serverErrorResponse("Failed to retrieve user reviews");
  }
}
