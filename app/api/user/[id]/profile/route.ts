import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { auth } from "@/auth";
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
  forbiddenResponse,
  notFoundResponse,
  validationErrorResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { generateUserSlug, ensureUniqueUserSlug } from "@/lib/slug-utils";

// GET /api/user/[id]/profile - Get user profile
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return unauthorizedResponse("Authentication required");
    }

    const { id } = await params;

    // Users can only access their own profile, or admins can access any profile
    if (session.user.id !== id && session.user.role !== "ADMIN") {
      return forbiddenResponse("Access denied");
    }

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        slug: true,
        about: true,
        role: true,
        emailVerified: true,
        isVerified: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            ToolReview: true,
            ToolSave: true,
            BlogPost: true,
          },
        },
      },
    });

    if (!user) {
      return notFoundResponse("User not found");
    }

    return successResponse(
      {
        id: user.id,
        name: user.name,
        email: user.email,
        image: user.image,
        slug: user.slug,
        about: user.about,
        role: user.role,
        emailVerified: user.emailVerified,
        isVerified: user.isVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        stats: {
          reviewsCount: user._count.ToolReview,
          savedToolsCount: user._count.ToolSave,
          blogPostsCount: user._count.BlogPost,
        },
      },
      "User profile retrieved successfully"
    );
  } catch (error) {
    console.error("GET /api/user/[id]/profile error:", error);
    return serverErrorResponse("Failed to retrieve user profile");
  }
}

// PATCH /api/user/[id]/profile - Update user profile
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return unauthorizedResponse("Authentication required");
    }

    const { id } = await params;

    // Users can only update their own profile
    if (session.user.id !== id) {
      return forbiddenResponse("Access denied");
    }

    const body = await request.json();
    const { name, image, about } = body;

    // Validation
    if (name !== undefined) {
      if (typeof name !== "string" || name.trim().length === 0) {
        return validationErrorResponse("Name is required and must be a string");
      }
      if (name.length > 100) {
        return validationErrorResponse("Name must be 100 characters or less");
      }
    }

    if (image !== undefined && image !== null) {
      if (typeof image !== "string") {
        return validationErrorResponse("Image must be a string URL");
      }
      // Basic URL validation
      try {
        new URL(image);
      } catch {
        return validationErrorResponse("Image must be a valid URL");
      }
    }

    if (about !== undefined && about !== null) {
      if (typeof about !== "string") {
        return validationErrorResponse("About must be a string");
      }
      if (about.length > 500) {
        return validationErrorResponse("About must be 500 characters or less");
      }
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
      select: { id: true, name: true, slug: true },
    });

    if (!existingUser) {
      return notFoundResponse("User not found");
    }

    // Prepare update data
    const updateData: any = {};

    if (name !== undefined && name !== existingUser.name) {
      updateData.name = name.trim();
      
      // Generate new slug if name changed
      const baseSlug = generateUserSlug(name);
      const existingUsers = await prisma.user.findMany({
        where: { id: { not: id } },
        select: { slug: true },
      });
      const existingSlugs = existingUsers.map((u) => u.slug);
      updateData.slug = ensureUniqueUserSlug(baseSlug, existingSlugs);
    }

    if (image !== undefined) {
      updateData.image = image;
    }

    if (about !== undefined) {
      updateData.about = about;
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        slug: true,
        about: true,
        role: true,
        emailVerified: true,
        isVerified: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return successResponse(updatedUser, "User profile updated successfully");
  } catch (error) {
    console.error("PATCH /api/user/[id]/profile error:", error);
    return serverErrorResponse("Failed to update user profile");
  }
}

// DELETE /api/user/[id]/profile - Delete user account
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return unauthorizedResponse("Authentication required");
    }

    const { id } = await params;

    // Users can only delete their own account, or admins can delete any account
    if (session.user.id !== id && session.user.role !== "ADMIN") {
      return forbiddenResponse("Access denied");
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
      select: { id: true, email: true },
    });

    if (!existingUser) {
      return notFoundResponse("User not found");
    }

    // Delete user (cascade will handle related records)
    await prisma.user.delete({
      where: { id },
    });

    return successResponse(
      { id, email: existingUser.email },
      "User account deleted successfully"
    );
  } catch (error) {
    console.error("DELETE /api/user/[id]/profile error:", error);
    return serverErrorResponse("Failed to delete user account");
  }
}
