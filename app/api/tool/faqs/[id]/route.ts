import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  successResponse,
  errorResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { UpdateToolFAQRequest } from "@/types/tool-api";
import { translateQuestionAndAnswer } from "@/lib/openai-translation";

// GET /api/tool/faqs/[id] - Get a specific FAQ
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const language = searchParams.get("language") || "en";
    const { id } = await params;

    const faq = await prisma.toolFAQ.findUnique({
      where: { id },
      include: {
        translations: {
          where: {
            language_code: language,
          },
        },
      },
    });

    if (!faq) {
      return errorResponse("FAQ not found", 404);
    }

    return successResponse(faq, "FAQ retrieved successfully");
  } catch (error) {
    console.error("Error fetching FAQ:", error);
    return serverErrorResponse("Failed to fetch FAQ");
  }
}

// PUT /api/tool/faqs/[id] - Update a FAQ
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = (await request.json()) as UpdateToolFAQRequest;

    // Check if FAQ exists
    const existingFAQ = await prisma.toolFAQ.findUnique({
      where: { id },
      include: {
        translations: {
          where: { language_code: "en" },
        },
      },
    });

    if (!existingFAQ) {
      return errorResponse("FAQ not found", 404);
    }

    // Update FAQ in transaction
    const faq = await prisma.$transaction(async (tx) => {
      // Update the main FAQ record
      const updatedFAQ = await tx.toolFAQ.update({
        where: { id },
        data: {
          ...(body.order !== undefined && { order: body.order }),
          ...(body.is_active !== undefined && { is_active: body.is_active }),
        },
      });

      // Update English translation if question or answer changed
      if (body.question || body.answer) {
        const englishTranslation = existingFAQ.translations[0];
        if (englishTranslation) {
          await tx.toolFAQTranslation.update({
            where: { id: englishTranslation.id },
            data: {
              ...(body.question && { question: body.question.trim() }),
              ...(body.answer && { answer: body.answer.trim() }),
            },
          });

          // If content changed, regenerate translations for all languages
          if (body.question || body.answer) {
            // Get updated English content
            const updatedEnglishTranslation =
              await tx.toolFAQTranslation.findUnique({
                where: { id: englishTranslation.id },
              });

            if (updatedEnglishTranslation) {
              // Generate new translations for all languages
              const translations = await translateQuestionAndAnswer(
                updatedEnglishTranslation.question,
                updatedEnglishTranslation.answer,
                "This is a FAQ for a software/tool directory website"
              );

              // Update translations for all languages
              const translationPromises = Object.entries(
                translations.question
              ).map(([langCode, translatedQuestion]) =>
                tx.toolFAQTranslation.upsert({
                  where: {
                    faq_id_language_code: {
                      faq_id: id,
                      language_code: langCode,
                    },
                  },
                  update: {
                    question: translatedQuestion,
                    answer: translations.answer[langCode],
                  },
                  create: {
                    faq_id: id,
                    language_code: langCode,
                    question: translatedQuestion,
                    answer: translations.answer[langCode],
                  },
                })
              );

              await Promise.all(translationPromises);
            }
          }
        }
      }

      // Return updated FAQ with translations
      return await tx.toolFAQ.findUnique({
        where: { id },
        include: {
          translations: true,
        },
      });
    });

    return successResponse(faq, "FAQ updated successfully");
  } catch (error) {
    console.error("Error updating FAQ:", error);
    return serverErrorResponse("Failed to update FAQ");
  }
}

// DELETE /api/tool/faqs/[id] - Delete a FAQ
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Check if FAQ exists
    const existingFAQ = await prisma.toolFAQ.findUnique({
      where: { id },
    });

    if (!existingFAQ) {
      return errorResponse("FAQ not found", 404);
    }

    // Delete FAQ (translations will be deleted due to cascade)
    await prisma.toolFAQ.delete({
      where: { id },
    });

    return successResponse(null, "FAQ deleted successfully");
  } catch (error) {
    console.error("Error deleting FAQ:", error);
    return serverErrorResponse("Failed to delete FAQ");
  }
}
