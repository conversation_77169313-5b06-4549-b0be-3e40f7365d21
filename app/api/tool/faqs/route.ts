import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  successResponse,
  errorResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { CreateToolFAQRequest, ToolFAQQueryParams } from "@/types/tool-api";
import { translateQuestionAndAnswer } from "@/lib/openai-translation";

// GET /api/tool/faqs - Get FAQs for a tool
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const toolId = searchParams.get("tool_id");
    const language = searchParams.get("language") || "en";
    const isActive = searchParams.get("is_active");

    if (!toolId) {
      return errorResponse("tool_id parameter is required", 400);
    }

    // Build where clause
    const where: any = {
      tool_id: toolId,
    };

    if (isActive !== null) {
      where.is_active = isActive === "true";
    }

    // Get FAQs with translations
    const faqs = await prisma.toolFAQ.findMany({
      where,
      include: {
        translations: {
          where: {
            language_code: language,
          },
        },
      },
      orderBy: {
        order: "asc",
      },
    });

    const formattedFAQs = faqs.map((faq) => ({
      id: faq.id,
      tool_id: faq.tool_id,
      order: faq.order,
      is_active: faq.is_active,
      created_at: faq.created_at,
      updated_at: faq.updated_at,
      translations: faq.translations,
    }));

    return successResponse(formattedFAQs, "FAQs retrieved successfully");
  } catch (error) {
    console.error("Error fetching FAQs:", error);
    return serverErrorResponse("Failed to fetch FAQs");
  }
}

// POST /api/tool/faqs - Create a new FAQ
export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as CreateToolFAQRequest;

    // Validation
    if (!body.tool_id || !body.question || !body.answer) {
      return errorResponse("tool_id, question, and answer are required", 400);
    }

    // Check if tool exists
    const tool = await prisma.tool.findUnique({
      where: { id: body.tool_id },
    });

    if (!tool) {
      return errorResponse("Tool not found", 404);
    }

    // Check FAQ limit (10 per tool)
    const faqCount = await prisma.toolFAQ.count({
      where: { tool_id: body.tool_id },
    });

    if (faqCount >= 10) {
      return errorResponse("Maximum of 10 FAQs allowed per tool", 400);
    }

    // Get the next order if not provided
    const maxOrder = await prisma.toolFAQ.findFirst({
      where: { tool_id: body.tool_id },
      orderBy: { order: "desc" },
      select: { order: true },
    });

    const order = body.order ?? (maxOrder?.order ?? 0) + 1;

    // Create FAQ with translation in transaction
    const faq = await prisma.$transaction(async (tx) => {
      // Create the FAQ
      const newFAQ = await tx.toolFAQ.create({
        data: {
          tool_id: body.tool_id,
          order,
          is_active: body.is_active ?? true,
        },
      });

      // Generate translations for all supported languages
      const translations = await translateQuestionAndAnswer(
        body.question.trim(),
        body.answer.trim(),
        "This is a FAQ for a software/tool directory website"
      );

      // Create translations for all supported languages
      const translationPromises = Object.entries(translations.question).map(
        ([langCode, translatedQuestion]) =>
          tx.toolFAQTranslation.create({
            data: {
              faq_id: newFAQ.id,
              language_code: langCode,
              question: translatedQuestion,
              answer: translations.answer[langCode],
            },
          })
      );

      await Promise.all(translationPromises);

      // Return FAQ with translations
      return await tx.toolFAQ.findUnique({
        where: { id: newFAQ.id },
        include: {
          translations: true,
        },
      });
    });

    return successResponse(faq, "FAQ created successfully", 201);
  } catch (error) {
    console.error("Error creating FAQ:", error);
    return serverErrorResponse("Failed to create FAQ");
  }
}
