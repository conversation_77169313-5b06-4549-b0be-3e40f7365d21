import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdminAuth } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  notFoundResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { translateToolToLanguage } from "@/lib/openai-translation";
import { locales } from "@/constant/locale";

// POST /api/tool/[id]/translate - Translate tool to specified language
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication
    await requireAdminAuth();

    // Await params before using
    const { id } = await params;

    const { language } = await request.json();

    if (!language) {
      return errorResponse("Language is required");
    }

    // Validate language
    const validLocales = locales.map((l) => l.locale);
    if (!validLocales.includes(language)) {
      return errorResponse("Invalid language code");
    }

    // Check if tool exists
    const tool = await prisma.tool.findUnique({
      where: { id },
      include: {
        translations: {
          where: { language_code: "en" },
        },
      },
    });

    if (!tool) {
      return notFoundResponse("Tool not found");
    }

    // Check if translation already exists
    const existingTranslation = await prisma.toolTranslation.findFirst({
      where: {
        tool_id: id,
        language_code: language,
      },
    });

    if (existingTranslation) {
      return errorResponse("Translation already exists for this language");
    }

    // Get English translation content
    const englishTranslation = tool.translations[0];
    if (!englishTranslation) {
      return errorResponse("English translation not found");
    }

    // Create translation using AI
    await translateToolToLanguage(id, {
      name: englishTranslation.name,
      short_description: englishTranslation.short_description,
      introduction: englishTranslation.introduction,
      what_is_it: englishTranslation.what_is_it,
      how_to_use: englishTranslation.how_to_use,
      full_feature: englishTranslation.full_feature,
      short_feature: englishTranslation.short_feature,
      pricing: englishTranslation.pricing,
      meta_title: englishTranslation.meta_title,
      meta_description: englishTranslation.meta_description,
    }, language);

    // Get the created translation
    const newTranslation = await prisma.toolTranslation.findFirst({
      where: {
        tool_id: id,
        language_code: language,
      },
    });

    return successResponse(
      {
        id: newTranslation?.id,
        language_code: language,
        tool_id: id,
      },
      `Translation to ${language} created successfully`
    );
  } catch (error) {
    console.error("Tool translation error:", error);
    return serverErrorResponse("Failed to translate tool");
  }
}

// GET /api/tool/[id]/translate - Get available languages for translation
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication
    await requireAdminAuth();

    // Await params before using
    const { id } = await params;

    // Get existing translations for this tool
    const existingTranslations = await prisma.toolTranslation.findMany({
      where: { tool_id: id },
      select: { language_code: true },
    });

    const existingLanguages = existingTranslations.map(
      (t) => t.language_code
    );

    // Get available languages for translation (excluding existing ones)
    const availableLanguages = locales
      .filter((locale) => !existingLanguages.includes(locale.locale))
      .map((locale) => ({
        code: locale.locale,
        name: locale.name,
      }));

    return successResponse(
      {
        available_languages: availableLanguages,
        existing_languages: existingLanguages,
      },
      "Available languages retrieved successfully"
    );
  } catch (error) {
    console.error("Get available languages error:", error);
    return serverErrorResponse("Failed to get available languages");
  }
} 