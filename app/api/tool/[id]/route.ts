import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdminAuth } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  validationErrorResponse,
  serverErrorResponse,
  notFoundResponse,
} from "@/lib/api-responses";
import { generateSlug, validateSlug, ensureUniqueSlug } from "@/lib/slug-utils";
import {
  translateAndCreateToolTranslation,
  createFAQTranslations,
} from "@/lib/openai-translation";
import {
  UpdateToolRequest,
  ToolResponse,
  ToolType,
  PricingModel,
  ToolStatus,
} from "@/types/tool-api";

// GET /api/tool/[id] - Get single tool
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const language = searchParams.get("language") || "en";

    const tool = await prisma.tool.findUnique({
      where: { id },
      include: {
        translations: {
          where: { language_code: language },
        },
        faqs: {
          where: { is_active: true },
          include: {
            translations: {
              where: { language_code: language },
            },
          },
          orderBy: { order: "asc" },
        },
      },
    });

    if (!tool) {
      return notFoundResponse("Tool not found");
    }

    // Get saved count for this tool
    const savedCount = await prisma.toolSave.count({
      where: { tool_id: id },
    });

    const response: ToolResponse = {
      id: tool.id,
      name: tool.name,
      slug: tool.slug,
      website_url: tool.website_url,
      logo_url: tool.logo_url,
      screenshot_urls: tool.screenshot_urls,
      tool_type: tool.tool_type as ToolType,
      pricing_models: tool.pricing_models as PricingModel[],
      is_available: tool.is_available,
      added_date: tool.added_date,
      status: tool.status as ToolStatus,
      is_published: tool.is_published,
      category_ids: tool.category_ids,
      tag_ids: tool.tag_ids,
      twitter_url: tool.twitter_url,
      facebook_url: tool.facebook_url,
      linkedin_url: tool.linkedin_url,
      github_url: tool.github_url,
      contact_email: tool.contact_email,
      support_email: tool.support_email,
      translations: tool.translations.map((t) => ({
        id: t.id,
        tool_id: t.tool_id,
        language_code: t.language_code,
        name: t.name,
        short_description: t.short_description,
        introduction: t.introduction,
        what_is_it: t.what_is_it,
        how_to_use: t.how_to_use,
        full_feature: t.full_feature,
        short_feature: t.short_feature,
        pricing: t.pricing,
        meta_title: t.meta_title,
        meta_description: t.meta_description,
        created_at: t.created_at,
        updated_at: t.updated_at,
      })),
      faqs: tool.faqs?.map((faq) => ({
        id: faq.id,
        tool_id: faq.tool_id,
        order: faq.order,
        is_active: faq.is_active,
        created_at: faq.created_at,
        updated_at: faq.updated_at,
        translations: faq.translations.map((ft) => ({
          id: ft.id,
          faq_id: ft.faq_id,
          language_code: ft.language_code,
          question: ft.question,
          answer: ft.answer,
          created_at: ft.created_at,
          updated_at: ft.updated_at,
        })),
      })),
      saved_count: savedCount,
    };

    return successResponse(response, "Tool retrieved successfully");
  } catch (error) {
    console.error("GET /api/tool/[id] error:", error);
    return serverErrorResponse("Failed to retrieve tool");
  }
}

// PUT /api/tool/[id] - Update tool
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAdminAuth();

    const { id } = await params;
    const body: UpdateToolRequest = await request.json();

    // Check if tool exists
    const existingTool = await prisma.tool.findUnique({
      where: { id },
    });

    if (!existingTool) {
      return notFoundResponse("Tool not found");
    }

    // Generate new slug if name is being updated
    let slug = existingTool.slug;
    if (body.name && body.name !== existingTool.name) {
      const newSlug = generateSlug(body.name);

      // Check if new slug conflicts with existing tools (excluding current tool)
      const existingTools = await prisma.tool.findMany({
        where: {
          slug: newSlug,
          id: { not: id },
        },
        select: { slug: true },
      });

      if (existingTools.length > 0) {
        return validationErrorResponse("A tool with this name already exists");
      }

      slug = newSlug;
    }

    // Update tool in transaction
    const tool = await prisma.$transaction(async (tx) => {
      // Update the main tool
      const updatedTool = await tx.tool.update({
        where: { id },
        data: {
          ...(body.name && { name: body.name.trim() }),
          slug,
          ...(body.website_url !== undefined && {
            website_url: body.website_url?.trim() || null,
          }),
          ...(body.logo_url !== undefined && {
            logo_url: body.logo_url?.trim() || null,
          }),
          ...(body.screenshot_urls !== undefined && {
            screenshot_urls: body.screenshot_urls,
          }),
          ...(body.tool_type && { tool_type: body.tool_type }),
          ...(body.pricing_models && { pricing_models: body.pricing_models }),
          ...(body.is_available !== undefined && {
            is_available: body.is_available,
          }),
          ...(body.status && { status: body.status }),
          ...(body.is_published !== undefined && {
            is_published: body.is_published,
          }),
          ...(body.category_ids !== undefined && {
            category_ids: body.category_ids,
          }),
          ...(body.tag_ids !== undefined && { tag_ids: body.tag_ids }),
          ...(body.twitter_url !== undefined && {
            twitter_url: body.twitter_url?.trim() || null,
          }),
          ...(body.facebook_url !== undefined && {
            facebook_url: body.facebook_url?.trim() || null,
          }),
          ...(body.linkedin_url !== undefined && {
            linkedin_url: body.linkedin_url?.trim() || null,
          }),
          ...(body.github_url !== undefined && {
            github_url: body.github_url?.trim() || null,
          }),
          ...(body.contact_email !== undefined && {
            contact_email: body.contact_email?.trim() || null,
          }),
          ...(body.support_email !== undefined && {
            support_email: body.support_email?.trim() || null,
          }),
        },
      });

      // Handle translation fields
      let translations = [];
      const hasTranslationFields = [
        "name",
        "short_description",
        "introduction",
        "what_is_it",
        "how_to_use",
        "full_feature",
        "short_feature",
        "pricing",
        "meta_title",
        "meta_description",
      ].some((field) => (body as any)[field] !== undefined);

      if (hasTranslationFields) {
        const translationData = {
          ...(body.name && { name: body.name.trim() }),
          ...(body.short_description !== undefined && {
            short_description: body.short_description?.trim() || null,
          }),
          ...(body.introduction !== undefined && {
            introduction: body.introduction?.trim() || null,
          }),
          ...(body.what_is_it !== undefined && {
            what_is_it: body.what_is_it?.trim() || null,
          }),
          ...(body.how_to_use !== undefined && {
            how_to_use: body.how_to_use?.trim() || null,
          }),
          ...(body.full_feature !== undefined && {
            full_feature: body.full_feature?.trim() || null,
          }),
          ...(body.short_feature !== undefined && {
            short_feature: body.short_feature?.trim() || null,
          }),
          ...(body.pricing !== undefined && {
            pricing: body.pricing?.trim() || null,
          }),
          ...(body.meta_title !== undefined && {
            meta_title: body.meta_title?.trim() || null,
          }),
          ...(body.meta_description !== undefined && {
            meta_description: body.meta_description?.trim() || null,
          }),
        };

        const englishTranslation = await tx.toolTranslation.upsert({
          where: {
            tool_id_language_code: {
              tool_id: id,
              language_code: "en",
            },
          },
          update: translationData,
          create: {
            tool_id: id,
            language_code: "en",
            name: body.name?.trim() || existingTool.name,
            ...translationData,
          },
        });

        translations = [englishTranslation];
      } else {
        // Fetch existing translations if no translation fields updated
        translations = await tx.toolTranslation.findMany({
          where: { tool_id: id, language_code: "en" },
        });
      }

      // Handle FAQs if provided (this should run regardless of translation updates)
      if (body.faqs !== undefined) {
        // Delete existing FAQs first
        await tx.toolFAQ.deleteMany({
          where: { tool_id: id },
        });

        // Create new FAQs
        if (body.faqs.length > 0) {
          const faqPromises = body.faqs.map(async (faq, index) => {
            const newFAQ = await tx.toolFAQ.create({
              data: {
                tool_id: id,
                order: faq.order ?? index + 1,
                is_active: faq.is_active ?? true,
              },
            });

            // Create English translation
            await tx.toolFAQTranslation.create({
              data: {
                faq_id: newFAQ.id,
                language_code: "en",
                question: faq.question.trim(),
                answer: faq.answer.trim(),
              },
            });

            return newFAQ;
          });

          await Promise.all(faqPromises);
        }
      }

      return { ...updatedTool, translations };
    });

    // AI translations are now handled separately through the translation dialog

    // Fetch updated FAQs for the response
    const updatedFAQs = await prisma.toolFAQ.findMany({
      where: { tool_id: id, is_active: true },
      include: {
        translations: {
          where: { language_code: "en" },
        },
      },
      orderBy: { order: "asc" },
    });

    // Get saved count for this tool
    const savedCount = await prisma.toolSave.count({
      where: { tool_id: id },
    });

    // Format response
    const response: ToolResponse = {
      id: tool.id,
      name: tool.name,
      slug: tool.slug,
      website_url: tool.website_url,
      logo_url: tool.logo_url,
      screenshot_urls: tool.screenshot_urls,
      tool_type: tool.tool_type as ToolType,
      pricing_models: tool.pricing_models as PricingModel[],
      is_available: tool.is_available,
      added_date: tool.added_date,
      status: tool.status as ToolStatus,
      is_published: tool.is_published,
      category_ids: tool.category_ids,
      tag_ids: tool.tag_ids,
      twitter_url: tool.twitter_url,
      facebook_url: tool.facebook_url,
      linkedin_url: tool.linkedin_url,
      github_url: tool.github_url,
      contact_email: tool.contact_email,
      support_email: tool.support_email,
      translations: tool.translations.map((t) => ({
        id: t.id,
        tool_id: t.tool_id,
        language_code: t.language_code,
        name: t.name,
        short_description: t.short_description,
        introduction: t.introduction,
        what_is_it: t.what_is_it,
        how_to_use: t.how_to_use,
        full_feature: t.full_feature,
        short_feature: t.short_feature,
        pricing: t.pricing,
        meta_title: t.meta_title,
        meta_description: t.meta_description,
        created_at: t.created_at,
        updated_at: t.updated_at,
      })),
      faqs: updatedFAQs.map((faq) => ({
        id: faq.id,
        tool_id: faq.tool_id,
        order: faq.order,
        is_active: faq.is_active,
        created_at: faq.created_at,
        updated_at: faq.updated_at,
        translations: faq.translations.map((ft) => ({
          id: ft.id,
          faq_id: ft.faq_id,
          language_code: ft.language_code,
          question: ft.question,
          answer: ft.answer,
          created_at: ft.created_at,
          updated_at: ft.updated_at,
        })),
      })),
      saved_count: savedCount,
    };

    return successResponse(response, "Tool updated successfully");
  } catch (error) {
    console.error("PUT /api/tool/[id] error:", error);
    return serverErrorResponse("Failed to update tool");
  }
}

// DELETE /api/tool/[id] - Delete tool
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requireAdminAuth();

    const { id } = await params;

    // Check if tool exists
    const existingTool = await prisma.tool.findUnique({
      where: { id },
    });

    if (!existingTool) {
      return notFoundResponse("Tool not found");
    }

    // Delete tool and all related data (cascade handled by Prisma schema)
    await prisma.tool.delete({
      where: { id },
    });

    return successResponse({ id }, "Tool deleted successfully");
  } catch (error) {
    console.error("DELETE /api/tool/[id] error:", error);
    return serverErrorResponse("Failed to delete tool");
  }
}
