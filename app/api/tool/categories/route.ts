import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdminAuth } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
  forbiddenResponse,
  validationErrorResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import {
  translateNameAndDescription,
  translateToAllLanguages,
} from "@/lib/openai-translation";
import { generateSlug, validateSlug, ensureUniqueSlug } from "@/lib/slug-utils";
import type {
  CreateCategoryRequest,
  CategoryResponse,
  ListQueryParams,
  PaginatedResponse,
} from "@/types/tool-api";
import { SuperCategory } from "@/types/tool-api";

// GET /api/tool/categories - List categories (public endpoint)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(parseInt(searchParams.get("limit") || "20"), 100);
    const search = searchParams.get("search") || "";
    const isActive = searchParams.get("is_active");
    const language = searchParams.get("language") || "en";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (isActive !== null) {
      where.is_active = isActive === "true";
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { slug: { contains: search, mode: "insensitive" } },
        {
          translations: {
            some: {
              OR: [
                { name: { contains: search, mode: "insensitive" } },
                { description: { contains: search, mode: "insensitive" } },
              ],
            },
          },
        },
      ];
    }

    // Get total count
    const total = await prisma.toolCategory.count({ where });

    // Get categories with translations
    const categories = await prisma.toolCategory.findMany({
      where,
      include: {
        translations: {
          where: {
            language_code: language,
          },
        },
      },
      orderBy: {
        created_at: "desc",
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    const response: PaginatedResponse<CategoryResponse> = {
      items: categories.map((category) => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        super_category: category.super_category as SuperCategory,
        icon_url: category.icon_url,
        color: category.color,
        is_active: category.is_active,
        created_at: category.created_at,
        updated_at: category.updated_at,
        translations: category.translations.map((t) => ({
          id: t.id,
          category_id: t.category_id,
          language_code: t.language_code,
          name: t.name,
          description: t.description,
          created_at: t.created_at,
          updated_at: t.updated_at,
        })),
      })),
      pagination: {
        page,
        limit,
        total,
        total_pages: totalPages,
        has_next: page < totalPages,
        has_prev: page > 1,
      },
    };

    return successResponse(response, "Categories retrieved successfully");
  } catch (error) {
    console.error("GET /api/tool/categories error:", error);
    return serverErrorResponse("Failed to retrieve categories");
  }
}

// POST /api/tool/categories - Create category (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const authResult = await requireAdminAuth();

    const body: CreateCategoryRequest = await request.json();
    const {
      name,
      description,
      slug,
      super_category,
      icon_url,
      color,
      is_active = true,
    } = body;

    // Validation
    if (!name || name.trim().length === 0) {
      return validationErrorResponse("Category name is required");
    }

    if (name.length > 100) {
      return validationErrorResponse(
        "Category name must be 100 characters or less"
      );
    }

    if (!super_category) {
      return validationErrorResponse("Super category is required");
    }

    if (!Object.values(SuperCategory).includes(super_category)) {
      return validationErrorResponse("Invalid super category");
    }

    // Generate or validate slug
    let finalSlug = slug || generateSlug(name);

    if (slug && !validateSlug(slug)) {
      return validationErrorResponse(
        "Invalid slug format. Use only lowercase letters, numbers, and hyphens."
      );
    }

    // Check if slug already exists
    const existingCategories = await prisma.toolCategory.findMany({
      select: { slug: true },
    });

    const existingSlugs = existingCategories.map((c) => c.slug);
    finalSlug = ensureUniqueSlug(finalSlug, existingSlugs);

    // Validate color if provided
    if (color && !/^#[0-9A-Fa-f]{6}$/.test(color)) {
      return validationErrorResponse(
        "Color must be a valid hex code (e.g., #FF5733)"
      );
    }

    // Generate translations for all supported languages
    const translations = await translateNameAndDescription(
      name,
      description,
      "This is a tool category name and description for a software/tool directory website"
    );

    // Create category with translations in a transaction
    const category = await prisma.$transaction(async (tx) => {
      // Create the category
      const newCategory = await tx.toolCategory.create({
        data: {
          name: name.trim(),
          slug: finalSlug,
          super_category,
          icon_url,
          color,
          is_active,
        },
      });

      // Create translations for all supported languages
      const translationPromises = Object.entries(translations.name).map(
        ([langCode, translatedName]) =>
          tx.toolCategoryTranslation.create({
            data: {
              category_id: newCategory.id,
              language_code: langCode,
              name: translatedName,
              description: translations.description?.[langCode] || null,
            },
          })
      );

      await Promise.all(translationPromises);

      // Return category with translations
      return await tx.toolCategory.findUnique({
        where: { id: newCategory.id },
        include: {
          translations: true,
        },
      });
    });

    if (!category) {
      return serverErrorResponse("Failed to create category");
    }

    const response: CategoryResponse = {
      id: category.id,
      name: category.name,
      slug: category.slug,
      super_category: category.super_category as SuperCategory,
      icon_url: category.icon_url,
      color: category.color,
      is_active: category.is_active,
      translations: category.translations.map((t) => ({
        id: t.id,
        category_id: t.category_id,
        language_code: t.language_code,
        name: t.name,
        description: t.description,
        created_at: t.created_at,
        updated_at: t.updated_at,
      })),
    };

    return successResponse(response, "Category created successfully", 201);
  } catch (error) {
    console.error("POST /api/tool/categories error:", error);

    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return unauthorizedResponse();
      }
      if (error.message === "Admin access required") {
        return forbiddenResponse();
      }
    }

    return serverErrorResponse("Failed to create category");
  }
}
