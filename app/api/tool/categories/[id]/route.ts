import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdminAuth } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
  forbiddenResponse,
  validationErrorResponse,
  serverErrorResponse,
  notFoundResponse,
} from "@/lib/api-responses";
import {
  translateNameAndDescription,
  translateToAllLanguages,
} from "@/lib/openai-translation";
import { generateSlug, validateSlug, ensureUniqueSlug } from "@/lib/slug-utils";
import type { UpdateCategoryRequest, CategoryResponse } from "@/types/tool-api";
import { SuperCategory } from "@/types/tool-api";

// GET /api/tool/categories/[id] - Get category by ID (public endpoint)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const language = searchParams.get("language") || "en";

    const { id } = await params;
    const categoryId = parseInt(id);

    if (isNaN(categoryId)) {
      return validationErrorResponse("Invalid category ID");
    }

    const category = await prisma.toolCategory.findUnique({
      where: { id: categoryId },
      include: {
        translations: {
          where: {
            language_code: language,
          },
        },
      },
    });

    if (!category) {
      return notFoundResponse("Category");
    }

    const response: CategoryResponse = {
      id: category.id,
      name: category.name,
      slug: category.slug,
      super_category: category.super_category as SuperCategory,
      icon_url: category.icon_url,
      color: category.color,
      is_active: category.is_active,
      translations: category.translations.map((t) => ({
        id: t.id,
        category_id: t.category_id,
        language_code: t.language_code,
        name: t.name,
        description: t.description,
      })),
    };

    return successResponse(response, "Category retrieved successfully");
  } catch (error) {
    console.error("GET /api/tool/categories/[id] error:", error);
    return serverErrorResponse("Failed to retrieve category");
  }
}

// PUT /api/tool/categories/[id] - Update category (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication
    const authResult = await requireAdminAuth();

    const { id } = await params;
    const categoryId = parseInt(id);

    if (isNaN(categoryId)) {
      return validationErrorResponse("Invalid category ID");
    }

    const body: UpdateCategoryRequest = await request.json();
    const {
      name,
      description,
      slug,
      super_category,
      icon_url,
      color,
      is_active,
    } = body;

    // Check if category exists
    const existingCategory = await prisma.toolCategory.findUnique({
      where: { id: categoryId },
      include: {
        translations: true,
      },
    });

    if (!existingCategory) {
      return notFoundResponse("Category");
    }

    // Validation
    if (name !== undefined) {
      if (!name || name.trim().length === 0) {
        return validationErrorResponse("Category name cannot be empty");
      }
      if (name.length > 100) {
        return validationErrorResponse(
          "Category name must be 100 characters or less"
        );
      }
    }

    // Handle slug validation and uniqueness
    let finalSlug = slug;
    if (name && !slug) {
      // If name is being updated but slug is not provided, generate new slug
      finalSlug = generateSlug(name);
    }

    if (finalSlug) {
      if (!validateSlug(finalSlug)) {
        return validationErrorResponse(
          "Invalid slug format. Use only lowercase letters, numbers, and hyphens."
        );
      }

      // Check if slug already exists (excluding current category)
      const existingCategories = await prisma.toolCategory.findMany({
        where: {
          slug: finalSlug,
          NOT: { id: categoryId },
        },
        select: { slug: true },
      });

      if (existingCategories.length > 0) {
        return validationErrorResponse("Slug already exists");
      }
    }

    // Validate super_category if provided
    if (
      super_category !== undefined &&
      !Object.values(SuperCategory).includes(super_category)
    ) {
      return validationErrorResponse("Invalid super category");
    }

    // Validate color if provided
    if (
      color !== undefined &&
      color !== null &&
      !/^#[0-9A-Fa-f]{6}$/.test(color)
    ) {
      return validationErrorResponse(
        "Color must be a valid hex code (e.g., #FF5733)"
      );
    }

    // Prepare update data
    const updateData: any = {};
    if (name !== undefined) updateData.name = name.trim();
    if (finalSlug !== undefined) updateData.slug = finalSlug;
    if (super_category !== undefined)
      updateData.super_category = super_category;
    if (icon_url !== undefined) updateData.icon_url = icon_url;
    if (color !== undefined) updateData.color = color;
    if (is_active !== undefined) updateData.is_active = is_active;

    // Update category and translations in a transaction
    const category = await prisma.$transaction(async (tx) => {
      // Update the category
      const updatedCategory = await tx.toolCategory.update({
        where: { id: categoryId },
        data: updateData,
      });

      // If name or description is being updated, update translations
      if (name !== undefined || description !== undefined) {
        // Use existing name if name is not being updated
        const nameToTranslate =
          name !== undefined ? name : existingCategory.name;
        const descriptionToTranslate =
          description !== undefined ? description : null;

        // Generate translations for all supported languages
        const translations = await translateNameAndDescription(
          nameToTranslate,
          descriptionToTranslate || undefined,
          "This is a tool category name and description for a software/tool directory website"
        );

        // Update or create translations for all supported languages
        const translationPromises = Object.entries(translations.name).map(
          ([langCode, translatedName]) =>
            tx.toolCategoryTranslation.upsert({
              where: {
                category_id_language_code: {
                  category_id: categoryId,
                  language_code: langCode,
                },
              },
              update: {
                name: translatedName,
                description: translations.description?.[langCode] || null,
              },
              create: {
                category_id: categoryId,
                language_code: langCode,
                name: translatedName,
                description: translations.description?.[langCode] || null,
              },
            })
        );

        await Promise.all(translationPromises);
      }

      // Return category with translations
      return await tx.toolCategory.findUnique({
        where: { id: categoryId },
        include: {
          translations: true,
        },
      });
    });

    if (!category) {
      return serverErrorResponse("Failed to update category");
    }

    const response: CategoryResponse = {
      id: category.id,
      name: category.name,
      slug: category.slug,
      super_category: category.super_category as SuperCategory,
      icon_url: category.icon_url,
      color: category.color,
      is_active: category.is_active,
      translations: category.translations.map((t) => ({
        id: t.id,
        category_id: t.category_id,
        language_code: t.language_code,
        name: t.name,
        description: t.description,
        created_at: t.created_at,
        updated_at: t.updated_at,
      })),
    };

    return successResponse(response, "Category updated successfully");
  } catch (error) {
    console.error("PUT /api/tool/categories/[id] error:", error);

    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return unauthorizedResponse();
      }
      if (error.message === "Admin access required") {
        return forbiddenResponse();
      }
    }

    return serverErrorResponse("Failed to update category");
  }
}

// DELETE /api/tool/categories/[id] - Delete category (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication
    const authResult = await requireAdminAuth();

    const { id } = await params;
    const categoryId = parseInt(id);

    if (isNaN(categoryId)) {
      return validationErrorResponse("Invalid category ID");
    }

    // Check if category exists
    const existingCategory = await prisma.toolCategory.findUnique({
      where: { id: categoryId },
    });

    if (!existingCategory) {
      return notFoundResponse("Category");
    }

    // Check if category is being used by any tools
    const toolsUsingCategory = await prisma.tool.findFirst({
      where: {
        category_ids: {
          has: categoryId,
        },
      },
    });

    if (toolsUsingCategory) {
      return validationErrorResponse(
        "Cannot delete category that is being used by tools. Please remove the category from all tools first."
      );
    }

    // Delete category and its translations in a transaction
    await prisma.$transaction(async (tx) => {
      // Delete translations first (due to foreign key constraint)
      await tx.toolCategoryTranslation.deleteMany({
        where: { category_id: categoryId },
      });

      // Delete the category
      await tx.toolCategory.delete({
        where: { id: categoryId },
      });
    });

    return successResponse({ id: categoryId }, "Category deleted successfully");
  } catch (error) {
    console.error("DELETE /api/tool/categories/[id] error:", error);

    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return unauthorizedResponse();
      }
      if (error.message === "Admin access required") {
        return forbiddenResponse();
      }
    }

    return serverErrorResponse("Failed to delete category");
  }
}
