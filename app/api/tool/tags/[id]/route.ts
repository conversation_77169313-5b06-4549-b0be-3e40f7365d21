import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdminAuth } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
  forbiddenResponse,
  validationErrorResponse,
  serverErrorResponse,
  notFoundResponse,
} from "@/lib/api-responses";
import {
  translateNameAndDescription,
  translateToAllLanguages,
} from "@/lib/openai-translation";
import { generateSlug, validateSlug, ensureUniqueSlug } from "@/lib/slug-utils";
import type { UpdateTagRequest, TagResponse } from "@/types/tool-api";

// GET /api/tool/tags/[id] - Get tag by ID (public endpoint)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const language = searchParams.get("language") || "en";

    const { id } = await params;
    const tagId = parseInt(id);

    if (isNaN(tagId)) {
      return validationErrorResponse("Invalid tag ID");
    }

    const tag = await prisma.toolTag.findUnique({
      where: { id: tagId },
      include: {
        translations: {
          where: {
            language_code: language,
          },
        },
      },
    });

    if (!tag) {
      return notFoundResponse("Tag");
    }

    const response: TagResponse = {
      id: tag.id,
      name: tag.name,
      slug: tag.slug,
      color: tag.color,
      is_active: tag.is_active,
      translations: tag.translations.map((t) => ({
        id: t.id,
        tag_id: t.tag_id,
        language_code: t.language_code,
        name: t.name,
        description: t.description,
      })),
    };

    return successResponse(response, "Tag retrieved successfully");
  } catch (error) {
    console.error("GET /api/tool/tags/[id] error:", error);
    return serverErrorResponse("Failed to retrieve tag");
  }
}

// PUT /api/tool/tags/[id] - Update tag (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication
    const authResult = await requireAdminAuth();

    const { id } = await params;
    const tagId = parseInt(id);

    if (isNaN(tagId)) {
      return validationErrorResponse("Invalid tag ID");
    }

    const body: UpdateTagRequest = await request.json();
    const { name, description, slug, color, is_active } = body;

    // Check if tag exists
    const existingTag = await prisma.toolTag.findUnique({
      where: { id: tagId },
      include: {
        translations: true,
      },
    });

    if (!existingTag) {
      return notFoundResponse("Tag");
    }

    // Validation
    if (name !== undefined) {
      if (!name || name.trim().length === 0) {
        return validationErrorResponse("Tag name cannot be empty");
      }
      if (name.length > 100) {
        return validationErrorResponse(
          "Tag name must be 100 characters or less"
        );
      }
    }

    // Handle slug validation and uniqueness
    let finalSlug = slug;
    if (name && !slug) {
      // If name is being updated but slug is not provided, generate new slug
      finalSlug = generateSlug(name);
    }

    if (finalSlug) {
      if (!validateSlug(finalSlug)) {
        return validationErrorResponse(
          "Invalid slug format. Use only lowercase letters, numbers, and hyphens."
        );
      }

      // Check if slug already exists (excluding current tag)
      const existingTags = await prisma.toolTag.findMany({
        where: {
          slug: finalSlug,
          NOT: { id: tagId },
        },
        select: { slug: true },
      });

      if (existingTags.length > 0) {
        return validationErrorResponse("Slug already exists");
      }
    }

    // Validate color if provided
    if (
      color !== undefined &&
      color !== null &&
      !/^#[0-9A-Fa-f]{6}$/.test(color)
    ) {
      return validationErrorResponse(
        "Color must be a valid hex code (e.g., #FF5733)"
      );
    }

    // Prepare update data
    const updateData: any = {};
    if (name !== undefined) updateData.name = name.trim();
    if (finalSlug !== undefined) updateData.slug = finalSlug;
    if (color !== undefined) updateData.color = color;
    if (is_active !== undefined) updateData.is_active = is_active;

    // Update tag and translations in a transaction
    const tag = await prisma.$transaction(async (tx) => {
      // Update the tag
      const updatedTag = await tx.toolTag.update({
        where: { id: tagId },
        data: updateData,
      });

      // If name or description is being updated, update translations
      if (name !== undefined || description !== undefined) {
        // Use existing name if name is not being updated
        const nameToTranslate = name !== undefined ? name : existingTag.name;
        const descriptionToTranslate =
          description !== undefined ? description : null;

        // Generate translations for all supported languages
        const translations = await translateNameAndDescription(
          nameToTranslate,
          descriptionToTranslate || undefined,
          "This is a tool tag name and description for a software/tool directory website"
        );

        // Update or create translations for all supported languages
        const translationPromises = Object.entries(translations.name).map(
          ([langCode, translatedName]) =>
            tx.toolTagTranslation.upsert({
              where: {
                tag_id_language_code: {
                  tag_id: tagId,
                  language_code: langCode,
                },
              },
              update: {
                name: translatedName,
                description: translations.description?.[langCode] || null,
              },
              create: {
                tag_id: tagId,
                language_code: langCode,
                name: translatedName,
                description: translations.description?.[langCode] || null,
              },
            })
        );

        await Promise.all(translationPromises);
      }

      // Return tag with translations
      return await tx.toolTag.findUnique({
        where: { id: tagId },
        include: {
          translations: true,
        },
      });
    });

    if (!tag) {
      return serverErrorResponse("Failed to update tag");
    }

    const response: TagResponse = {
      id: tag.id,
      name: tag.name,
      slug: tag.slug,
      color: tag.color,
      is_active: tag.is_active,
      translations: tag.translations.map((t) => ({
        id: t.id,
        tag_id: t.tag_id,
        language_code: t.language_code,
        name: t.name,
        description: t.description,
      })),
    };

    return successResponse(response, "Tag updated successfully");
  } catch (error) {
    console.error("PUT /api/tool/tags/[id] error:", error);

    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return unauthorizedResponse();
      }
      if (error.message === "Admin access required") {
        return forbiddenResponse();
      }
    }

    return serverErrorResponse("Failed to update tag");
  }
}

// DELETE /api/tool/tags/[id] - Delete tag (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authentication
    const authResult = await requireAdminAuth();

    const { id } = await params;
    const tagId = parseInt(id);

    if (isNaN(tagId)) {
      return validationErrorResponse("Invalid tag ID");
    }

    // Check if tag exists
    const existingTag = await prisma.toolTag.findUnique({
      where: { id: tagId },
    });

    if (!existingTag) {
      return notFoundResponse("Tag");
    }

    // Check if tag is being used by any tools
    const toolsUsingTag = await prisma.tool.findFirst({
      where: {
        tag_ids: {
          has: tagId,
        },
      },
    });

    if (toolsUsingTag) {
      return validationErrorResponse(
        "Cannot delete tag that is being used by tools. Please remove the tag from all tools first."
      );
    }

    // Delete tag and its translations in a transaction
    await prisma.$transaction(async (tx) => {
      // Delete translations first (due to foreign key constraint)
      await tx.toolTagTranslation.deleteMany({
        where: { tag_id: tagId },
      });

      // Delete the tag
      await tx.toolTag.delete({
        where: { id: tagId },
      });
    });

    return successResponse({ id: tagId }, "Tag deleted successfully");
  } catch (error) {
    console.error("DELETE /api/tool/tags/[id] error:", error);

    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return unauthorizedResponse();
      }
      if (error.message === "Admin access required") {
        return forbiddenResponse();
      }
    }

    return serverErrorResponse("Failed to delete tag");
  }
}
