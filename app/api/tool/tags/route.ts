import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdminAuth } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
  forbiddenResponse,
  validationErrorResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import {
  translateNameAndDescription,
  translateToAllLanguages,
} from "@/lib/openai-translation";
import { generateSlug, validateSlug, ensureUniqueSlug } from "@/lib/slug-utils";
import type {
  CreateTagRequest,
  TagResponse,
  ListQueryParams,
  PaginatedResponse,
} from "@/types/tool-api";

// GET /api/tool/tags - List tags (public endpoint)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(parseInt(searchParams.get("limit") || "20"), 100);
    const search = searchParams.get("search") || "";
    const isActive = searchParams.get("is_active");
    const language = searchParams.get("language") || "en";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (isActive !== null) {
      where.is_active = isActive === "true";
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { slug: { contains: search, mode: "insensitive" } },
        {
          translations: {
            some: {
              OR: [
                { name: { contains: search, mode: "insensitive" } },
                { description: { contains: search, mode: "insensitive" } },
              ],
            },
          },
        },
      ];
    }

    // Get total count
    const total = await prisma.toolTag.count({ where });

    // Get tags with translations
    const tags = await prisma.toolTag.findMany({
      where,
      include: {
        translations: {
          where: {
            language_code: language,
          },
        },
      },
      orderBy: {
        created_at: "desc",
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    const response: PaginatedResponse<TagResponse> = {
      items: tags.map((tag) => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
        color: tag.color,
        is_active: tag.is_active,
        created_at: tag.created_at,
        updated_at: tag.updated_at,
        translations: tag.translations.map((t) => ({
          id: t.id,
          tag_id: t.tag_id,
          language_code: t.language_code,
          name: t.name,
          description: t.description,
          created_at: t.created_at,
          updated_at: t.updated_at,
        })),
      })),
      pagination: {
        page,
        limit,
        total,
        total_pages: totalPages,
        has_next: page < totalPages,
        has_prev: page > 1,
      },
    };

    return successResponse(response, "Tags retrieved successfully");
  } catch (error) {
    console.error("GET /api/tool/tags error:", error);
    return serverErrorResponse("Failed to retrieve tags");
  }
}

// POST /api/tool/tags - Create tag (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const authResult = await requireAdminAuth();

    const body: CreateTagRequest = await request.json();
    const { name, description, slug, color, is_active = true } = body;

    // Validation
    if (!name || name.trim().length === 0) {
      return validationErrorResponse("Tag name is required");
    }

    if (name.length > 100) {
      return validationErrorResponse("Tag name must be 100 characters or less");
    }

    // Generate or validate slug
    let finalSlug = slug || generateSlug(name);

    if (slug && !validateSlug(slug)) {
      return validationErrorResponse(
        "Invalid slug format. Use only lowercase letters, numbers, and hyphens."
      );
    }

    // Check if slug already exists
    const existingTags = await prisma.toolTag.findMany({
      select: { slug: true },
    });

    const existingSlugs = existingTags.map((t) => t.slug);
    finalSlug = ensureUniqueSlug(finalSlug, existingSlugs);

    // Validate color if provided
    if (color && !/^#[0-9A-Fa-f]{6}$/.test(color)) {
      return validationErrorResponse(
        "Color must be a valid hex code (e.g., #FF5733)"
      );
    }

    // Generate translations for all supported languages
    const translations = await translateNameAndDescription(
      name,
      description,
      "This is a tool tag name and description for a software/tool directory website"
    );

    // Create tag with translations in a transaction
    const tag = await prisma.$transaction(async (tx) => {
      // Create the tag
      const newTag = await tx.toolTag.create({
        data: {
          name: name.trim(),
          slug: finalSlug,
          color,
          is_active,
        },
      });

      // Create translations for all supported languages
      const translationPromises = Object.entries(translations.name).map(
        ([langCode, translatedName]) =>
          tx.toolTagTranslation.create({
            data: {
              tag_id: newTag.id,
              language_code: langCode,
              name: translatedName,
              description: translations.description?.[langCode] || null,
            },
          })
      );

      await Promise.all(translationPromises);

      // Return tag with translations
      return await tx.toolTag.findUnique({
        where: { id: newTag.id },
        include: {
          translations: true,
        },
      });
    });

    if (!tag) {
      return serverErrorResponse("Failed to create tag");
    }

    const response: TagResponse = {
      id: tag.id,
      name: tag.name,
      slug: tag.slug,
      color: tag.color,
      is_active: tag.is_active,
      translations: tag.translations.map((t) => ({
        id: t.id,
        tag_id: t.tag_id,
        language_code: t.language_code,
        name: t.name,
        description: t.description,
        created_at: t.created_at,
        updated_at: t.updated_at,
      })),
    };

    return successResponse(response, "Tag created successfully", 201);
  } catch (error) {
    console.error("POST /api/tool/tags error:", error);

    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return unauthorizedResponse();
      }
      if (error.message === "Admin access required") {
        return forbiddenResponse();
      }
    }

    return serverErrorResponse("Failed to create tag");
  }
}
