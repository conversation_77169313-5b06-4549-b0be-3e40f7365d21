import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { successResponse, serverErrorResponse } from "@/lib/api-responses";
import { SuperCategory } from "@/types/tool-api";

// GET /api/tool/config - Get all tool configuration data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const language = searchParams.get("language") || "en";

    // Get all active categories with translations, organized by super category
    const categories = await prisma.toolCategory.findMany({
      where: { is_active: true },
      include: {
        translations: {
          where: { language_code: language },
        },
      },
      orderBy: [{ super_category: "asc" }, { name: "asc" }],
    });

    // Get all active tags with translations
    const tags = await prisma.toolTag.findMany({
      where: { is_active: true },
      include: {
        translations: {
          where: { language_code: language },
        },
      },
      orderBy: { name: "asc" },
    });

    // Organize categories by super category
    const categoriesBySuper = Object.values(SuperCategory).reduce(
      (acc, superCategory) => {
        acc[superCategory] = categories
          .filter((cat) => cat.super_category === superCategory)
          .map((cat) => ({
            id: cat.id,
            name: cat.translations[0]?.name || cat.name,
            slug: cat.slug,
            super_category: cat.super_category,
            icon_url: cat.icon_url,
            color: cat.color,
            description: cat.translations[0]?.description,
          }));
        return acc;
      },
      {} as Record<SuperCategory, any[]>
    );

    // Format tags
    const formattedTags = tags.map((tag) => ({
      id: tag.id,
      name: tag.translations[0]?.name || tag.name,
      slug: tag.slug,
      color: tag.color,
      description: tag.translations[0]?.description,
    }));

    // Get enum values for dropdowns
    const { ToolType, PricingModel, ToolStatus } = await import(
      "@/types/tool-api"
    );
    const toolTypes = Object.values(ToolType);
    const pricingModels = Object.values(PricingModel);
    const toolStatuses = Object.values(ToolStatus);
    const superCategories = Object.values(SuperCategory);

    const response = {
      categories: categoriesBySuper,
      tags: formattedTags,
      enums: {
        toolTypes,
        pricingModels,
        toolStatuses,
        superCategories,
      },
      stats: {
        totalCategories: categories.length,
        totalTags: tags.length,
        categoriesBySuper: Object.entries(categoriesBySuper).reduce(
          (acc, [key, value]) => {
            acc[key] = value.length;
            return acc;
          },
          {} as Record<string, number>
        ),
      },
    };

    return successResponse(
      response,
      "Tool configuration retrieved successfully"
    );
  } catch (error) {
    console.error("GET /api/tool/config error:", error);
    return serverErrorResponse("Failed to retrieve tool configuration");
  }
}
