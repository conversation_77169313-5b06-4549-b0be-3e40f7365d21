import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdminAuth } from "@/lib/api-auth";
import { 
  ReviewResponse, 
  UpdateReviewRequest,
  AdminReviewApprovalRequest
} from "@/types/tool-api";

// GET - Get single review (Admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdminAuth();

    const { id } = await params;
    const reviewId = id;

    if (!reviewId) {
      return NextResponse.json(
        { error: "Review ID is required" },
        { status: 400 }
      );
    }

    const review = await prisma.toolReview.findUnique({
      where: { id: reviewId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        tool: {
          select: {
            id: true,
            name: true,
            slug: true,
            logo_url: true,
          },
        },
      },
    });

    if (!review) {
      return NextResponse.json({ error: "Review not found" }, { status: 404 });
    }

    const response: ReviewResponse = {
      id: review.id,
      tool_id: review.tool_id,
      user_id: review.user_id,
      rating: review.rating,
      content: review.content,
      is_published: review.is_published,
      helpful_votes: review.helpful_votes,
      unhelpful_votes: review.unhelpful_votes,
      created_at: review.created_at,
      updated_at: review.updated_at,
      user: review.user
        ? {
            id: review.user.id,
            name: review.user.name,
            email: review.user.email,
            image: review.user.image,
          }
        : undefined,
      tool: review.tool
        ? {
            id: review.tool.id,
            name: review.tool.name,
            slug: review.tool.slug,
            logo_url: review.tool.logo_url,
          }
        : undefined,
    };

    return NextResponse.json({
      success: true,
      data: response,
    });
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }
      if (error.message === "Admin access required") {
        return NextResponse.json(
          { error: "Admin access required" },
          { status: 403 }
        );
      }
    }

    console.error("Error fetching review:", error);
    return NextResponse.json(
      { error: "Failed to fetch review" },
      { status: 500 }
    );
  }
}

// PUT - Update review (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdminAuth();

    const { id } = await params;
    const reviewId = id;
    const body = await request.json();
    const updateData = body as UpdateReviewRequest & AdminReviewApprovalRequest;

    if (!reviewId) {
      return NextResponse.json(
        { error: "Review ID is required" },
        { status: 400 }
      );
    }

    const existingReview = await prisma.toolReview.findUnique({
      where: { id: reviewId },
    });

    if (!existingReview) {
      return NextResponse.json({ error: "Review not found" }, { status: 404 });
    }

    // Build update object with only provided fields
    const updateObject: any = {};

    if (updateData.rating !== undefined) {
      if (updateData.rating < 1 || updateData.rating > 5) {
        return NextResponse.json(
          { error: "Rating must be between 1 and 5" },
          { status: 400 }
        );
      }
      updateObject.rating = updateData.rating;
    }

    if (updateData.content !== undefined) {
      updateObject.content = updateData.content;
    }

    if (updateData.is_published !== undefined) {
      updateObject.is_published = updateData.is_published;
    }

    const updatedReview = await prisma.toolReview.update({
      where: { id: reviewId },
      data: updateObject,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        tool: {
          select: {
            id: true,
            name: true,
            slug: true,
            logo_url: true,
          },
        },
      },
    });

    const response: ReviewResponse = {
      id: updatedReview.id,
      tool_id: updatedReview.tool_id,
      user_id: updatedReview.user_id,
      rating: updatedReview.rating,
      content: updatedReview.content,
      is_published: updatedReview.is_published,
      helpful_votes: updatedReview.helpful_votes,
      unhelpful_votes: updatedReview.unhelpful_votes,
      created_at: updatedReview.created_at,
      updated_at: updatedReview.updated_at,
      user: updatedReview.user
        ? {
            id: updatedReview.user.id,
            name: updatedReview.user.name,
            email: updatedReview.user.email,
            image: updatedReview.user.image,
          }
        : undefined,
      tool: updatedReview.tool
        ? {
            id: updatedReview.tool.id,
            name: updatedReview.tool.name,
            slug: updatedReview.tool.slug,
            logo_url: updatedReview.tool.logo_url,
          }
        : undefined,
    };

    return NextResponse.json({
      success: true,
      message: "Review updated successfully",
      data: response,
    });
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }
      if (error.message === "Admin access required") {
        return NextResponse.json(
          { error: "Admin access required" },
          { status: 403 }
        );
      }
    }

    console.error("Error updating review:", error);
    return NextResponse.json(
      { error: "Failed to update review" },
      { status: 500 }
    );
  }
}

// DELETE - Delete single review (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdminAuth();

    const { id } = await params;
    const reviewId = id;

    if (!reviewId) {
      return NextResponse.json(
        { error: "Review ID is required" },
        { status: 400 }
      );
    }

    const existingReview = await prisma.toolReview.findUnique({
      where: { id: reviewId },
    });

    if (!existingReview) {
      return NextResponse.json({ error: "Review not found" }, { status: 404 });
    }

    await prisma.toolReview.delete({
      where: { id: reviewId },
    });

    return NextResponse.json({
      success: true,
      message: "Review deleted successfully",
      data: { id: reviewId },
    });
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === "Authentication required") {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }
      if (error.message === "Admin access required") {
        return NextResponse.json(
          { error: "Admin access required" },
          { status: 403 }
        );
      }
    }

    console.error("Error deleting review:", error);
    return NextResponse.json(
      { error: "Failed to delete review" },
      { status: 500 }
    );
  }
} 