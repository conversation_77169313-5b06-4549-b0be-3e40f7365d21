import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdminAuth } from "@/lib/api-auth";
import { 
  ReviewResponse, 
  ReviewQueryParams, 
  AdminReviewApprovalRequest,
  PaginatedResponse,
  ReviewStatsResponse
} from "@/types/tool-api";

// GET - List reviews with filtering and pagination (Admin only)
export async function GET(request: NextRequest) {
  try {
    await requireAdminAuth();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const tool_id = searchParams.get("tool_id");
    const user_id = searchParams.get("user_id");
    const rating = searchParams.get("rating") ? parseInt(searchParams.get("rating")!) : undefined;
    const is_published = searchParams.get("is_published") === "true" ? true : 
                        searchParams.get("is_published") === "false" ? false : undefined;
    const stats = searchParams.get("stats") === "true";

    const skip = (page - 1) * limit;

    // Build where clause for filtering
    const where: any = {};
    
    if (tool_id) {
      where.tool_id = tool_id;
    }
    
    if (user_id) {
      where.user_id = user_id;
    }
    
    if (rating !== undefined) {
      where.rating = rating;
    }
    
    if (is_published !== undefined) {
      where.is_published = is_published;
    }
    
    if (search) {
      where.OR = [
        { user_name: { contains: search, mode: "insensitive" } },
        { title: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
        { tool: { name: { contains: search, mode: "insensitive" } } }
      ];
    }

    // If stats requested, return statistics
    if (stats) {
      const totalReviews = await prisma.toolReview.count();
      const publishedReviews = await prisma.toolReview.count({
        where: { is_published: true }
      });
      const pendingReviews = await prisma.toolReview.count({
        where: { is_published: false }
      });
      
      const ratingStats = await prisma.toolReview.groupBy({
        by: ['rating'],
        _count: {
          rating: true
        }
      });
      
      const avgRating = await prisma.toolReview.aggregate({
        _avg: {
          rating: true
        }
      });

      const ratingDistribution: Record<number, number> = {};
      ratingStats.forEach(stat => {
        ratingDistribution[stat.rating] = stat._count.rating;
      });

      const statsResponse: ReviewStatsResponse = {
        totalReviews,
        averageRating: avgRating._avg.rating || 0,
        ratingDistribution,
        publishedReviews,
        pendingReviews
      };

      return NextResponse.json({
        success: true,
        data: statsResponse
      });
    }

    // Get reviews with pagination
    const [reviews, total] = await Promise.all([
      prisma.toolReview.findMany({
        where,
        skip,
        take: limit,
        orderBy: { created_at: "desc" },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          },
          tool: {
            select: {
              id: true,
              name: true,
              slug: true,
              logo_url: true
            }
          }
        }
      }),
      prisma.toolReview.count({ where })
    ]);

    const response: PaginatedResponse<ReviewResponse> = {
      items: reviews.map(review => ({
        id: review.id,
        tool_id: review.tool_id,
        user_id: review.user_id,
        rating: review.rating,
        content: review.content,
        is_published: review.is_published,
        helpful_votes: review.helpful_votes,
        unhelpful_votes: review.unhelpful_votes,
        created_at: review.created_at,
        updated_at: review.updated_at,
        user: review.user ? {
          id: review.user.id,
          name: review.user.name,
          email: review.user.email,
          image: review.user.image,
        } : undefined,
        tool: review.tool ? {
          id: review.tool.id,
          name: review.tool.name,
          slug: review.tool.slug,
          logo_url: review.tool.logo_url
        } : undefined
      })),
      pagination: {
        page,
        limit,
        total,
        total_pages: Math.ceil(total / limit),
        has_next: page < Math.ceil(total / limit),
        has_prev: page > 1
      }
    };

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error("Error fetching reviews:", error);
    return NextResponse.json(
      { error: "Failed to fetch reviews" },
      { status: 500 }
    );
  }
}

// POST - Approve or reject review (Admin only)
export async function POST(request: NextRequest) {
  try {
    await requireAdminAuth();

    const body = await request.json();
    const { review_id, is_published } = body as { review_id: string; is_published: boolean };

    if (!review_id || typeof is_published !== "boolean") {
      return NextResponse.json(
        { error: "Review ID and is_published status are required" },
        { status: 400 }
      );
    }

    const review = await prisma.toolReview.findUnique({
      where: { id: review_id },
      include: {
        tool: {
          select: {
            id: true,
            name: true,
            slug: true,
            logo_url: true
          }
        }
      }
    });

    if (!review) {
      return NextResponse.json(
        { error: "Review not found" },
        { status: 404 }
      );
    }

    const updatedReview = await prisma.toolReview.update({
      where: { id: review_id },
      data: { is_published },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        tool: {
          select: {
            id: true,
            name: true,
            slug: true,
            logo_url: true
          }
        }
      }
    });

    const response: ReviewResponse = {
      id: updatedReview.id,
      tool_id: updatedReview.tool_id,
      user_id: updatedReview.user_id,
      rating: updatedReview.rating,
      content: updatedReview.content,
      is_published: updatedReview.is_published,
      helpful_votes: updatedReview.helpful_votes,
      unhelpful_votes: updatedReview.unhelpful_votes,
      created_at: updatedReview.created_at,
      updated_at: updatedReview.updated_at,
      user: updatedReview.user ? {
        id: updatedReview.user.id,
        name: updatedReview.user.name,
        email: updatedReview.user.email,
        image: updatedReview.user.image,
      } : undefined,
      tool: updatedReview.tool ? {
        id: updatedReview.tool.id,
        name: updatedReview.tool.name,
        slug: updatedReview.tool.slug,
        logo_url: updatedReview.tool.logo_url
      } : undefined
    };

    return NextResponse.json({
      success: true,
      message: `Review ${is_published ? "approved" : "rejected"} successfully`,
      data: response
    });

  } catch (error) {
    console.error("Error updating review:", error);
    return NextResponse.json(
      { error: "Failed to update review" },
      { status: 500 }
    );
  }
}

// DELETE - Bulk delete reviews (Admin only)
export async function DELETE(request: NextRequest) {
  try {
    await requireAdminAuth();

    const body = await request.json();
    const { review_ids } = body as { review_ids: string[] };

    if (!Array.isArray(review_ids) || review_ids.length === 0) {
      return NextResponse.json(
        { error: "Review IDs array is required" },
        { status: 400 }
      );
    }

    const deleteResult = await prisma.toolReview.deleteMany({
      where: {
        id: {
          in: review_ids
        }
      }
    });

    return NextResponse.json({
      success: true,
      message: `${deleteResult.count} reviews deleted successfully`,
      data: { deleted_count: deleteResult.count }
    });

  } catch (error) {
    console.error("Error deleting reviews:", error);
    return NextResponse.json(
      { error: "Failed to delete reviews" },
      { status: 500 }
    );
  }
} 