import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdminAuth } from "@/lib/api-auth";
import {
  successResponse,
  errorResponse,
  unauthorizedResponse,
  forbiddenResponse,
  validationErrorResponse,
  serverErrorResponse,
} from "@/lib/api-responses";
import { generateSlug, validateSlug, ensureUniqueSlug } from "@/lib/slug-utils";
import { translateAndCreateToolTranslation } from "@/lib/openai-translation";
import {
  CreateToolRequest,
  ToolResponse,
  PaginatedResponse,
  ToolQueryParams,
  ToolType,
  PricingModel,
  ToolStatus,
} from "@/types/tool-api";

// GET /api/tool - List tools with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(parseInt(searchParams.get("limit") || "20"), 100);
    const search = searchParams.get("search") || "";
    const toolType = searchParams.get("tool_type");
    const pricingModel = searchParams.get("pricing_model");
    const status = searchParams.get("status");
    const isPublished = searchParams.get("is_published");
    const categoryId = searchParams.get("category_id");
    const tagId = searchParams.get("tag_id");
    const language = searchParams.get("language") || "en";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { slug: { contains: search, mode: "insensitive" } },
        {
          translations: {
            some: {
              OR: [
                { name: { contains: search, mode: "insensitive" } },
                {
                  short_description: { contains: search, mode: "insensitive" },
                },
              ],
            },
          },
        },
      ];
    }

    if (toolType) {
      where.tool_type = toolType;
    }

    if (pricingModel) {
      where.pricing_models = { has: pricingModel };
    }

    if (status) {
      where.status = status;
    }

    if (isPublished !== null) {
      where.is_published = isPublished === "true";
    }

    if (categoryId) {
      where.category_ids = { has: parseInt(categoryId) };
    }

    if (tagId) {
      where.tag_ids = { has: parseInt(tagId) };
    }

    // Get total count
    const total = await prisma.tool.count({ where });

    // Get tools with translations
    const tools = await prisma.tool.findMany({
      where,
      include: {
        translations: {
          where: {
            language_code: language,
          },
        },
      },
      orderBy: {
        created_at: "desc",
      },
      skip,
      take: limit,
    });

    // Get saved counts for all tools
    const toolIds = tools.map((tool) => tool.id);
    const savedCounts = await prisma.toolSave.groupBy({
      by: ["tool_id"],
      where: {
        tool_id: { in: toolIds },
      },
      _count: {
        tool_id: true,
      },
    });

    // Create a map for quick lookup
    const savedCountMap = new Map(
      savedCounts.map((item) => [item.tool_id, item._count.tool_id])
    );

    const totalPages = Math.ceil(total / limit);

    const response: PaginatedResponse<ToolResponse> = {
      items: tools.map((tool) => ({
        id: tool.id,
        name: tool.name,
        slug: tool.slug,
        website_url: tool.website_url,
        logo_url: tool.logo_url,
        screenshot_urls: tool.screenshot_urls,
        tool_type: tool.tool_type as ToolType,
        pricing_models: tool.pricing_models as PricingModel[],
        is_available: tool.is_available,
        added_date: tool.added_date,
        status: tool.status as ToolStatus,
        is_published: tool.is_published,
        category_ids: tool.category_ids,
        tag_ids: tool.tag_ids,
        twitter_url: tool.twitter_url,
        facebook_url: tool.facebook_url,
        linkedin_url: tool.linkedin_url,
        github_url: tool.github_url,
        contact_email: tool.contact_email,
        support_email: tool.support_email,
        created_at: tool.created_at,
        updated_at: tool.updated_at,
        translations: tool.translations.map((t) => ({
          id: t.id,
          tool_id: t.tool_id,
          language_code: t.language_code,
          name: t.name,
          short_description: t.short_description,
          introduction: t.introduction,
          what_is_it: t.what_is_it,
          how_to_use: t.how_to_use,
          full_feature: t.full_feature,
          short_feature: t.short_feature,
          pricing: t.pricing,
          meta_title: t.meta_title,
          meta_description: t.meta_description,
          created_at: t.created_at,
          updated_at: t.updated_at,
        })),
        saved_count: savedCountMap.get(tool.id) || 0,
      })),
      pagination: {
        page,
        limit,
        total,
        total_pages: totalPages,
        has_next: page < totalPages,
        has_prev: page > 1,
      },
    };

    return successResponse(response, "Tools retrieved successfully");
  } catch (error) {
    console.error("GET /api/tool error:", error);
    return serverErrorResponse("Failed to retrieve tools");
  }
}

// POST /api/tool - Create new tool (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check admin/author authentication
    const authResult = await requireAdminAuth();

    const body: CreateToolRequest = await request.json();
    const {
      name,
      website_url,
      logo_url,
      screenshot_urls,
      tool_type = "SAAS",
      pricing_models = ["FREE"],
      is_available = true,
      status = "DRAFT",
      is_published = false,
      category_ids = [],
      tag_ids = [],
      twitter_url,
      facebook_url,
      linkedin_url,
      github_url,
      contact_email,
      support_email,
      short_description,
      introduction,
      what_is_it,
      how_to_use,
      full_feature,
      short_feature,
      pricing,
      meta_title,
      meta_description,
      faqs = [],
    } = body;

    // Validation
    if (!name || name.trim().length === 0) {
      return validationErrorResponse("Tool name is required");
    }

    if (name.length > 100) {
      return validationErrorResponse(
        "Tool name must be 100 characters or less"
      );
    }

    // Validate enum values
    if (!Object.values(ToolType).includes(tool_type as ToolType)) {
      return validationErrorResponse("Invalid tool type");
    }

    // Validate pricing models
    if (pricing_models && pricing_models.length > 0) {
      for (const model of pricing_models) {
        if (!Object.values(PricingModel).includes(model as PricingModel)) {
          return validationErrorResponse(`Invalid pricing model: ${model}`);
        }
      }
    }

    if (!Object.values(ToolStatus).includes(status as ToolStatus)) {
      return validationErrorResponse("Invalid status");
    }

    // Generate slug
    const slug = generateSlug(name);

    // Check if slug already exists
    const existingTools = await prisma.tool.findMany({
      select: { slug: true },
    });

    const existingSlugs = existingTools.map((t) => t.slug);
    const finalSlug = ensureUniqueSlug(slug, existingSlugs);

    // Create tool with translations in a transaction
    const tool = await prisma.$transaction(async (tx) => {
      // Create the main tool
      const newTool = await tx.tool.create({
        data: {
          name: name.trim(),
          slug: finalSlug,
          website_url: website_url?.trim() || null,
          logo_url: logo_url?.trim() || null,
          screenshot_urls: screenshot_urls || [],
          tool_type,
          pricing_models,
          is_available,
          status,
          is_published,
          category_ids,
          tag_ids,
          twitter_url: twitter_url?.trim() || null,
          facebook_url: facebook_url?.trim() || null,
          linkedin_url: linkedin_url?.trim() || null,
          github_url: github_url?.trim() || null,
          contact_email: contact_email?.trim() || null,
          support_email: support_email?.trim() || null,
        },
      });

      // Create English translation
      const englishTranslation = await tx.toolTranslation.create({
        data: {
          tool_id: newTool.id,
          language_code: "en",
          name: name.trim(),
          short_description: short_description?.trim() || null,
          introduction: introduction?.trim() || null,
          what_is_it: what_is_it?.trim() || null,
          how_to_use: how_to_use?.trim() || null,
          full_feature: full_feature?.trim() || null,
          short_feature: short_feature?.trim() || null,
          pricing: pricing?.trim() || null,
          meta_title: meta_title?.trim() || null,
          meta_description: meta_description?.trim() || null,
        },
      });

      // Create FAQs if provided
      if (faqs && faqs.length > 0 && faqs.length <= 10) {
        const { translateQuestionAndAnswer } = await import(
          "@/lib/openai-translation"
        );

        const faqPromises = faqs.map(async (faq, index) => {
          const newFAQ = await tx.toolFAQ.create({
            data: {
              tool_id: newTool.id,
              order: faq.order ?? index + 1,
              is_active: true,
            },
          });

          // Generate translations for all supported languages
          const translations = await translateQuestionAndAnswer(
            faq.question.trim(),
            faq.answer.trim(),
            "This is a FAQ for a software/tool directory website"
          );

          // Create translations for all supported languages
          const translationPromises = Object.entries(translations.question).map(
            ([langCode, translatedQuestion]) =>
              tx.toolFAQTranslation.create({
                data: {
                  faq_id: newFAQ.id,
                  language_code: langCode,
                  question: translatedQuestion,
                  answer: translations.answer[langCode],
                },
              })
          );

          await Promise.all(translationPromises);

          return newFAQ;
        });

        await Promise.all(faqPromises);
      }

      // Return complete tool with FAQs and translations
      return await tx.tool.findUnique({
        where: { id: newTool.id },
        include: {
          translations: {
            where: { language_code: "en" },
          },
          faqs: {
            where: { is_active: true },
            include: {
              translations: {
                where: { language_code: "en" },
              },
            },
            orderBy: { order: "asc" },
          },
        },
      });
    });

    if (!tool) {
      return serverErrorResponse("Failed to create tool");
    }

    // AI translations are now handled separately through the translation dialog

    // Format response
    const response: ToolResponse = {
      id: tool.id,
      name: tool.name,
      slug: tool.slug,
      website_url: tool.website_url,
      logo_url: tool.logo_url,
      screenshot_urls: tool.screenshot_urls,
      tool_type: tool.tool_type as ToolType,
      pricing_models: tool.pricing_models as PricingModel[],
      is_available: tool.is_available,
      added_date: tool.added_date,
      status: tool.status as ToolStatus,
      is_published: tool.is_published,
      category_ids: tool.category_ids,
      tag_ids: tool.tag_ids,
      twitter_url: tool.twitter_url,
      facebook_url: tool.facebook_url,
      linkedin_url: tool.linkedin_url,
      github_url: tool.github_url,
      contact_email: tool.contact_email,
      support_email: tool.support_email,
      translations: tool.translations.map((t) => ({
        id: t.id,
        tool_id: t.tool_id,
        language_code: t.language_code,
        name: t.name,
        short_description: t.short_description,
        introduction: t.introduction,
        what_is_it: t.what_is_it,
        how_to_use: t.how_to_use,
        full_feature: t.full_feature,
        short_feature: t.short_feature,
        pricing: t.pricing,
        meta_title: t.meta_title,
        meta_description: t.meta_description,
        created_at: t.created_at,
        updated_at: t.updated_at,
      })),
      faqs: tool.faqs?.map((faq) => ({
        id: faq.id,
        tool_id: faq.tool_id,
        order: faq.order,
        is_active: faq.is_active,
        created_at: faq.created_at,
        updated_at: faq.updated_at,
        translations: faq.translations.map((ft) => ({
          id: ft.id,
          faq_id: ft.faq_id,
          language_code: ft.language_code,
          question: ft.question,
          answer: ft.answer,
          created_at: ft.created_at,
          updated_at: ft.updated_at,
        })),
      })),
      saved_count: 0, // New tools start with 0 saves
    };

    return successResponse(response, "Tool created successfully");
  } catch (error) {
    console.error("POST /api/tool error:", error);
    return serverErrorResponse("Failed to create tool");
  }
}
