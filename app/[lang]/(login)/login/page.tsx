import { Suspense } from "react";
import { i18n } from "@/i18n-config";
import Auth from "@/feature/auth/components/auth";
import { getDictionary } from "../../dictionaries";

type Props = {
  params: Promise<{
    lang: string;
  }>;
};

export function generateStaticParams() {
  return i18n.locales.map((locale) => ({ lang: locale }));
}

function AuthWrapper({ dict, lang }: { dict: any; lang: string }) {
  return <Auth dict={dict} lang={lang} />;
}

const LoginPage = async ({ params }: Props) => {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          {dict?.common?.loading || "Loading..."}
        </div>
      }
    >
      <AuthWrapper dict={dict} lang={lang} />
    </Suspense>
  );
};

export default LoginPage;
