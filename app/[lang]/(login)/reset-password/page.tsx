import { Suspense } from "react";
import { i18n } from "@/i18n-config";
import ResetPassword from "@/feature/auth/components/reset-password";
import { getDictionary } from "../../dictionaries";

type Props = {
  params: Promise<{
    lang: string;
  }>;
};

export function generateStaticParams() {
  return i18n.locales.map((locale) => ({ lang: locale }));
}

function ResetPasswordWrapper({ dict, lang }: { dict: any; lang: string }) {
  return <ResetPassword dict={dict} lang={lang} />;
}

const ResetPasswordPage = async ({ params }: Props) => {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      }
    >
      <ResetPasswordWrapper dict={dict} lang={lang} />
    </Suspense>
  );
};

export default ResetPasswordPage;
