import { getDictionary } from "@/app/[lang]/dictionaries";
import { ToolListingPage } from "@/feature/(client)/tool";

interface HomePageProps {
  params: Promise<{
    lang: string;
  }>;
}

const HomePage = async ({ params }: HomePageProps) => {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <ToolListingPage
      lang={lang}
      title="Discover AI Tools"
      description="Explore our curated collection of AI-powered tools to enhance your productivity and creativity."
    />
  );
};

export default HomePage;
