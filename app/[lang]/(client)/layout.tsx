import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { getDictionary } from "../dictionaries";

interface ClientLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    lang: string;
  }>;
}

export default async function ClientLayout({
  children,
  params,
}: ClientLayoutProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <div className="min-h-screen flex flex-col">
      <Header lang={lang} dict={dict} />
      <main className="flex-1">{children}</main>
      <Footer />
    </div>
  );
}
