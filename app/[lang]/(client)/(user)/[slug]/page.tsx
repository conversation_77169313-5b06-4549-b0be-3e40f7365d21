import { getDictionary } from "@/app/[lang]/dictionaries";
import UserProfile from "@/feature/(client)/user/components/user-profile";

interface UserPageProps {
  params: Promise<{
    lang: string;
    slug: string;
  }>;
}

const UserPage = async ({ params }: UserPageProps) => {
  const { lang, slug } = await params;
  const dict = await getDictionary(lang);

  return (
    <div>
      <UserProfile dict={dict} lang={lang} slug={slug} />
    </div>
  );
};

export default UserPage;
