import { getDictionary } from "@/app/[lang]/dictionaries";
import UserProfile from "@/feature/(client)/user/components/user-profile";

interface MyProfilePageProps {
  params: Promise<{
    lang: string;
    slug: string;
  }>;
}

const MyProfilePage = async ({ params }: MyProfilePageProps) => {
  const { lang, slug } = await params;
  const dict = await getDictionary(lang);

  return (
    <div>
        {/* My Profile */}
        Hello
    </div>
  );
};

export default MyProfilePage;
