import { getDictionary } from "@/app/[lang]/dictionaries";

interface UserLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    lang: string;
  }>;
}

export default async function UserLayout({
  children,
  params,
}: UserLayoutProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return <div className="max-w-7xl mx-auto px-4 md:px-0">{children}</div>;
}
