import { getDictionary } from "@/app/[lang]/dictionaries";

interface ToolLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    lang: string;
  }>;
}

export default async function ToolLayout({
  children,
  params,
}: ToolLayoutProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return <div className="max-w-7xl mx-auto px-4 md:px-0">{children}</div>;
}
