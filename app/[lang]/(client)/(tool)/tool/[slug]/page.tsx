import { getDictionary } from "@/app/[lang]/dictionaries";
import ToolView from "@/feature/(client)/tool/components/tool-view";

interface ToolViewPageProps {
  params: Promise<{
    lang: string;
    slug: string;
  }>;
}

const ToolViewPage = async ({ params }: ToolViewPageProps) => {
  const { lang, slug } = await params;
  const dict = await getDictionary(lang);

  return (
    <div>
      <ToolView dict={dict} lang={lang} slug={slug} />
    </div>
  );
};

export default ToolViewPage;
