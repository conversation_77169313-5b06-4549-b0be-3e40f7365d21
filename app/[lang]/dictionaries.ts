// app/[lang]/dictionaries.ts

import "server-only";

const dictionaries: any = {
  en: () =>
    import("@/feature/lang/translation/en.json").then(
      (module) => module.default
    ),
  es: () =>
    import("@/feature/lang/translation/es.json").then(
      (module) => module.default
    ),
};

// export const getDictionary = async (locale: string) => dictionaries[locale]();
export const getDictionary = async (locale: string) => {
  return (dictionaries[locale] || dictionaries["en"])();
};
