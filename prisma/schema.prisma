// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  USER
  ADMIN
  AUTHOR
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  isVerified    Boolean   @default(false)
  image         String?
  password      String?
  role          UserRole  @default(USER)
  slug          String    @unique
  about         String?   @default("")

  // Email rate limiting fields
  verificationEmailCount    Int       @default(0)
  lastVerificationEmailSent DateTime?
  passwordResetCount        Int       @default(0)
  lastPasswordResetSent     DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  accounts    Account[]
  sessions    Session[]
  BlogPost    BlogPost[]
  BlogComment BlogComment[]
  Media       Media[]
  ToolReview  ToolReview[]
  ReviewVote  ReviewVote[]
  ToolSave    ToolSave[]

  @@map("users")
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
  @@map("verificationtokens")
}

model Tool {
  id         String   @id @default(cuid())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  name            String
  slug            String   @unique
  website_url     String?
  logo_url        String?
  screenshot_urls String[] @default([])
  tool_type       ToolType @default(SAAS)

  pricing_models PricingModel[] @default([FREE])
  is_available   Boolean        @default(true)
  added_date     DateTime       @default(now())
  status         ToolStatus     @default(PENDING)
  is_published   Boolean        @default(false)

  category_ids Int[] @default([])
  tag_ids      Int[] @default([])

  twitter_url   String?
  facebook_url  String?
  linkedin_url  String?
  github_url    String?
  contact_email String?
  support_email String?

  translations ToolTranslation[]
  reviews      ToolReview[]
  faqs         ToolFAQ[]
  highlights   ToolHighlight[]
  featured     ToolFeatured[]
  analytics    ToolAnalytics[]
  saves        ToolSave[]

  @@map("tools")
}

model ToolTranslation {
  id         String   @id @default(cuid())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  tool_id       String
  tool          Tool   @relation(fields: [tool_id], references: [id], onDelete: Cascade)
  language_code String

  name              String?
  short_description String?
  introduction      String? @db.Text
  what_is_it        String? @db.Text
  how_to_use        String? @db.Text
  full_feature      String? @db.Text
  short_feature     String? @db.Text
  pricing           String? @db.Text
  meta_title        String?
  meta_description  String?

  @@unique([tool_id, language_code])
  @@index([language_code])
  @@map("tool_translations")
}

model ToolCategory {
  id         Int      @id @default(autoincrement())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  name           String
  slug           String        @unique
  super_category SuperCategory
  icon_url       String?
  color          String?
  is_active      Boolean       @default(true)

  translations ToolCategoryTranslation[]

  @@map("tool_categories")
}

model ToolCategoryTranslation {
  id         Int      @id @default(autoincrement())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  category_id   Int
  category      ToolCategory @relation(fields: [category_id], references: [id], onDelete: Cascade)
  language_code String

  name        String
  description String? @db.Text

  @@unique([category_id, language_code])
  @@index([language_code])
  @@map("tool_category_translations")
}

model ToolTag {
  id         Int      @id @default(autoincrement())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  name      String
  slug      String  @unique
  color     String?
  is_active Boolean @default(true)

  translations ToolTagTranslation[]

  @@map("tool_tags")
}

model ToolTagTranslation {
  id         Int      @id @default(autoincrement())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  tag_id        Int
  tag           ToolTag @relation(fields: [tag_id], references: [id], onDelete: Cascade)
  language_code String

  name        String
  description String?

  @@unique([tag_id, language_code])
  @@index([language_code])
  @@map("tool_tag_translations")
}

model ToolFAQ {
  id         String   @id @default(cuid())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  tool_id String
  tool    Tool   @relation(fields: [tool_id], references: [id], onDelete: Cascade)

  order     Int     @default(0)
  is_active Boolean @default(true)

  translations ToolFAQTranslation[]

  @@map("tool_faqs")
}

model ToolFAQTranslation {
  id         String   @id @default(cuid())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  faq_id        String
  faq           ToolFAQ @relation(fields: [faq_id], references: [id], onDelete: Cascade)
  language_code String

  question String
  answer   String @db.Text

  @@unique([faq_id, language_code])
  @@index([language_code])
  @@map("tool_faq_translations")
}

model ToolReview {
  id         String   @id @default(cuid())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  tool_id String
  tool    Tool   @relation(fields: [tool_id], references: [id], onDelete: Cascade)

  user_id String
  user    User   @relation(fields: [user_id], references: [id], onDelete: Cascade)

  rating  Int
  content String? @db.Text

  is_published Boolean @default(false)

  helpful_votes   Int @default(0)
  unhelpful_votes Int @default(0)

  votes ReviewVote[]

  @@unique([tool_id, user_id])
  @@index([tool_id, is_published])
  @@index([rating])
  @@map("tool_reviews")
}

model ReviewVote {
  id         String   @id @default(cuid())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  review_id String
  review    ToolReview @relation(fields: [review_id], references: [id], onDelete: Cascade)

  user_id String
  user    User   @relation(fields: [user_id], references: [id], onDelete: Cascade)

  vote_type VoteType

  @@unique([review_id, user_id])
  @@index([review_id])
  @@index([user_id])
  @@map("review_votes")
}

enum VoteType {
  HELPFUL
  UNHELPFUL
}

model ToolSave {
  id         String   @id @default(cuid())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  tool_id String
  tool    Tool   @relation(fields: [tool_id], references: [id], onDelete: Cascade)

  user_id String
  user    User   @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([tool_id, user_id])
  @@index([tool_id])
  @@index([user_id])
  @@map("tool_saves")
}

model ToolHighlight {
  id         String   @id @default(cuid())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  tool_id String
  tool    Tool   @relation(fields: [tool_id], references: [id], onDelete: Cascade)

  start_date DateTime
  end_date   DateTime
  amount     Float

  is_active Boolean @default(true)
  is_paid   Boolean @default(false)

  @@index([start_date, end_date, is_active])
  @@map("tool_highlights")
}

model ToolFeatured {
  id         String   @id @default(cuid())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  tool_id String
  tool    Tool   @relation(fields: [tool_id], references: [id], onDelete: Cascade)

  start_date DateTime
  end_date   DateTime
  amount     Float
  position   Int      @default(0)

  is_active Boolean @default(true)
  is_paid   Boolean @default(false)

  @@index([start_date, end_date, is_active])
  @@index([position])
  @@map("tool_featured")
}

model ToolAnalytics {
  id   String   @id @default(cuid())
  date DateTime @default(now())

  tool_id String
  tool    Tool   @relation(fields: [tool_id], references: [id], onDelete: Cascade)

  views  Int @default(0)
  clicks Int @default(0)
  saves  Int @default(0)

  @@unique([tool_id, date])
  @@index([date])
  @@map("tool_analytics")
}

enum ToolType {
  SAAS
  MOBILE_APP
  DESKTOP_APP
  AI_MODEL
  CHROME_EXTENSION
}

enum PricingModel {
  FREE
  FREEMIUM
  PAID
  SUBSCRIPTION
  ONE_TIME
  USAGE_BASED
  CUSTOM
}

enum ToolStatus {
  PENDING
  APPROVED
  REJECTED
  PUBLISHED
  DRAFT
  ARCHIVED
}

enum SubmissionStatus {
  PENDING
  APPROVED
  REJECTED
  NEEDS_INFO
}

enum SuperCategory {
  WRITING
  IMAGE_GENERATION
  AUDIO
  VIDEO_GENERATION
  SOCIAL_MEDIA
}

model BlogPost {
  id         String   @id @default(cuid())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  title   String
  slug    String     @unique
  excerpt String?    @db.Text
  content String     @db.Text
  status  BlogStatus @default(DRAFT)

  meta_title       String?
  meta_description String?

  featured_image_url String?

  author_id String
  author    User   @relation(fields: [author_id], references: [id], onDelete: Cascade)

  category_ids Int[] @default([])
  tag_ids      Int[] @default([])

  published_at DateTime?

  comments BlogComment[]

  @@index([status])
  @@index([published_at])
  @@index([author_id])
  @@map("blog_posts")
}

model BlogCategory {
  id         Int      @id @default(autoincrement())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  name        String
  slug        String  @unique
  description String? @db.Text

  parent_id Int?
  parent    BlogCategory?  @relation("CategoryHierarchy", fields: [parent_id], references: [id])
  children  BlogCategory[] @relation("CategoryHierarchy")

  @@index([parent_id])
  @@map("blog_categories")
}

model BlogTag {
  id         Int      @id @default(autoincrement())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  name        String
  description String? @db.Text
  slug        String  @unique

  @@map("blog_tags")
}

model BlogComment {
  id         String   @id @default(cuid())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  post_id String
  post    BlogPost @relation(fields: [post_id], references: [id], onDelete: Cascade)

  author_id    String?
  author       User?   @relation(fields: [author_id], references: [id], onDelete: SetNull)
  author_name  String
  author_email String
  author_url   String?

  content String            @db.Text
  status  BlogCommentStatus @default(PENDING)

  parent_id String?
  parent    BlogComment?  @relation("CommentThread", fields: [parent_id], references: [id])
  replies   BlogComment[] @relation("CommentThread")

  is_approved Boolean @default(false)

  @@index([post_id])
  @@index([status])
  @@index([parent_id])
  @@map("blog_comments")
}

model Media {
  id         String   @id @default(cuid())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // File information
  file_key      String        @unique
  file_name     String
  original_name String
  file_url      String
  file_size     Int
  mime_type     String
  file_type     MediaFileType

  // WordPress-like metadata fields
  title       String?
  alt_text    String?
  caption     String? @db.Text
  description String? @db.Text

  // File organization
  folder_path String @default("")

  // User who uploaded
  uploaded_by String?
  uploader    User?   @relation(fields: [uploaded_by], references: [id], onDelete: SetNull)

  @@index([file_type])
  @@index([folder_path])
  @@index([uploaded_by])
  @@index([created_at])
  @@map("media")
}

enum MediaFileType {
  image
  video
  audio
  document
  archive
  other
}

enum BlogStatus {
  DRAFT
  PUBLISHED
  SCHEDULED
  ARCHIVED
  PRIVATE
}

enum BlogCommentStatus {
  PENDING
  APPROVED
  SPAM
  TRASH
}
