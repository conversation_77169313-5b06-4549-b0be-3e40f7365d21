"use client"

import * as React from "react"
import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"

import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export function ThemeSwitch() {
  const { setTheme } = useTheme()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme("light")}>
          Light
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          Dark
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
          System
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}


// "use client"

// import * as React from "react"
// import { Moon, Sun } from "lucide-react"
// import { useTheme } from "next-themes"
// import { cn } from "@/lib/utils";

// export function ThemeSwitch() {
//   const { theme, setTheme } = useTheme();
//   const [mounted, setMounted] = React.useState(false);

//   React.useEffect(() => {
//     setMounted(true);
//   }, []);

//   if (!mounted) {
//     return (
//       <div className="relative h-8 w-14 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse" />
//     );
//   }

//   const isDark = theme === "dark";

//   const toggleTheme = () => {
//     setTheme(isDark ? "light" : "dark");
//   };

//   return (
//     <button
//       onClick={toggleTheme}
//       className={cn(
//         "relative h-8 w-14 rounded-full transition-all duration-300 ease-in-out",
//         "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
//         "hover:shadow-lg transform hover:scale-105 active:scale-95",
//         "bg-gradient-to-r",
//         isDark
//           ? "from-slate-700 to-slate-800 shadow-inner"
//           : "from-blue-400 to-blue-500 shadow-md"
//       )}
//       aria-label="Toggle theme"
//     >
//       {/* Background glow effect */}
//       <div
//         className={cn(
//           "absolute inset-0 rounded-full opacity-20 blur-sm transition-all duration-300",
//           isDark ? "bg-purple-400" : "bg-yellow-300"
//         )}
//       />

//       {/* Toggle circle */}
//       <div
//         className={cn(
//           "absolute top-1 h-6 w-6 rounded-full transition-all duration-300 ease-in-out",
//           "shadow-lg backdrop-blur-sm",
//           "flex items-center justify-center",
//           isDark
//             ? "translate-x-7 bg-gradient-to-br from-slate-100 to-slate-200"
//             : "translate-x-1 bg-gradient-to-br from-white to-yellow-50"
//         )}
//       >
//         {/* Icon container with rotation animation */}
//         <div className="relative">
//           <Sun
//             className={cn(
//               "h-3.5 w-3.5 transition-all duration-300 ease-in-out",
//               isDark
//                 ? "rotate-180 scale-0 opacity-0"
//                 : "rotate-0 scale-100 opacity-100 text-yellow-500"
//             )}
//           />
//           <Moon
//             className={cn(
//               "absolute inset-0 h-3.5 w-3.5 transition-all duration-300 ease-in-out",
//               isDark
//                 ? "rotate-0 scale-100 opacity-100 text-slate-600"
//                 : "rotate-180 scale-0 opacity-0"
//             )}
//           />
//         </div>
//       </div>

//       {/* Animated stars for dark mode */}
//       <div className="absolute inset-0 overflow-hidden rounded-full">
//         {[...Array(3)].map((_, i) => (
//           <div
//             key={i}
//             className={cn(
//               "absolute h-1 w-1 bg-white rounded-full transition-all duration-500",
//               isDark ? "opacity-60" : "opacity-0",
//               i === 0 && "top-2 left-2 animate-pulse",
//               i === 1 && "top-3 right-3 animate-pulse delay-150",
//               i === 2 && "bottom-2 left-3 animate-pulse delay-300"
//             )}
//           />
//         ))}
//       </div>

//       {/* Animated sun rays for light mode */}
//       <div className="absolute inset-0 overflow-hidden rounded-full">
//         {[...Array(4)].map((_, i) => (
//           <div
//             key={i}
//             className={cn(
//               "absolute h-0.5 w-0.5 bg-yellow-300 rounded-full transition-all duration-500",
//               !isDark ? "opacity-40" : "opacity-0",
//               i === 0 &&
//                 "top-1 left-1/2 transform -translate-x-1/2 animate-pulse",
//               i === 1 &&
//                 "bottom-1 left-1/2 transform -translate-x-1/2 animate-pulse delay-100",
//               i === 2 &&
//                 "top-1/2 left-1 transform -translate-y-1/2 animate-pulse delay-200",
//               i === 3 &&
//                 "top-1/2 right-1 transform -translate-y-1/2 animate-pulse delay-300"
//             )}
//           />
//         ))}
//       </div>
//     </button>
//   );
// }
