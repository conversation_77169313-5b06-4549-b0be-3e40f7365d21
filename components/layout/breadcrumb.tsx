"use client";

import React from "react";
import { Home, ChevronRight, ChevronLeft } from "lucide-react";
import Link from "next/link";
import { getLocalizedUrl, cn } from "@/lib/utils";

interface BreadcrumbItem {
  icon: string;
  text: string;
  href?: string;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  lang: string;
  dict: any;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items = [], lang, dict }) => {
  const isRTL = lang === "ar";
  const ChevronIcon = isRTL ? ChevronLeft : ChevronRight;

  return (
    <div className="">
      <div className="max-w-7xl custom-gradient mx-auto px-4 md:px-0 py-1.5">
        <nav className="flex" aria-label="Breadcrumb">
          <ol className="inline-flex items-center flex-wrap">
            <li className="inline-flex items-center">
              <Link
                href={getLocalizedUrl(lang, "/")}
                className="group inline-flex items-center text-sm font-medium text-gray-600 hover:text-primary transition-all duration-200"
              >
                <span
                  className={`bg-gray-100 dark:bg-gray-800 group-hover:bg-primary/10 p-1.5 rounded-md transition-all duration-200 ${isRTL ? "ml-2" : "mr-2"}`}
                >
                  <Home className="w-3.5 h-3.5 text-gray-500 dark:text-gray-300 group-hover:text-primary" />
                </span>
                {dict.breadcrumb.home}
              </Link>
            </li>

            {items.map((item, index) => (
              <li key={index} className="flex items-center">
                <ChevronIcon className="w-4 h-4 text-gray-400 mx-2 flex-shrink-0" />
                <div className="max-w-[200px] md:max-w-xs truncate">
                  {item.href ? (
                    <Link
                      href={item.href}
                      className={cn(
                        "text-sm font-medium hover:text-primary transition-all duration-200",
                        index === items.length - 1
                          ? "text-primary font-semibold"
                          : "text-gray-600"
                      )}
                    >
                      {item.text}
                    </Link>
                  ) : (
                    <span
                      className={cn(
                        "text-sm font-medium",
                        index === items.length - 1
                          ? "text-primary font-semibold"
                          : "text-gray-500"
                      )}
                    >
                      {item.text}
                    </span>
                  )}
                </div>
              </li>
            ))}
          </ol>
        </nav>
      </div>
    </div>
  );
};

export default Breadcrumb;
