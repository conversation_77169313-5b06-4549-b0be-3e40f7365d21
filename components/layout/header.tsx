"use client";

import Link from "next/link";
import { <PERSON>u, X } from "lucide-react";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { AuthNav } from "@/feature/auth/components/auth-nav";
import { ThemeSwitch } from "@/components/theme/theme-switch";
import LanguageSwitch from "@/feature/lang/components/language-switch";

interface HeaderProps {
  lang: string;
  dict?: any;
}

export function Header({ lang, dict }: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white dark:bg-black">
      <div className="container mx-auto px-4 md:px-0">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-lg">
                  A
                </span>
              </div>
              <span className="hidden font-bold text-xl sm:inline-block">
                {dict?.header?.app_name || "AppList"}
              </span>
            </Link>
          </div>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-3">
            <LanguageSwitch lang={lang} dict={dict} />
            <ThemeSwitch />
            <Separator orientation="vertical" className="h-6" />
            <AuthNav dict={dict} lang={lang} />
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center space-x-2">
            <LanguageSwitch lang={lang} dict={dict} />
            <ThemeSwitch />
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMobileMenu}
              aria-label={dict?.header?.toggle_menu || "Toggle menu"}
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 border-t">
              <Link
                href="/"
                className="block px-3 py-2 text-base font-medium transition-colors hover:text-primary hover:bg-accent rounded-md"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {dict?.common?.home || "Home"}
              </Link>
              <Separator className="my-2" />
              <div className="px-3 py-2">
                <AuthNav dict={dict} lang={lang} />
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
