"use client";

import * as React from "react";
import {
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
} from "lucide-react";
import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";

import { NavProjects } from "./nav-projects";
import { NavUser } from "./nav-user";
import { TeamSwitcher } from "./team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { NavMain } from "./nav-main";

// Base navigation data structure
const baseNavData = [
  {
    title: "Dashboard",
    url: "/admin",
    icon: <PERSON><PERSON><PERSON>,
    items: undefined,
  },
  {
    title: "Tools",
    url: "#",
    icon: SquareTerminal,
    items: [
      {
        title: "Categories",
        url: "/admin/tools/category",
      },
      {
        title: "Tags",
        url: "/admin/tools/tag",
      },
      {
        title: "All Tools",
        url: "/admin/tools",
      },
      {
        title: "Add New",
        url: "/admin/tools/add",
      },
      {
        title: "Review",
        url: "/admin/tools/review",
      },
      {
        title: "Settings",
        url: "#",
      },
    ],
  },
  {
    title: "Blog",
    url: "#",
    icon: BookOpen,
    items: [
      {
        title: "Add New",
        url: "/admin/blog/new",
      },
      {
        title: "All Posts",
        url: "/admin/blog",
      },
      {
        title: "Categories",
        url: "/admin/blog/category",
      },
      {
        title: "Tags",
        url: "/admin/blog/tag",
      },
    ],
  },
  {
    title: "Media",
    url: "#",
    icon: GalleryVerticalEnd,
    items: [
      {
        title: "Library",
        url: "/admin/media",
      },
    ],
  },
];

// Function to determine navigation state based on current path
function getNavigationData(pathname: string) {
  return baseNavData.map((item) => {
    // For items without subitems, check if current path matches exactly
    if (!item.items) {
      return {
        ...item,
        isActive: pathname === item.url,
      };
    }

    // For items with subitems, check if any subitem matches the current path
    const hasActiveSubItem = item.items.some(
      (subItem) => pathname === subItem.url
    );

    // Also check if we're on a related page (e.g., /admin/tools/edit/[id] should activate Tools section)
    const isInSection = pathname.startsWith(
      `/admin/${item.title.toLowerCase()}`
    );

    return {
      ...item,
      isActive: hasActiveSubItem || isInSection,
      items: item.items.map((subItem) => ({
        ...subItem,
        isActive: pathname === subItem.url,
      })),
    };
  });
}

const projects = [
  {
    name: "Design Engineering",
    url: "#",
    icon: Frame,
  },
];

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session, status } = useSession();
  const pathname = usePathname();

  // Get dynamic navigation data based on current path
  const navMainData = React.useMemo(
    () => getNavigationData(pathname),
    [pathname]
  );

  // Show loading state while session is loading
  if (status === "loading") {
    return (
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader>
          <div className="flex items-center justify-center h-16">
            <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">
                A
              </span>
            </div>
          </div>
        </SidebarHeader>
        <SidebarContent>
          <NavMain items={navMainData} />
        </SidebarContent>
        <SidebarFooter>
          <div className="h-12 bg-muted rounded animate-pulse" />
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
    );
  }

  // Don't render user nav if no session
  if (!session?.user) {
    return (
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader>
          <div className="flex items-center justify-center h-16">
            <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">
                A
              </span>
            </div>
          </div>
        </SidebarHeader>
        <SidebarContent>
          <NavMain items={navMainData} />
        </SidebarContent>
        <SidebarFooter>
          <div className="text-center text-sm text-muted-foreground">
            Not logged in
          </div>
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
    );
  }

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <div className="flex items-center justify-center h-16">
          <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-lg">A</span>
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navMainData} />
        {/* <NavProjects projects={projects} /> */}
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={session.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
