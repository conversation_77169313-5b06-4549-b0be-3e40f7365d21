import { NextResponse } from "next/server";
import { defaultLocale } from "./constant/locale";
import { i18n } from "./i18n-config";
import { NextRequest } from "next/server";
import NextAuth from "next-auth";
import authConfig from "./auth.config";

const { auth } = NextAuth(authConfig);

const nonI18nRoutes = [
  "/admin",
  "/blog",
];

export default auth(async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  if (
    pathname.startsWith("/api/") ||
    pathname.startsWith("/_next/") ||
    // pathname.startsWith("/login") ||
    pathname.includes(".")
  ) {
    return NextResponse.next();
  }

  const isNonI18nRoute = nonI18nRoutes.some(
    (route) => pathname === route || pathname.startsWith(`${route}/`)
  );

  if (isNonI18nRoute) {
    return NextResponse.next();
  }

  const langCookie = req.cookies.get("NEXT_LOCALE");
  let lang = langCookie ? langCookie.value : defaultLocale;

  if (!i18n.locales.includes(lang)) {
    lang = defaultLocale;
  }

  const pathnameIsMissingLocale = i18n.locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  );

  // Handle root path "/"
  if (pathname === "/") {
    if (lang === defaultLocale) {
      return NextResponse.rewrite(new URL(`/${defaultLocale}`, req.url));
    } else {
      return NextResponse.redirect(new URL(`/${lang}`, req.url));
    }
  }

  // Handle default locale path - redirect /en to /
  if (pathname === `/${defaultLocale}`) {
    return NextResponse.redirect(new URL("/", req.url));
  }

  // Handle default locale with subpaths - redirect /en/... to /...
  if (pathname.startsWith(`/${defaultLocale}/`)) {
    const pathWithoutLocale = pathname.slice(`/${defaultLocale}`.length);
    return NextResponse.redirect(new URL(pathWithoutLocale, req.url));
  }

  if (pathnameIsMissingLocale && pathname !== "/") {
    return NextResponse.rewrite(new URL(`/${lang}${pathname}`, req.url));
  }

  return NextResponse.next();
});

export const config = {
  matcher: ["/((?!api|_next|.*\\..*).*)", "/"],
};
